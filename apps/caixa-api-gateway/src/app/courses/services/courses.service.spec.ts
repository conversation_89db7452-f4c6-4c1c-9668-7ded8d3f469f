import { CoursesService } from './courses.service';
import { SmartzapClient } from '../../core';
import { CoursesListParamsDto } from '../dtos/courses-list-params.dto';
import { Chance } from 'chance';
import { CourseDto } from '../dtos/course.dto';
import { HttpClientError, KpCacheService, PageDto } from '@keeps-node-apis/@core';
import { UserAlreadyEnrolledException } from '../../exceptions';

describe('CoursesService', () => {
  let service: CoursesService;
  let smartZapClientMock: jest.Mocked<SmartzapClient>;
  let kpCacheServiceMock: jest.Mocked<KpCacheService>;
  const chance = new Chance();
  const workspaceId = chance.guid();

  beforeEach(async () => {
    smartZapClientMock = {
      post: jest.fn(),
      get: jest.fn(),
    } as unknown as jest.Mocked<SmartzapClient>;
    kpCacheServiceMock = {
      get: jest.fn(),
      set: jest.fn(),
    } as unknown as jest.Mocked<KpCacheService>;

    service = new CoursesService(smartZapClientMock, kpCacheServiceMock);
  });

  it('should fetch courses with the correct parameters', async () => {
    // without cache
    kpCacheServiceMock.get.mockResolvedValue(null);

    const mockCourse = {
      id: chance.guid(),
      name: chance.name(),
      category: { id: chance.guid(), name: chance.name() },
      is_active: true,
      holder_image: chance.url(),
      thumb_image: chance.url(),
      created: '',
      updated: '',
    } as CourseDto;
    const categoryId = chance.guid();
    const params: CoursesListParamsDto = {
      search: 'mockSearch',
      perPage: 10,
      page: 1,
      category_id: categoryId,
    } as CoursesListParamsDto;

    const expectedResponse = new PageDto([mockCourse], params, 1);
    const expectedParams = {
      page: params.page,
      per_page: params.perPage,
      name__ilike: params.search,
      status: 'FINISHED',
      sort: '-created',
      category_id__eq: categoryId,
    };
    const expectedCacheKey = ['caixa-api-gateway-courses', workspaceId, JSON.stringify(expectedParams)];

    smartZapClientMock.get.mockResolvedValueOnce({ result: [mockCourse], count: 1 });

    const response = await service.getCourses(workspaceId, params);

    expect(smartZapClientMock.get).toHaveBeenCalledWith('/courses', workspaceId, expectedParams);
    expect(kpCacheServiceMock.set).toHaveBeenCalledWith(expectedCacheKey, expectedResponse, { ttl: 18000 });
    expect(response).toEqual(expectedResponse);
  });

  it('should use null as the search param when no search value is provided', async () => {
    const params: CoursesListParamsDto = { search: '', perPage: 10, page: 1 } as CoursesListParamsDto;
    const expectedParams = {
      page: params.page,
      per_page: params.perPage,
      name__ilike: null,
      status: 'FINISHED',
      sort: '-created',
    };
    smartZapClientMock.get.mockResolvedValueOnce({ result: [], count: 1 });
    await service.getCourses(workspaceId, params);

    expect(smartZapClientMock.get).toHaveBeenCalledWith('/courses', workspaceId, expectedParams);
  });

  describe('enroll', () => {
    it('should enroll an user into a course', async () => {
      const courseId = chance.guid();
      const userId = chance.guid();
      smartZapClientMock.post.mockResolvedValueOnce({});

      await service.enroll(workspaceId, courseId, { user_id: userId, terms_accept: true });

      expect(smartZapClientMock.post).toHaveBeenCalledWith('/enrollments', workspaceId, {
        course_id: courseId,
        user_id: userId,
      });
    });

    it('should throw a UserAlreadyEnrolledException when a 409 error happens', () => {
      smartZapClientMock.post.mockRejectedValueOnce(new HttpClientError('Conflict', '', 409));
      const courseId = chance.guid();
      const userId = chance.guid();
      const error = new UserAlreadyEnrolledException(
        'The user already has an ongoing enrollment.',
        workspaceId,
        userId,
        courseId,
      );

      expect(async () => {
        await service.enroll(workspaceId, courseId, { user_id: userId, terms_accept: true });
      }).rejects.toThrow(error);
    });

    it('should throw the error if it is not a 409 status code', () => {
      const error = new HttpClientError('Not Found', '', 400);
      smartZapClientMock.post.mockRejectedValueOnce(error);
      const courseId = chance.guid();
      const userId = chance.guid();

      expect(async () => {
        await service.enroll(workspaceId, courseId, { user_id: userId, terms_accept: true });
      }).rejects.toThrow(error);
    });

    it('should a bad request exception if terms_accept is false', () => {
      const courseId = chance.guid();
      const userId = chance.guid();

      expect(async () => {
        await service.enroll(workspaceId, courseId, { user_id: userId, terms_accept: false });
      }).rejects.toThrow('The terms and conditions should be accepted');
    });
  });
});
