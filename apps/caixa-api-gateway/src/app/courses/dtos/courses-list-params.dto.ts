import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsUUID } from 'class-validator';
import { PageOptionsDto } from '@keeps-node-apis/@core';

export class CoursesListParamsDto extends PageOptionsDto {
  @ApiProperty({ required: false, description: 'Search string to filter courses', example: 'Typescript types 101' })
  @IsOptional()
  search?: string;

  @ApiProperty({
    description: 'Category id to filter by',
  })
  @IsOptional()
  @IsUUID('4')
  category_id: string;
}
