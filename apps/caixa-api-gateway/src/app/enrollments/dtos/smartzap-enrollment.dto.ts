import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class SmartZapEnrollmentDto {
  @ApiProperty({
    description: 'Unique identifier of the enrollment',
    example: '8b3f2b7c-1e5a-4f9a-9a2d-4d5e6f7a8b9c',
  })
  @Expose()
  id: string;

  @ApiProperty({
    description: 'Identifier of the course associated with this enrollment',
    example: 'f2a6c7b8-9d0e-4a1b-8c2d-3e4f5a6b7c8d',
  })
  @Expose()
  course_id: string;

  @ApiProperty({
    description: 'Identifier of the user who is enrolled',
    example: '3d2c1b0a-9e8f-7d6c-5b4a-3f2e1d0c9b8a',
  })
  @Expose()
  user_id: string;

  @ApiProperty({
    description: 'Current enrollment status',
    example: 'in_progress',
    enum: ['enrolled', 'in_progress', 'completed', 'cancelled'],
  })
  @Expose()
  status: string;

  @ApiProperty({
    description: 'Course completion progress percentage (0–100)',
    example: 42,
    minimum: 0,
    maximum: 100,
  })
  @Expose()
  progress: number;
}
