import { EnrollmentsController } from './enrollments.controller';
import { EnrollmentsService } from '../services/enrollments.service';
import { Chance } from 'chance';

describe('EnrollmentsController', () => {
  let controller: EnrollmentsController;
  let enrollmentsService: jest.Mocked<EnrollmentsService>;
  const chance = new Chance();

  beforeEach(() => {
    enrollmentsService = { getEnrollments: jest.fn() } as unknown as jest.Mocked<EnrollmentsService>;
    controller = new EnrollmentsController(enrollmentsService);
  });

  it('should fetch enrollments for a user', async () => {
    const workspaceId = chance.guid();
    const userId = chance.guid();

    await controller.getEnrollments(workspaceId, userId);

    expect(enrollmentsService.getEnrollments).toHaveBeenCalledWith(workspaceId, userId);
  });
});
