import { <PERSON>, Get, Head<PERSON>, Param } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { EnrollmentsService } from '../services/enrollments.service';
import { Serialize } from '@keeps-node-apis/@core';
import { SmartZapEnrollmentDto } from '../dtos/smartzap-enrollment.dto';

@ApiTags('Enrollments')
@ApiResponse({ status: 403, description: 'Forbidden.' })
@Controller('enrollments')
export class EnrollmentsController {
  constructor(private readonly enrollmentsService: EnrollmentsService) {}

  @Get(':id')
  @ApiOperation({ summary: 'List enrollments' })
  @ApiResponse({
    status: 200,
    description: 'The list of enrollments.',
    type: [SmartZapEnrollmentDto],
  })
  @Serialize(SmartZapEnrollmentDto)
  getEnrollments(@Headers('x-workspace-id') workspaceId: string, @Param('id') userId: string) {
    return this.enrollmentsService.getEnrollments(workspaceId, userId);
  }
}
