import { EnrollmentsService } from './enrollments.service';
import { SmartzapClient } from '../../core';
import { Chance } from 'chance';
import { SmartZapEnrollmentDto } from '../dtos/smartzap-enrollment.dto';
import { SmartZapResponseDto } from '../../common/dtos';

describe('EnrollmentsService', () => {
  let service: EnrollmentsService;
  let smartZapClient: jest.Mocked<SmartzapClient>;
  const chance = new Chance();

  beforeEach(() => {
    smartZapClient = {
      get: jest.fn(),
    } as unknown as jest.Mocked<SmartzapClient>;

    service = new EnrollmentsService(smartZapClient);
  });

  it('should fetch enrollments for a user', async () => {
    const workspaceId = chance.guid();
    const userId = chance.guid();

    const enrollments: SmartZapEnrollmentDto[] = [
      {
        id: chance.guid(),
        course_id: chance.guid(),
        user_id: userId,
        status: 'STARTED',
        progress: 50,
      },
      {
        id: chance.guid(),
        course_id: chance.guid(),
        user_id: userId,
        status: 'FINISHED',
        progress: 100,
      },
    ];

    const apiResponse = {
      result: enrollments,
      count: enrollments.length,
    } as SmartZapResponseDto<SmartZapEnrollmentDto>;

    smartZapClient.get.mockResolvedValueOnce(apiResponse);

    const result = await service.getEnrollments(workspaceId, userId);

    expect(smartZapClient.get).toHaveBeenCalledWith('/enrollments', workspaceId, { user_id: userId });
    expect(result).toEqual(enrollments);
  });

  it('should return an empty list when API returns no enrollments', async () => {
    const workspaceId = chance.guid();
    const userId = chance.guid();

    const apiResponse = {
      result: [],
      count: 0,
    } as SmartZapResponseDto<SmartZapEnrollmentDto>;

    smartZapClient.get.mockResolvedValueOnce(apiResponse);

    const result = await service.getEnrollments(workspaceId, userId);

    expect(smartZapClient.get).toHaveBeenCalledWith('/enrollments', workspaceId, { user_id: userId });
    expect(result).toEqual([]);
  });

  it('should propagate errors from the api request', async () => {
    const workspaceId = chance.guid();
    const userId = chance.guid();
    const error = new Error('Request failed');

    smartZapClient.get.mockRejectedValueOnce(error);

    await expect(service.getEnrollments(workspaceId, userId)).rejects.toThrow(error);
    expect(smartZapClient.get).toHaveBeenCalledWith('/enrollments', workspaceId, { user_id: userId });
  });
});
