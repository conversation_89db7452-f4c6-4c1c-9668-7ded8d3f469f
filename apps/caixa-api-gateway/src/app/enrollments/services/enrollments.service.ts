import { Injectable } from '@nestjs/common';
import { SmartzapClient } from '../../core';
import { SmartZapEnrollmentDto } from '../dtos/smartzap-enrollment.dto';
import { SmartZapResponseDto } from '../../common/dtos';

@Injectable()
export class EnrollmentsService {
  constructor(private smartZapClient: SmartzapClient) {}

  async getEnrollments(workspaceId: string, userId: string): Promise<SmartZapEnrollmentDto[]> {
    const response = await this.smartZapClient.get<SmartZapResponseDto<SmartZapEnrollmentDto>>(
      '/enrollments',
      workspaceId,
      {
        user_id: userId,
      },
    );

    return response.result;
  }
}
