import { Controller, Get, Headers } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CategoriesService } from './services/categories.service';
import { CategoryDto } from './dtos/category.dto';

@ApiTags('Categories')
@Controller('categories')
@ApiResponse({ status: 403, description: 'Forbidden.' })
export class CategoriesController {
  constructor(private readonly categoriesService: CategoriesService) {}

  @Get()
  @ApiOperation({ summary: 'List categories' })
  @ApiResponse({
    status: 200,
    description: 'The list of categories.',
    type: [CategoryDto],
  })
  getCategories(@Headers('x-workspace-id') workspaceId: string) {
    return this.categoriesService.getCategories(workspaceId);
  }
}
