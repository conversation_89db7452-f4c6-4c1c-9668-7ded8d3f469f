import { CategoriesController } from './categories.controller';
import { CategoriesService } from './services/categories.service';
import { Chance } from 'chance';

describe('CategoriesController', () => {
  let controller: CategoriesController;
  let categoriesService: jest.Mocked<CategoriesService>;
  const chance = new Chance();

  beforeEach(() => {
    categoriesService = { getCategories: jest.fn() } as unknown as jest.Mocked<CategoriesService>;
    controller = new CategoriesController(categoriesService);
  });

  it('should fetch categories', async () => {
    const workspaceId = chance.guid();

    await controller.getCategories(workspaceId);

    expect(categoriesService.getCategories).toHaveBeenCalledWith(workspaceId);
  });
});
