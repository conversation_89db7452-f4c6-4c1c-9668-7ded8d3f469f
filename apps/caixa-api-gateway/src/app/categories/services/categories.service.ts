import { Injectable } from '@nestjs/common';
import { SmartzapClient } from '../../core';
import { KpCacheService } from '@keeps-node-apis/@core';
import { CategoryDto } from '../dtos/category.dto';
import { SmartZapResponseDto } from '../../common/dtos';
import { CaixaCategoryAdapter } from '../caixa-category.adapter';

const CACHE_TTL = 18000; // 30 minutes

@Injectable()
export class CategoriesService {
  constructor(
    private smartZapClient: SmartzapClient,
    private cache: KpCacheService,
  ) {}

  async getCategories(workspaceId: string): Promise<CategoryDto[]> {
    const cacheKey = ['caixa-api-gateway-categories', workspaceId];
    const cachedCategories = await this.cache.get<CategoryDto[]>(cacheKey);

    if (cachedCategories) {
      return cachedCategories;
    }

    try {
      const response = await this.smartZapClient.get<SmartZapResponseDto<CategoryDto>>(
        '/course-categories',
        workspaceId,
        null,
      );
      const categories = CaixaCategoryAdapter.filterAndConvertCategories(response.result);
      await this.cache.set(cacheKey, categories, { ttl: CACHE_TTL });
      return categories;
    } catch (error) {
      console.error(error);
      return [];
    }
  }
}
