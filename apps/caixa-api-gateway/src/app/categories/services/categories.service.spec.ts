import { CategoriesService } from './categories.service';
import { SmartzapClient } from '../../core';
import { KpCacheService } from '@keeps-node-apis/@core';
import { Chance } from 'chance';
import { CategoryDto } from '../dtos/category.dto';

describe('CategoriesService', () => {
  let service: CategoriesService;
  let smartZapClientMock: jest.Mocked<SmartzapClient>;
  let kpCacheServiceMock: jest.Mocked<KpCacheService>;
  const chance = new Chance();
  const workspaceId = chance.guid();

  beforeEach(async () => {
    smartZapClientMock = {
      post: jest.fn(),
      get: jest.fn(),
    } as unknown as jest.Mocked<SmartzapClient>;
    kpCacheServiceMock = {
      get: jest.fn(),
      set: jest.fn(),
    } as unknown as jest.Mocked<KpCacheService>;

    service = new CategoriesService(smartZapClientMock, kpCacheServiceMock);
  });

  it('it should fetch the categories when the cache is not found', async () => {
    kpCacheServiceMock.get.mockResolvedValue(null);
    const caixaProductCategory: CategoryDto = { id: 'e5077e79-1ac8-4f84-a54e-c768eaf6a552', name: 'Vida' };
    const expectedCacheKey = ['caixa-api-gateway-categories', workspaceId];
    smartZapClientMock.get.mockResolvedValueOnce({ result: [caixaProductCategory] });

    const response = await service.getCategories(workspaceId);

    expect(smartZapClientMock.get).toHaveBeenCalledWith('/course-categories', workspaceId, null);
    expect(kpCacheServiceMock.set).toHaveBeenCalledWith(expectedCacheKey, [caixaProductCategory], { ttl: 18000 });
    expect(response).toEqual([caixaProductCategory]);
  });

  it('should use the cached categories when existing', async () => {
    const expectedCacheKey = ['caixa-api-gateway-categories', workspaceId];
    const mockCategory: CategoryDto = { id: chance.guid(), name: chance.name() };
    kpCacheServiceMock.get.mockResolvedValueOnce([mockCategory]);

    const response = await service.getCategories(workspaceId);
    expect(response).toEqual([mockCategory]);
    expect(kpCacheServiceMock.get).toHaveBeenCalledWith(expectedCacheKey);
    expect(smartZapClientMock.get).not.toHaveBeenCalled();
  });
});
