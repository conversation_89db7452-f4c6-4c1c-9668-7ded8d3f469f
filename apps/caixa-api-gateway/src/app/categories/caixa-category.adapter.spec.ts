import { CaixaCategoryAdapter } from './caixa-category.adapter';
import { CategoryDto } from './dtos/category.dto';

describe('CaixaCategoryAdapter', () => {
  describe('toCaixaCategory', () => {
    it('should return category with the updated name when it is a Caixa product category', () => {
      const category: CategoryDto = {
        id: 'e5077e79-1ac8-4f84-a54e-c768eaf6a552',
        name: 'Original Name',
      };

      const result = CaixaCategoryAdapter.toCaixaCategory(category);

      expect(result).toEqual({
        id: 'e5077e79-1ac8-4f84-a54e-c768eaf6a552',
        name: 'Vida',
      });
    });

    it('should return category with original name when the category is not a Caixa product category', () => {
      const category: CategoryDto = {
        id: 'non-existing-id',
        name: 'Original Name',
      };

      const result = CaixaCategoryAdapter.toCaixaCategory(category);

      expect(result).toEqual({
        id: 'non-existing-id',
        name: 'Original Name',
      });
    });

    it('should handle update all Caixa products categories', () => {
      const testCases = [
        { id: 'e5077e79-1ac8-4f84-a54e-c768eaf6a552', expectedName: 'Vida' },
        { id: '7d46e9d8-30cf-42be-99d0-19dd724d2be9', expectedName: 'Acidentes Pessoais' },
        { id: '9c671f59-bb3c-4945-832b-e5e26b65d5cd', expectedName: 'Previdência' },
        { id: '49223560-7afb-46b9-bea9-5b7036ba673f', expectedName: 'Dívida Zero' },
      ];

      testCases.forEach((testCase) => {
        const category: CategoryDto = {
          id: testCase.id,
          name: 'Original Name',
        };

        const result = CaixaCategoryAdapter.toCaixaCategory(category);

        expect(result).toEqual({
          id: testCase.id,
          name: testCase.expectedName,
        });
      });
    });
  });

  describe('filterAndConvertCategories', () => {
    it('should handle falsy cases', () => {
      expect(CaixaCategoryAdapter.filterAndConvertCategories(null)).toEqual([]);
      expect(CaixaCategoryAdapter.filterAndConvertCategories(undefined)).toEqual([]);
      expect(CaixaCategoryAdapter.filterAndConvertCategories([])).toEqual([]);
    });

    it('should return only Caixa products categories', () => {
      const categories: CategoryDto[] = [
        { id: 'e5077e79-1ac8-4f84-a54e-c768eaf6a552', name: 'Original Name 1' },
        { id: 'non-existing-id', name: 'Original Name 2' },
        { id: '7d46e9d8-30cf-42be-99d0-19dd724d2be9', name: 'Original Name 3' },
        { id: 'another-non-existing-id', name: 'Original Name 4' },
      ];

      const result = CaixaCategoryAdapter.filterAndConvertCategories(categories);

      expect(result).toEqual([
        { id: 'e5077e79-1ac8-4f84-a54e-c768eaf6a552', name: 'Vida' },
        { id: '7d46e9d8-30cf-42be-99d0-19dd724d2be9', name: 'Acidentes Pessoais' },
      ]);
    });
  });
});
