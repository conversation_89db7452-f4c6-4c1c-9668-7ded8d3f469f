import { CategoryDto } from './dtos/category.dto';

const CAIXA_PRODUCTS_CATEGORIES: Record<string, string> = {
  // Communications
  'e5077e79-1ac8-4f84-a54e-c768eaf6a552': 'Vida',
  // Design
  '7d46e9d8-30cf-42be-99d0-19dd724d2be9': 'Acidentes Pessoais',
  // Development
  '9c671f59-bb3c-4945-832b-e5e26b65d5cd': 'Previdência',
  // Digital Marketing
  '49223560-7afb-46b9-bea9-5b7036ba673f': 'Dívida Zero',
};

export class CaixaCategoryAdapter {
  static toCaixaCategory(category: CategoryDto): CategoryDto {
    return { id: category.id, name: CAIXA_PRODUCTS_CATEGORIES[category.id] || category.name };
  }

  static filterAndConvertCategories(categories: CategoryDto[]): CategoryDto[] {
    if (!categories?.length) {
      return [];
    }

    const filteredCategories: CategoryDto[] = [];
    for (const category of categories) {
      if (!CAIXA_PRODUCTS_CATEGORIES[category.id]) {
        continue;
      }

      filteredCategories.push(CaixaCategoryAdapter.toCaixaCategory(category));
    }

    return filteredCategories;
  }
}
