import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { CoursesModule } from './courses/courses.module';
import { CoreModule, WorkspaceIdMiddleware } from './core';
import { ElasticModule } from './elastic';
import { PartnersModule } from './partners';
import { UsersModule } from './users/users.module';
import { ExceptionsModule } from './exceptions';
import { NotificationModule } from '@keeps-node-apis/@core';
import { CategoriesModule } from './categories/categories.module';
import { EnrollmentsModule } from './enrollments/enrollments.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
    }),
    CoreModule,
    CoursesModule,
    ElasticModule,
    PartnersModule,
    UsersModule,
    ExceptionsModule,
    NotificationModule,
    CategoriesModule,
    EnrollmentsModule,
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(WorkspaceIdMiddleware).forRoutes('*');
  }
}
