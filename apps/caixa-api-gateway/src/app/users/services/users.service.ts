import { BadRequestException, ConflictException, Injectable, InternalServerErrorException } from '@nestjs/common';
import { SmartzapClient } from '../../core';
import { UserSignupDto } from '../dtos/user-signup.dto';
import { SmartzapUserDto } from '../dtos/smartzap-user.dto';
import { PartnersSearchService } from '../../partners';
import { PARTNER_TYPE } from '../../partners/models/partner-type.enum';
import { PartnerElasticDto, SmartZapResponseDto } from '../../common/dtos';
import { cpf } from 'cpf-cnpj-validator';
import { UserNotFoundException, UserSignUpException } from '../../exceptions';
import { HttpClientError } from '@keeps-node-apis/@core';
import { CoursesService } from '../../courses/services/courses.service';
import { UserDto } from '../dtos/user.dto';
import { UserUpdateDto } from '../dtos/user-update.dto';

@Injectable()
export class UsersService {
  private static buildSmartZapUser(user: UserSignupDto, partner: PartnerElasticDto | null): SmartzapUserDto {
    const smartZapUser = {} as SmartzapUserDto;
    smartZapUser.cpf = cpf.strip(user.cpf);
    smartZapUser.name = user.name;
    smartZapUser.email = this.getUserEmail(user);
    smartZapUser.phone = user.phone;
    this.setUserPartnerInfo(smartZapUser, partner);
    return smartZapUser;
  }

  private static getUserEmail(user: UserSignupDto) {
    if (user.email) {
      return user.email;
    }
    const userCPF = cpf.strip(user.cpf);
    const suffix = user.partner_type === PARTNER_TYPE.LOTERICO ? 'cvp-loterica.com' : 'cvp-correspondente.com';
    return `${userCPF}@${suffix}`;
  }

  private static setUserPartnerInfo(user: SmartzapUserDto, partner: PartnerElasticDto | null) {
    if (!partner) {
      const defaultValue = 'REDE NÃO IDENTIFICADA';
      user.area_of_activity = defaultValue;
      user.director = defaultValue;
      user.manager = defaultValue;
      return;
    }
    user.director = partner.convention_number;
  }

  constructor(
    private smartZapClient: SmartzapClient,
    private partnersSearchService: PartnersSearchService,
    private coursesService: CoursesService,
  ) {}

  async createUser(workspaceId: string, userSignupDto: UserSignupDto): Promise<UserDto> {
    const user = await this.signUpUser(workspaceId, userSignupDto);
    const courseId = userSignupDto.course_id;

    if (courseId) {
      await this.enrollIntoCourse(workspaceId, courseId, user.id);
    }

    return user;
  }

  async findUser(workspaceId: string, search: string) {
    const searchKey = cpf.isValid(search) ? 'cpf' : 'ein';
    const response = await this.smartZapClient.get<SmartZapResponseDto<SmartzapUserDto>>('/users', workspaceId, {
      [searchKey]: search,
    });
    if (!response.result?.length) {
      throw new UserNotFoundException('User not found', workspaceId, search, searchKey);
    }
    const user = response.result[0];
    return SmartzapUserDto.toUserDto(user);
  }

  private async findPartnerByConventionNumber(conventionNumber: string) {
    if (!conventionNumber) {
      return null;
    }
    const partner = await this.partnersSearchService.getByConventionNumber(conventionNumber);
    if (!partner) {
      throw new BadRequestException(`Partner with convention number ${conventionNumber} not found.`);
    }
    return partner;
  }

  private async signUpUser(workspaceId: string, user: UserSignupDto) {
    if (user.terms_accept !== true) {
      throw new ConflictException('The terms and conditions should be accepted');
    }
    const partner = await this.findPartnerByConventionNumber(user.partner_convention_number);
    const smartZapUser = UsersService.buildSmartZapUser(user, partner);
    try {
      const { id } = await this.smartZapClient.post<{ id: string }>('/users', workspaceId, smartZapUser);
      return SmartzapUserDto.toUserDto({ ...smartZapUser, id });
    } catch (error) {
      if (error instanceof HttpClientError) {
        throw new UserSignUpException('Error while registering user', error.status, workspaceId, user);
      }
      throw error;
    }
  }

  async updateUser(workspaceId: string, userUpdateDto: UserUpdateDto) {
    try {
      await this.smartZapClient.post('/users', workspaceId, userUpdateDto);
    } catch (e) {
      throw new InternalServerErrorException('There was an error while updating the user');
    }
  }

  async giveUpCurrentEnrollment(workspaceId: string, userId: string) {
    try {
      await this.smartZapClient.post('/enrollments/give-up', workspaceId, { user_id: userId });
    } catch (e) {
      throw new InternalServerErrorException('There was an error while cancelling the user enrollment');
    }
  }

  private async enrollIntoCourse(workspaceId: string, courseId: string, user_id: string) {
    return await this.coursesService.enroll(workspaceId, courseId, { user_id, terms_accept: true });
  }
}
