version: '3'

services:
  api:
    image: caixa-api-gateway:latest
    container_name: caixa-api-gateway
    build:
      context: ../../
      dockerfile: apps/caixa-api-gateway/.docker/dev/Dockerfile
    ports:
      - 3000:3000
    env_file:
      - ./.env
    volumes:
      - ../../:/usr/src/app
    command: ./apps/caixa-api-gateway/.docker/dev/start.sh

  redis:
    image: redis:7.2.1-alpine
    env_file:
      - ./.env
    ports:
      - '6379:6379'
