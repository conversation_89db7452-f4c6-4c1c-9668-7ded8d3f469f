import { BatchApproveMissionEnrollmentStrategy } from './batch-approve-mission-enrollment';
import { MissionEnrollmentClientService } from '../../../../@core/konquest-grpc/mission-enrollment-client.service';
import { ENROLLMENT_STATUS } from '../../../../@core/constants/index';
import { Batch } from '../../../../entities/batch.entity';
import { MissionEnrollment } from '../../../../@core/konquest-grpc/mission-enrollment.interfaces';

describe('BatchMissionEnrollmentV2Strategy - batch-approve-mission-enrollment', () => {
  let strategy: BatchApproveMissionEnrollmentStrategy;
  let mockClientService: jest.Mocked<MissionEnrollmentClientService>;

  beforeEach(() => {
    mockClientService = {
      getMissionEnrollments: jest.fn(),
    } as unknown as jest.Mocked<MissionEnrollmentClientService>;

    strategy = new BatchApproveMissionEnrollmentStrategy(mockClientService);
  });

  describe('retrieveObjects', () => {
    const batch: Batch = {
      workspaceId: 'workspace-123',
    } as Batch;

    it('should call client service with formatted filters and return items', async () => {
      const filters = { campaignId: 1, status: ['PENDING_VALIDATION', 'APPROVED'] };
      const expectedFormattedFilters = {
        campaignId: '1',
        status: 'PENDING_VALIDATION,APPROVED',
      };

      const mockResponse = {
        items: [{ id: 'enroll-1', status: ENROLLMENT_STATUS.PENDING_VALIDATION } as MissionEnrollment],
      };

      mockClientService.getMissionEnrollments.mockResolvedValue(mockResponse);

      const result = await strategy.retrieveObjects(batch, filters);

      expect(mockClientService.getMissionEnrollments).toHaveBeenCalledWith(
        { filters: expectedFormattedFilters },
        'workspace-123',
      );

      expect(result).toEqual(mockResponse.items);
    });

    it('should return an empty array if response is empty', async () => {
      mockClientService.getMissionEnrollments.mockResolvedValue({} as any);

      const result = await strategy.retrieveObjects(batch, {});

      expect(result).toEqual([]);
    });
  });

  describe('isObjectAvailable', () => {
    it('should return true for PENDING_VALIDATION status', () => {
      const enrollment = { status: ENROLLMENT_STATUS.PENDING_VALIDATION } as any;
      expect(strategy.isObjectAvailable(enrollment)).toBe(true);
    });

    it('should return false for non-PENDING_VALIDATION status', () => {
      const enrollment = { status: ENROLLMENT_STATUS.COMPLETED } as any;
      expect(strategy.isObjectAvailable(enrollment)).toBe(false);
    });
  });
});
