import { Injectable, Inject } from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';
import { Observable } from 'rxjs';
import { USER_PACKAGE } from '../../constants';
import { UsersRepository } from '../interfaces/users.repository.interface';
import { UserListResponse, UserRequest } from './users-grpc.interfacte';
import { User } from '../interfaces/user.interface';
import { MetadataFactory } from './metadata.factory';
import { GrpcResilientClientService, MyAccountService } from '@keeps-node-apis/@core';

interface UserService {
  Get(data: UserRequest, metadata: any): Observable<UserListResponse>;
}

@Injectable()
export class UsersGrpcRepository implements UsersRepository {
  private usersService: UserService;

  constructor(
    @Inject(USER_PACKAGE) private readonly client: ClientGrpc,
    private readonly myAccountService: MyAccountService,
    private readonly grpcResilientClientService: GrpcResilientClientService,
  ) {}

  onModuleInit() {
    this.usersService = this.client.getService<UserService>('UserService');
  }

  async getUsers(filters: Record<string, any>, workspaceId: string): Promise<User[]> {
    const metadata = MetadataFactory.create(workspaceId);
    const grpcCall$ = this.usersService.Get({ filters }, metadata);
    const response = await this.grpcResilientClientService.execute(grpcCall$);
    return response?.items || [];
  }

  async updateUserStatus(
    userId: string,
    status: boolean,
    requestUserToken: string,
    workspaceId: string,
  ): Promise<void> {
    await this.myAccountService.updateUserStatus(
      userId,
      { status },
      { xClient: workspaceId, userToken: requestUserToken },
    );
  }
}
