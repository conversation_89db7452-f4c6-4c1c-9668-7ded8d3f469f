import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MYACCOUNT_GRPC_SERVER_URL_CONFIG, USER_PACKAGE, USER_PROTO_PACKAGE } from '../constants';
import { UsersGrpcRepository } from './users/users.repository';
import { GrpcResilientClientService, MyAccountService, RestModule } from '@keeps-node-apis/@core';
import { HttpModule } from '@nestjs/axios';
import { join } from 'path';
import { InvitationsRepository } from './interfaces/invitations.repository.abstract';
import { InvitationsGRPCRepository } from './users/invitations.repository';

@Module({
  imports: [
    ClientsModule.registerAsync([
      {
        name: USER_PACKAGE,
        imports: [ConfigModule],
        inject: [ConfigService],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.GRPC,
          options: {
            url: configService.get<string>(MYACCOUNT_GRPC_SERVER_URL_CONFIG),
            package: USER_PROTO_PACKAGE,
            protoPath: join(__dirname, './assets/protos/users.proto'),
            keepCase: false,
            longs: String,
            enums: String,
            defaults: true,
            oneofs: true,
            channelOptions: {
              'grpc.keepalive_time_ms': 10000,
              'grpc.keepalive_timeout_ms': 5000,
              'grpc.keepalive_permit_without_calls': 1,
              'grpc.max_reconnect_backoff_ms': 10000,
              'grpc.initial_reconnect_backoff_ms': 1000,
            },
          },
        }),
      },
    ]),
    HttpModule,
    RestModule,
  ],
  providers: [
    GrpcResilientClientService,
    UsersGrpcRepository,
    MyAccountService,
    { provide: InvitationsRepository, useClass: InvitationsGRPCRepository },
  ],
  exports: [UsersGrpcRepository, MyAccountService, InvitationsRepository],
})
export class MyaccountModule {}
