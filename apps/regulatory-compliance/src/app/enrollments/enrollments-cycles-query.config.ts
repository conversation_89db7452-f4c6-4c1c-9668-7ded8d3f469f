import { FilterOperator, PaginateConfig } from 'nestjs-paginate';
import EnrollmentCycle from '../entities/enrollment-cycle.entity';

export const ENROLLMENTS_CYCLES_QUERY_CONFIG: PaginateConfig<EnrollmentCycle> = {
  select: [
    'id',
    'status',
    'cycle',
    'deadline',
    'createdDate',
    'cyclesCount',
    'cycle.id',
    'cycle.duration',
    'cycle.periodType',
    'cycle.compliance.id',
    'cycle.compliance.name',
    'enrollment.id',
    'enrollment.learningObject.id',
    'enrollment.learningObject.name',
    'enrollment.learningObject.learningObjectTypeId',
    'enrollment.user.id',
    'enrollment.user.name',
    'enrollment.user.email',
    'enrollment.user.relatedUserLeaderId',
    'enrollment.user.relatedUserLeader.id',
    'enrollment.user.relatedUserLeader.name',
  ],
  searchableColumns: ['enrollment.learningObject.name', 'enrollment.user.name'],
  defaultSortBy: [['createdDate', 'DESC']],
  sortableColumns: [
    'enrollment.user.relatedUserLeader',
    'cycle.compliance.name',
    'enrollment.learningObject.name',
    'createdDate',
    'deadline',
    'status',
  ],
  filterableColumns: {
    status: [FilterOperator.IN],
    deadline: [FilterOperator.LT, FilterOperator.GT, FilterOperator.EQ],
    cycleId: [FilterOperator.EQ],
    'enrollment.user.id': [FilterOperator.EQ],
    'enrollment.learningObject.id': [FilterOperator.EQ],
    'enrollment.user.relatedUserLeader.id': [FilterOperator.EQ],
    'cycle.compliance.id': [FilterOperator.EQ],
  },
  relations: [
    'enrollment',
    'enrollment.learningObject',
    'enrollment.user',
    'enrollment.user.relatedUserLeader',
    'learningObject',
    'cycle',
    'cycle.compliance',
  ],
};
