import { Test, TestingModule } from '@nestjs/testing';
import EnrollmentCyclesService from './enrollment-cycles.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import EnrollmentCycle from '../../entities/enrollment-cycle.entity';
import { HasAnotherEnrollmentCycleInProgressException } from '../../@core/exceptions/has-another-enrollment-cycle-in-progress.exception';
import { CycleBelongsToAnotherWorkspaceException } from '../../@core/exceptions/cycle-belongs-to-another-workspace.exception';
import IEnrollmentCyclesRepository from '../repositories/enrollment-cycles.repository.interface';
import EnrollmentsService from './enrollments.service';
import CyclesService from '../../compliances/services/cycles.service';
import { NotAllowedException } from '../../@core/exceptions';
import { ENROLLMENT_CYCLE_STATUS } from '../../@core/constants';
import { APP_KONQUEST_ROLES, AuthUser } from '@keeps-node-apis/@core';
import Cycle from '../../entities/cycle.entity';
import { Chance } from 'chance';
import { CycleLearningObjectNotExistsException } from '../../@core/exceptions/cycle-learning-object-not-exists.exception';
import LearningObject from '../../entities/learning-object.entity';
import { CycleLearningObjectStatusUnabled } from '../../@core/exceptions/cycle-learning-status-unabled';
import { EnrollmentBelongsToAnotherWorkspaceException } from '../../@core/exceptions/enrollment-belongs-to-another-workspace.exception';
import EnrollmentCycleHistory from '../../entities/enrollment-cycle-history.entity';
import { TrailEnrollmentService } from './trail-enrollment-rest.service';
import { MissionEnrollmentService } from './mission-enrollment-rest.service';
import { addMonths } from 'date-fns';
import { Paginated, PaginateQuery } from 'nestjs-paginate';

// Mock app-root-path before other imports
jest.mock('app-root-path', () => ({
  toString: () => '/home/<USER>/keeps-node-apis',
  path: '/home/<USER>/keeps-node-apis',
  resolve: (path: string) => `/home/<USER>/keeps-node-apis/${path}`,
}));

jest.mock('@aws-sdk/client-s3', () => ({
  S3Client: jest.fn().mockImplementation(() => ({})),
  PutObjectCommand: jest.fn(),
}));

// Define mock functions first
const mockCreateReadStream = jest.fn();
const mockUnlinkSync = jest.fn();
const mockWriteFileSync = jest.fn();
const mockTmpdir = jest.fn();
const mockPathJoin = jest.fn();

// Mock only the specific functions we need from fs, os, and path
// Don't mock the entire modules to avoid conflicts with other dependencies
jest.mock('fs', () => {
  const actualFs = jest.requireActual('fs');
  return {
    ...actualFs,
    createReadStream: (...args: any[]) => mockCreateReadStream(...args),
    unlinkSync: (...args: any[]) => mockUnlinkSync(...args),
    writeFileSync: (...args: any[]) => mockWriteFileSync(...args),
  };
});

jest.mock('os', () => {
  const actualOs = jest.requireActual('os');
  return {
    ...actualOs,
    tmpdir: (...args: any[]) => mockTmpdir(...args),
  };
});

jest.mock('path', () => {
  const actualPath = jest.requireActual('path');
  return {
    ...actualPath,
    join: (...args: any[]) => mockPathJoin(...args),
  };
});

describe('EnrollmentCyclesService', () => {
  const chance = new Chance();
  let service: EnrollmentCyclesService;
  let mockRepository: jest.Mocked<Partial<IEnrollmentCyclesRepository>>;
  let mockEnrollmentsService: jest.Mocked<Partial<EnrollmentsService>>;
  let mockCyclesService: jest.Mocked<Partial<CyclesService>>;
  let mockLearningObjectRepository: jest.Mocked<Partial<any>>;
  let mockEnrollmentCycleHistoryRepository: jest.Mocked<Partial<any>>;
  let mockMissionEnrollmentService: jest.Mocked<Partial<any>>;
  let mockTrailEnrollmentService: jest.Mocked<Partial<any>>;

  const authUserWithLimitedAccess: AuthUser = {
    sub: '124124',
    roles: [APP_KONQUEST_ROLES.USER],
    exp: 0,
    iat: 0,
    auth_time: 0,
    jti: '',
    iss: '',
    aud: [],
    typ: '',
    azp: '',
    session_state: '',
    acr: '',
    'allowed-origins': [],
    realm_access: {
      roles: [],
    },
    resource_access: {},
    scope: '',
    sid: '',
    email_verified: false,
    name: '',
    given_name: '',
    locale: '',
    email: '',
  };

  const authAdminUser: AuthUser = {
    ...authUserWithLimitedAccess,
    roles: [APP_KONQUEST_ROLES.ADMIN],
  };

  beforeEach(async () => {
    mockRepository = {
      create: jest.fn(),
      save: jest.fn(),
      exist: jest.fn(),
      findOneOrFail: jest.fn(),
      findAllowed: jest.fn(),
      getUsersCount: jest.fn(),
      getStatusStats: jest.fn(),
      findOneByOrFail: jest.fn(),
      findOne: jest.fn(),
      upsert: jest.fn(),
      findPaginated: jest.fn(),
    };
    mockLearningObjectRepository = {
      findOneOrFail: jest.fn(),
    };
    mockEnrollmentCycleHistoryRepository = {
      create: jest.fn(),
      save: jest.fn(),
      findOne: jest.fn(),
    };
    mockMissionEnrollmentService = {
      enroll: jest.fn(),
    };
    mockTrailEnrollmentService = {
      enroll: jest.fn(),
    };

    mockEnrollmentsService = {
      findOneById: jest.fn(),
      findOneOrCreateById: jest.fn(),
    };

    mockCyclesService = {
      findOneById: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EnrollmentCyclesService,
        {
          provide: getRepositoryToken(EnrollmentCycle),
          useValue: mockRepository,
        },
        {
          provide: EnrollmentsService,
          useValue: mockEnrollmentsService,
        },
        {
          provide: CyclesService,
          useValue: mockCyclesService,
        },
        {
          provide: getRepositoryToken(LearningObject),
          useValue: mockLearningObjectRepository,
        },
        {
          provide: getRepositoryToken(EnrollmentCycleHistory),
          useValue: mockEnrollmentCycleHistoryRepository,
        },
        {
          provide: MissionEnrollmentService,
          useValue: mockMissionEnrollmentService,
        },
        {
          provide: TrailEnrollmentService,
          useValue: mockTrailEnrollmentService,
        },
      ],
    }).compile();

    service = module.get<EnrollmentCyclesService>(EnrollmentCyclesService);

    mockLearningObjectRepository.findOneOrFail.mockResolvedValue({
      developmentStatus: 'DONE',
    } as any);
  });

  describe('create', () => {
    const workspaceId = 'workspace123';
    const enrollmentCycleCreateDto = {
      enrollmentId: 'enrollment456',
      cycleId: 'cycle789',
      learningObjectId: 'learningObject123',
      deadline: expect.any(Date),
      cyclesCount: 1,
    };

    it('should throw EnrollmentBelongsToAnotherWorkspaceException if the enrollment belongs to another workspace', async () => {
      mockEnrollmentsService.findOneById.mockResolvedValue({
        id: 'enrollment456',
        workspaceId: 'anotherWorkspace',
      } as any);

      service.validateCycle = jest.fn().mockResolvedValue({ workspaceId: workspaceId } as any);
      service.validateLearningObjectDefined = jest.fn().mockResolvedValue(true);
      await expect(service.create(enrollmentCycleCreateDto, workspaceId, {} as any)).rejects.toThrow(
        EnrollmentBelongsToAnotherWorkspaceException,
      );
    });

    it('should throw CycleBelongsToAnotherWorkspaceException if the cycle belongs to another workspace', async () => {
      mockEnrollmentsService.findOneById.mockResolvedValue({
        id: 'enrollment456',
        workspaceId: workspaceId,
      } as any);
      mockCyclesService.findOneById.mockResolvedValue({
        id: 'cycle789',
        workspaceId: 'anotherWorkspace',
        learningObjectId: 'learningObject123',
      } as any);

      await expect(service.create(enrollmentCycleCreateDto, workspaceId, {} as any)).rejects.toThrow(
        CycleBelongsToAnotherWorkspaceException,
      );
    });

    it('should throw CycleLearningObjectStatusUnabled if LearningObject developmentStatus INACTIVATED', async () => {
      mockLearningObjectRepository.findOneOrFail.mockResolvedValue({
        developmentStatus: 'INACTIVATED',
      } as any);
      mockEnrollmentsService.findOneById.mockResolvedValue({
        id: 'enrollment456',
        workspaceId: workspaceId,
      } as any);
      service.validateCycle = jest.fn().mockResolvedValue({ workspaceId: workspaceId } as any);
      service.validateLearningObjectDefined = jest.fn().mockResolvedValue(true);

      jest.spyOn(service as any, 'addEnrollmentCycleHistory');

      await expect(service.create(enrollmentCycleCreateDto, workspaceId, {} as any)).rejects.toThrow(
        CycleLearningObjectStatusUnabled,
      );
    });

    it('should throw HasAnotherEnrollmentCycleInProgressException if there is another enrollment cycle in progress', async () => {
      mockEnrollmentsService.findOneById.mockResolvedValue({
        id: 'enrollment456',
        workspaceId: workspaceId,
        userId: 'user123',
      } as any);
      mockCyclesService.findOneById.mockResolvedValue({
        id: 'cycle789',
        workspaceId: workspaceId,
        duration: 30,
        learningObjectId: 'learningObject123',
      } as any);
      service.cannotRenew = jest.fn().mockResolvedValue(true);
      service.addEnrollmentCycleHistory = jest.fn();

      await expect(service.create(enrollmentCycleCreateDto, workspaceId, {} as any)).rejects.toThrow(
        HasAnotherEnrollmentCycleInProgressException,
      );
    });

    it('should throw CycleLearningObjectNotExistsException if the cycle does not have a learning object', async () => {
      mockEnrollmentsService.findOneById.mockResolvedValue({
        id: 'enrollment456',
        workspaceId: workspaceId,
      } as any);
      mockCyclesService.findOneById.mockResolvedValue({
        id: 'cycle789',
        workspaceId: workspaceId,
        duration: 30,
      } as any);
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.create(enrollmentCycleCreateDto, workspaceId, {} as any)).rejects.toThrow(
        CycleLearningObjectNotExistsException,
      );
    });

    it('should successfully create a new enrollment cycle', async () => {
      // Given
      const deadline = undefined;
      const cycleId = chance.guid();
      mockEnrollmentsService.findOneById.mockResolvedValue({
        id: chance.guid(),
        workspaceId,
      } as any);

      mockCyclesService.findOneById.mockResolvedValue({
        id: cycleId,
        workspaceId,
        learningObjectId: 'learningObject123',
      } as any);

      mockRepository.findOne.mockResolvedValue(null);

      const createSpy = jest
        .spyOn(mockRepository, 'create')
        .mockImplementation((data) => ({ ...data, id: cycleId }) as any);
      mockRepository.save.mockImplementation((data) => data as any);
      // When
      const result = await service.create(enrollmentCycleCreateDto, workspaceId, {} as any);

      // Then
      expect(createSpy).toHaveBeenCalledWith({
        deadline,
        ...enrollmentCycleCreateDto,
      });
      expect(result.id).toEqual(cycleId);
    });

    it('should successfully renew enrollment cycle', async () => {
      // Given
      const cycleId = chance.guid();
      mockEnrollmentsService.findOneById.mockResolvedValue({
        id: chance.guid(),
        workspaceId,
      } as any);

      mockCyclesService.findOneById.mockResolvedValue({
        id: cycleId,
        workspaceId,
        learningObjectId: 'learningObject123',
      } as any);
      service.validateEnrollmentCycle;
      mockRepository.findOne.mockResolvedValue({
        id: cycleId,
        status: ENROLLMENT_CYCLE_STATUS.EXPIRED,
      } as any);

      service.renewEnrollmentCycle = jest.fn().mockResolvedValue({
        id: cycleId,
        status: ENROLLMENT_CYCLE_STATUS.IN_PROGRESS,
      } as any);
      mockRepository.save.mockImplementation((data) => data as any);

      // When
      const result = await service.create(enrollmentCycleCreateDto, workspaceId, {} as any);

      // Then
      expect(service.renewEnrollmentCycle).toHaveBeenCalled();
      expect(result.id).toEqual(cycleId);
    });
  });

  describe('disable', () => {
    const id = 'enrollmentCycleId123';
    const workspaceId = 'workspace123';

    it('should throw an error if the enrollment cycle is not found', async () => {
      mockRepository.findOneOrFail.mockImplementationOnce(() => {
        throw new Error('Enrollment cycle not found.');
      });

      await expect(service.disable(id, authUserWithLimitedAccess, workspaceId)).rejects.toThrow(
        'Enrollment cycle not found.',
      );
    });

    it('should not call save if the enrollment cycle is already disabled', async () => {
      mockRepository.findOneOrFail.mockResolvedValueOnce({
        id,
        cycle: {
          workspaceId: workspaceId,
        },
        enrollment: {
          user: { id: '213123' },
        },
        status: ENROLLMENT_CYCLE_STATUS.DISABLED,
      } as any);

      await service.disable(id, authAdminUser, workspaceId);
      expect(mockRepository.save).not.toHaveBeenCalled();
    });

    it('should throw NotAllowedException if the workspaceId does not match', async () => {
      mockRepository.findOneOrFail.mockResolvedValueOnce({
        id,
        cycle: {
          workspaceId: 'differentWorkspaceId',
        },
        enrollment: {
          user: { id: '213123' },
        },
        status: ENROLLMENT_CYCLE_STATUS.IN_PROGRESS,
      } as any);

      await expect(service.disable(id, authAdminUser, workspaceId)).rejects.toThrow(NotAllowedException);
      expect(mockRepository.save).not.toHaveBeenCalled();
    });

    it('should throw NotAllowedException if the consumer user try to disable a cycle of another user', async () => {
      mockRepository.findOneOrFail.mockResolvedValueOnce({
        id,
        cycle: {
          workspaceId: workspaceId,
        },
        enrollment: {
          user: { id: '213123' },
        },
        status: ENROLLMENT_CYCLE_STATUS.IN_PROGRESS,
      } as any);

      await expect(service.disable(id, authUserWithLimitedAccess, workspaceId)).rejects.toThrow(NotAllowedException);
      expect(mockRepository.save).not.toHaveBeenCalled();
    });

    it('should consumer user update status to DISABLED if the enrollment cycle is linked to an user led', async () => {
      mockRepository.findOneOrFail.mockResolvedValueOnce({
        id,
        cycle: {
          workspaceId: workspaceId,
        },
        enrollment: {
          user: {
            id: '213123',
            relatedUserLeaderId: authUserWithLimitedAccess.sub,
          },
        },
        status: ENROLLMENT_CYCLE_STATUS.IN_PROGRESS,
      } as EnrollmentCycle);

      await service.disable(id, authUserWithLimitedAccess, workspaceId);
      expect(mockRepository.save).toHaveBeenCalled();
    });

    it('should throw NotAllowedException if the consumer user try to disable a cycle of another user', async () => {
      mockRepository.findOneOrFail.mockResolvedValueOnce({
        id,
        cycle: {
          workspaceId: workspaceId,
        },
        enrollment: {
          user: { id: '213123' },
        },
        status: ENROLLMENT_CYCLE_STATUS.IN_PROGRESS,
      } as EnrollmentCycle);

      await expect(service.disable(id, authUserWithLimitedAccess, workspaceId)).rejects.toThrow(NotAllowedException);
      expect(mockRepository.save).not.toHaveBeenCalled();
    });

    it('should consumer user update status to DISABLED if the enrollment cycle is linked to an user led', async () => {
      mockRepository.findOneOrFail.mockResolvedValueOnce({
        id,
        cycle: {
          workspaceId: workspaceId,
        },
        enrollment: {
          user: {
            id: '213123',
            relatedUserLeaderId: authUserWithLimitedAccess.sub,
          },
        },
        status: ENROLLMENT_CYCLE_STATUS.IN_PROGRESS,
      } as EnrollmentCycle);

      await service.disable(id, authUserWithLimitedAccess, workspaceId);
      expect(mockRepository.save).toHaveBeenCalled();
    });

    it('should update status to DISABLED if the enrollment cycle is in a different status', async () => {
      mockRepository.findOneOrFail.mockResolvedValueOnce({
        id,
        cycle: {
          workspaceId: workspaceId,
        },
        enrollment: {
          user: { id: '123456' },
        },
        status: ENROLLMENT_CYCLE_STATUS.IN_PROGRESS,
      } as EnrollmentCycle);

      mockRepository.save.mockResolvedValueOnce({} as EnrollmentCycle);

      await service.disable(id, authAdminUser, workspaceId);
      expect(mockRepository.save).toHaveBeenCalled();
    });
  });

  it('should create an enrollment cycle without mandatory relation', async () => {
    const enrollmentId = 'testEnrollmentId';
    const cycleId = 'testCycleId';
    const testCycle = {
      duration: 30,
      periodType: 'DAY',
      learningObjectId: 'learningObject123',
    } as Partial<Cycle>;
    const testEnrollment = {} as any;

    const mockDate = new Date(2024, 5, 30);
    global.Date = jest.fn(() => mockDate) as any;
    mockCyclesService.findOneById.mockResolvedValue(testCycle as any);
    mockEnrollmentsService.findOneOrCreateById.mockResolvedValue(testEnrollment);
    mockRepository.create.mockReturnValue({} as any);

    await service.createWithoutMandatoryRelation({ enrollmentId, cycleId });

    expect(mockCyclesService.findOneById).toHaveBeenCalledWith(cycleId);
    expect(mockEnrollmentsService.findOneOrCreateById).toHaveBeenCalledWith(enrollmentId);
    expect(mockRepository.create).toHaveBeenCalledWith({
      cycle: testCycle,
      enrollment: testEnrollment,
      deadline: addMonths(new Date(), 1),
      cyclesCount: 1,
    });
    expect(mockRepository.save).toHaveBeenCalled();
  });

  it('should retrieve enrollment cycle stats', async () => {
    const workspaceId = '12412';
    const usersCount = 40;
    const statusStats = [
      { status: ENROLLMENT_CYCLE_STATUS.COMPLETED, count: 10 },
      { status: ENROLLMENT_CYCLE_STATUS.DISABLED, count: 20 },
      { status: ENROLLMENT_CYCLE_STATUS.EXPIRED, count: 30 },
      { status: ENROLLMENT_CYCLE_STATUS.IN_PROGRESS, count: 40 },
    ];
    mockRepository.getStatusStats.mockResolvedValueOnce(statusStats);
    mockRepository.getUsersCount.mockResolvedValueOnce(usersCount);

    const stats = await service.getStats(workspaceId);

    expect(stats.statusStats).toEqual(statusStats);
    expect(stats.usersCount).toEqual(usersCount);
    expect(mockRepository.getStatusStats).toHaveBeenCalledWith(workspaceId);
    expect(mockRepository.getUsersCount).toHaveBeenCalledWith(workspaceId);
  });

  describe('exportToCsv', () => {
    const workspaceId = 'workspace123';

    const mockListParams = {
      search: '',
      relatedUserLeaderId: '',
      perPage: 10,
      page: 1,
      skip: 0,
    };

    const mockEnrollmentCycles = [
      {
        id: '1',
        status: 'IN_PROGRESS',
        deadline: new Date('2024-12-31'),
        createdDate: new Date('2024-01-01'),
        cyclesCount: 1,
        enrollment: {
          userId: 'user-1',
          user: {
            name: 'João Silva',
            relatedUserLeader: {
              name: 'Maria Santos',
            },
          },
          learningObject: {
            name: 'Curso de Segurança',
          },
        },
        cycle: {
          compliance: {
            name: 'Normativa de Segurança',
          },
          duration: 30,
          periodType: 'DAY',
        },
      },
    ];

    let mockResponse: any;
    let mockReadStream: any;

    beforeEach(() => {
      // Reset all mocks
      jest.clearAllMocks();

      // Create a proper mock response object with stream interface
      mockResponse = {
        setHeader: jest.fn(),
        on: jest.fn(),
        once: jest.fn(),
        emit: jest.fn(),
        pipe: jest.fn(),
        write: jest.fn(),
        end: jest.fn(),
        destroy: jest.fn(),
      };

      // Create a mock read stream
      mockReadStream = {
        pipe: jest.fn().mockImplementation((dest) => {
          // Simulate successful piping
          setTimeout(() => {
            mockReadStream.emit('end');
          }, 0);
          return dest;
        }),
        on: jest.fn().mockImplementation((event, callback) => {
          if (event === 'end') {
            setTimeout(callback, 0);
          }
          return mockReadStream;
        }),
        emit: jest.fn(),
        destroy: jest.fn(),
      };

      mockCreateReadStream.mockReturnValue(mockReadStream);
      mockUnlinkSync.mockImplementation(() => {});
      mockWriteFileSync.mockImplementation(() => {});
      mockTmpdir.mockReturnValue('/tmp');
      mockPathJoin.mockReturnValue('/tmp/test.csv');

      const mockQueryBuilder = {
        getMany: jest.fn().mockResolvedValue(mockEnrollmentCycles),
      };
      mockRepository.findAllowed.mockReturnValue(mockQueryBuilder as any);
    });

    it('should call findAllowed with correct parameters', async () => {
      await service.exportToCsv(workspaceId, mockListParams as any, authAdminUser, mockResponse);

      expect(mockRepository.findAllowed).toHaveBeenCalledWith(workspaceId, authAdminUser, mockListParams);
    });

    it('should set correct response headers', async () => {
      await service.exportToCsv(workspaceId, mockListParams as any, authAdminUser, mockResponse);

      expect(mockResponse.setHeader).toHaveBeenCalledWith('Content-Type', 'text/csv');
      expect(mockResponse.setHeader).toHaveBeenCalledWith(
        'Content-Disposition',
        expect.stringMatching(/attachment; filename="enrollment-cycles-export-\d{4}-\d{2}-\d{2}\.csv"/),
      );
    });

    it('should create and stream CSV file', async () => {
      await service.exportToCsv(workspaceId, mockListParams as any, authAdminUser, mockResponse);

      const wasFileMockCalled = mockWriteFileSync.mock.calls.length > 0 || mockCreateReadStream.mock.calls.length > 0;

      expect(wasFileMockCalled).toBe(true);
      expect(mockCreateReadStream).toHaveBeenCalledWith('/tmp/test.csv');
      expect(mockReadStream.pipe).toHaveBeenCalledWith(mockResponse);
    });

    it('should clean up temporary file after streaming', async () => {
      await service.exportToCsv(workspaceId, mockListParams as any, authAdminUser, mockResponse);

      await new Promise((resolve) => setTimeout(resolve, 10));

      expect(mockUnlinkSync).toHaveBeenCalledWith('/tmp/test.csv');
    });

    it('should handle empty results', async () => {
      const mockQueryBuilder = {
        getMany: jest.fn().mockResolvedValue([]),
      };
      mockRepository.findAllowed.mockReturnValue(mockQueryBuilder as any);

      await expect(
        service.exportToCsv(workspaceId, mockListParams as any, authAdminUser, mockResponse),
      ).resolves.not.toThrow();

      const wasFileOperationAttempted =
        mockWriteFileSync.mock.calls.length > 0 || mockCreateReadStream.mock.calls.length > 0;

      expect(wasFileOperationAttempted).toBe(true);
      expect(mockResponse.setHeader).toHaveBeenCalledWith('Content-Type', 'text/csv');
    });

    it('should handle errors gracefully', async () => {
      const error = new Error('File system error');
      mockCreateReadStream.mockImplementation(() => {
        throw error;
      });

      await expect(
        service.exportToCsv(workspaceId, mockListParams as any, authAdminUser, mockResponse),
      ).rejects.toThrow('File system error');
    });

    it('should debug what methods are actually called', async () => {
      jest.clearAllMocks();

      mockCreateReadStream.mockReturnValue(mockReadStream);
      mockUnlinkSync.mockImplementation(() => {});
      mockWriteFileSync.mockImplementation(() => {});
      mockTmpdir.mockReturnValue('/tmp');
      mockPathJoin.mockReturnValue('/tmp/test.csv');

      const mockQueryBuilder = {
        getMany: jest.fn().mockResolvedValue(mockEnrollmentCycles),
      };
      mockRepository.findAllowed.mockReturnValue(mockQueryBuilder as any);

      await service.exportToCsv(workspaceId, mockListParams as any, authAdminUser, mockResponse);

      expect(mockResponse.setHeader).toHaveBeenCalledWith('Content-Type', 'text/csv');
      expect(mockResponse.setHeader).toHaveBeenCalledWith(
        'Content-Disposition',
        expect.stringMatching(/attachment; filename="enrollment-cycles-export-\d{4}-\d{2}-\d{2}\.csv"/),
      );
    });
  });

  describe('findAll', () => {
    const workspaceId = chance.guid();
    const params = { page: 1, limit: 10 } as PaginateQuery;

    beforeEach(() => {
      jest.useFakeTimers().setSystemTime(new Date('01 Sep 2025 08:00:00 GMT-0300'));
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('should call findPaginated with correct args and return the result', async () => {
      const resultMock = { data: [], meta: { total: 0 } } as unknown as Paginated<EnrollmentCycle>;
      mockRepository.findPaginated.mockResolvedValue(resultMock);

      const result = await service.findAll(workspaceId, authAdminUser, params);

      expect(mockRepository.findPaginated).toHaveBeenCalledWith(workspaceId, authAdminUser, params);
      expect(result).toBe(resultMock);
    });

    it('should set cycles with status COMPLETED and a deadline within 30 days as EXPIRING', async () => {
      const within30Days = new Date('05 Sep 2025 08:00:00 GMT-0300');
      const cycles = [
        {
          id: chance.guid(),
          status: ENROLLMENT_CYCLE_STATUS.COMPLETED,
          deadline: within30Days,
        },
      ] as EnrollmentCycle[];
      const mockResponse = { data: cycles } as Paginated<EnrollmentCycle>;
      mockRepository.findPaginated.mockResolvedValue(mockResponse);

      const result = await service.findAll(workspaceId, authAdminUser, params);

      expect(result.data[0].status).toBe(ENROLLMENT_CYCLE_STATUS.EXPIRING);
    });

    it('should not change status when deadline is after 30 days', async () => {
      const beyond30Days = new Date('01 Sep 2026 08:00:00 GMT-0300');
      const cycles: any[] = [
        {
          id: chance.guid(),
          status: ENROLLMENT_CYCLE_STATUS.COMPLETED,
          deadline: beyond30Days.toISOString(),
        },
      ];
      const mockResponse = { data: cycles } as Paginated<EnrollmentCycle>;
      mockRepository.findPaginated.mockResolvedValue(mockResponse);

      const result = await service.findAll(workspaceId, authAdminUser, params);

      expect(result.data[0].status).toBe(ENROLLMENT_CYCLE_STATUS.COMPLETED);
    });

    it('should not change status for not COMPLETED cycles even within 30 days', async () => {
      const within30Days = new Date('05 Sep 2025 08:00:00 GMT-0300');
      const cycles: any[] = [
        {
          id: chance.guid(),
          status: ENROLLMENT_CYCLE_STATUS.IN_PROGRESS,
          deadline: within30Days.toISOString(),
        },
      ];
      const mockResponse = { data: cycles } as Paginated<EnrollmentCycle>;
      mockRepository.findPaginated.mockResolvedValue(mockResponse);

      const result = await service.findAll(workspaceId, authAdminUser, params);

      expect(result.data[0].status).toBe(ENROLLMENT_CYCLE_STATUS.IN_PROGRESS);
    });

    it('should handle an empty response', async () => {
      const mockResponse = { data: [] } as Paginated<EnrollmentCycle>;
      mockRepository.findPaginated.mockResolvedValue(mockResponse);

      const result = await service.findAll(workspaceId, authAdminUser, params);

      expect(result.data).toEqual([]);
    });
  });
});
