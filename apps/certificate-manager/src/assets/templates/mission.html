<!doctype html>
<html>
  <head>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <title>{{__ "FILE_TITLE" }}</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
      rel="stylesheet"
    />
    <script>
      function adjustFontSizeByClass(className) {
        document.querySelectorAll(className).forEach((el) => {
          if (!el.style.width) {
            return;
          }

          let fontSize = 32;
          const minFontSize = 5;
          el.style.setProperty('font-size', `${fontSize}px`, 'important');
          while (el.scrollWidth > el.clientWidth && fontSize > minFontSize) {
            fontSize--;
            el.style.setProperty('font-size', `${fontSize}px`, 'important');
          }
        });
      }

      window.addEventListener('load', () => adjustFontSizeByClass('.resizable-text'));
      window.addEventListener('resize', () => adjustFontSizeByClass('.resizable-text'));
    </script>
    <style type="text/css">
      body,
      html {
        margin: 0;
        padding: 0;
      }

      body {
        /*
          As proporções finais do certificado padrão são 75% do tamanho informado (1920x1022)
          resultando em 1440x767, por isso usamos esse valor resultante para o posicionamento e cálculo dos tamanhos em pixels
        */
        --scaling-factor: 0.75;

        /* ----- Cuidado com a formatação das variáveis abaixo, precisam seguir a sintaxe de interpolação do handlebars ---- */
        /*noinspection CssInvalidHtmlTagReference*/
        --certificate-width: {{numberToPx config.width}};
        /*noinspection CssInvalidHtmlTagReference*/
        --certificate-height: {{numberToPx config.height}};
        /*noinspection CssInvalidHtmlTagReference*/
        --background-color: {{config.backgroundColor}};
        /*noinspection CssInvalidHtmlTagReference*/
        --workspace-color: {{config.workspaceColor}};
        /*noinspection CssInvalidHtmlTagReference*/
        --text-color: {{config.textColor}};
        /*noinspection CssInvalidHtmlTagReference*/
        --certificate-content-rows: {{#if config.portrait}}8{{else}}5{{/if}};
        /*noinspection CssInvalidHtmlTagReference*/
        --certificate-content-columns: {{#if config.portrait}}2{{else}}3{{/if}};

        --font-size: calc(24px / var(--scaling-factor));
        --secondary-font-size: calc(14px / var(--scaling-factor));
        --signed-by-font-size: calc(12px / var(--scaling-factor));
        --header-font-size: calc(48px / var(--scaling-factor));
        --holder-font-size: calc(56px / var(--scaling-factor));
        --course-name-font-size: calc(40px / var(--scaling-factor));

        --logo-max-width: calc(150px / var(--scaling-factor));
        /*noinspection CssUnknownTarget*/
        --background-image-src: url('{{config.backgroundImageUrl}}');

        /*noinspection CssUnresolvedCustomProperty*/
        color: var(--text-color);
        font-family: 'Poppins', sans-serif;
        font-size: var(--font-size);
        counter-reset: items-counter;
      }

      .draggable {
        position: absolute;
        transform: translate(-50%, -50%);
      }

      .text-center {
        text-align: center;
        width: 100%;
        max-width: 90%;
      }

      .main-container {
        position: relative;
        display: flex;
        flex-direction: column;
        width: 100vw;
        height: 100vh;
      }

      .contents-page {
        max-height: calc(var(--certificate-height) / var(--scaling-factor));
        color: #000000;
      }

      .contents-container {
        display: grid;
        margin: 0 calc(8px / var(--scaling-factor)) calc(24px / var(--scaling-factor)) calc(8px / var(--scaling-factor));
        height: 100%;
        grid-template-rows: repeat(var(--certificate-content-rows), 1fr);
        grid-template-columns: repeat(var(--certificate-content-columns), 1fr);
        grid-auto-flow: column;
        position: relative;
      }

      .contents-container-item {
        font-size: calc(20px / var(--scaling-factor));
        margin: 0 calc(16px / var(--scaling-factor));
      }

      .contents-container-item p {
        margin: calc(4px / var(--scaling-factor)) 0;
      }

      .content-item-title {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
      }

      .content-item-description {
        font-size: var(--secondary-font-size);
      }

      .content-item-title::before {
        counter-increment: items-counter;
        content: counter(items-counter) " - ";
      }

      .content-item-time {
        font-weight: bold;
      }

      .content-item-info {
        display: flex;
        flex-direction: row;
        gap: calc(8px / var(--scaling-factor));
        font-size: var(--secondary-font-size);
      }

      .dividers-container {
        display: grid;
        grid-template-columns: repeat(var(--certificate-content-columns), 1fr);
        position: absolute;
        justify-items: end;
        inset: 0;
      }

      .vertical-divider {
        height: 100%;
        width: calc(1px / var(--scaling-factor));
        background-color: #434343;
      }

      .first-container-background {
        background-image: var(--background-image-src);
        background-size: cover;
        background-repeat: no-repeat;
      }

      .logo-image {
        max-width: var(--logo-max-width);
      }

      .certificate-title {
        font-size: var(--header-font-size);
        font-weight: bolder;
        width: 100%;
        max-width: 90%;
      }

      .holder-name {
        font-size: var(--holder-font-size);
        /*noinspection CssUnresolvedCustomProperty*/
        color: var(--workspace-color);
        font-weight: bolder;
        width: 100%;
        max-width: 90%;
      }

      .course-name {
        font-size: var(--course-name-font-size);
        font-weight: bolder;
        width: 100%;
        max-width: 90%;
      }

      .signed-by {
        font-size: var(--signed-by-font-size);
      }

      .user-performance-item {
        text-align: center;
        --item-margin: calc(1px / var(--scaling-factor));

        .title {
          margin: 0 0 var(--item-margin) 0;
          font-weight: bold;
        }

        .value {
          margin: 0;
        }
      }

      .contents-title {
        margin: calc(20px / var(--scaling-factor)) auto;
      }
    </style>
  </head>

  <body>
    <div class="main-container first-container-background">
      <!--suppress HtmlUnknownAttribute -->
      {{#elementVisible config "logo"}}
      <img {{{elementStyle config.elementsConfig.logo}}} class="logo-image draggable" src="{{config.brandImageUrl}}" />
      {{/elementVisible}}

      <!--suppress HtmlUnknownAttribute -->
      {{#elementVisible config "title"}}
      <div {{{elementStyle config.elementsConfig.title}}} class="certificate-title draggable text-center">
        {{__ "TITLE_CERTIFICATE" }}
      </div>
      {{/elementVisible}}

      <!--suppress HtmlUnknownAttribute -->
      {{#elementVisible config "weCertifyLabel"}}
      <div {{{elementStyle config.elementsConfig.weCertifyLabel}}} class="draggable text-center">
        {{__ "CERTIFICATE_OF" }}
      </div>
      {{/elementVisible}}

      <!--suppress HtmlUnknownAttribute -->
      <div {{{elementStyle config.elementsConfig.holderName}}} class="holder-name resizable-text draggable text-center">
        {{user_name}}
      </div>

      <!--suppress HtmlUnknownAttribute -->
      {{#elementVisible config "hasCompletedLabel"}}
      <div {{{elementStyle config.elementsConfig.hasCompletedLabel}}} class="draggable text-center">
        {{__ "SUCCESSFULLY_COMPLETED_THE_COURSE" }}
      </div>
      {{/elementVisible}}

      <!--suppress HtmlUnknownAttribute -->
      <div {{{elementStyle config.elementsConfig.courseName}}} class="course-name resizable-text draggable text-center">
        {{course_name}}
      </div>

      <!--suppress HtmlUnknownAttribute -->
      {{#elementVisible config "signedByLabel"}}
      <div
        {{{elementStyle
        config.elementsConfig.signedByLabel}}}
        class="signed-by draggable resizable-text text-center"
      >
        {{__ "CERTIFICATE_EMISSION" }}&nbsp;{{signed_by}}
      </div>
      {{/elementVisible}}

      <!--suppress HtmlUnknownAttribute -->
      {{#if config.displayPerformance}}
      <div {{{elementStyle config.elementsConfig.performanceInfo}}} class="user-performance-item draggable">
        {{#elementVisible config "performanceInfo"}}
        <p class="title">{{__ "PERFORMANCE"}}</p>
        {{/elementVisible}}
        <p class="value">{{performance}}</p>
      </div>
      {{/if}}

      <!--suppress HtmlUnknownAttribute -->
      {{#if config.displayTotalTime}}
      <div {{{elementStyle config.elementsConfig.totalTime}}} class="user-performance-item draggable">
        {{#elementVisible config "totalTime"}}
        <p class="title">{{__ "TIME"}}</p>
        {{/elementVisible}}
        <p class="value">{{time}}</p>
      </div>
      {{/if}}

      <!--suppress HtmlUnknownAttribute -->
      {{#if config.displayConclusionDate}}
      <div {{{elementStyle config.elementsConfig.conclusionDate}}} class="user-performance-item draggable">
        {{#elementVisible config "conclusionDate"}}
        <p class="title">{{__ "FINISH_DATE"}}</p>
        {{/elementVisible}}
        <p class="value">{{date_finish}}</p>
      </div>
      {{/if}}
    </div>

    {{#each paginatedContent as |page|}}
    <div class="main-container contents-page">
      <h4 class="contents-title">{{__ "CONTENTS" }} {{config}}</h4>
      <div class="contents-container">
        <div class="dividers-container">
          <div class="vertical-divider"></div>
          {{#unless ../config.portrait}}
          <div class="vertical-divider"></div>
          {{/unless}}
        </div>
        {{#each this as |content|}}
        <div class="contents-container-item">
          <p class="content-item-title">{{ content.title }}</p>
          <p class="content-item-description">{{ content.description }}</p>
          <div class="content-item-info">
            <p>{{__ "DURATION" }}:&nbsp;<span class="content-item-time">{{ content.time }}</span></p>
            {{#if content.performance}}
            <p>{{__ "PERFORMANCE" }}:&nbsp;<span class="content-item-time">{{ content.performance }}</span></p>
            {{/if}}
          </div>
        </div>
        {{/each}}
      </div>
    </div>
    {{/each}}
  </body>
</html>
