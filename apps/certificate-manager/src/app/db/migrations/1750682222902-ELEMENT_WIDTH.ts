import { MigrationInterface, QueryRunner } from "typeorm";

export class ELEMENTWIDTH1750682222902 implements MigrationInterface {
    name = 'ELEMENTWIDTH1750682222902'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "certificate_element" ADD "width" integer`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "certificate_element" DROP COLUMN "width"`);
    }

}
