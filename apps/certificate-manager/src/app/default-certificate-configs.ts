import { CertificateConfig, CertificateElementsStyling } from './models';

export const DEFAULT_ELEMENTS_STYLING: CertificateElementsStyling = {
  logo: { position: { x: 0.5, y: 0.1 } },
  title: { position: { x: 0.5, y: 0.225 } },
  weCertifyLabel: { position: { x: 0.5, y: 0.35 } },
  holderName: { position: { x: 0.5, y: 0.45 } },
  hasCompletedLabel: { position: { x: 0.5, y: 0.55 } },
  courseName: { position: { x: 0.5, y: 0.65 } },
  signedByLabel: { position: { x: 0.5, y: 0.95 }, hidden: true },
  performanceInfo: { position: { x: 0.25, y: 0.85 } },
  totalTime: { position: { x: 0.5, y: 0.85 } },
  conclusionDate: { position: { x: 0.75, y: 0.85 } },
};

const DEFAULT_CERTIFICATE_CONFIG: CertificateConfig = {
  backgroundColor: '#FFF',
  textColor: '#757575',
  width: 1920,
  height: 1022,
  portrait: false,
  backgroundImageUrl: '',
  brandImageUrl: '',
  displayPerformance: true,
  displayConclusionDate: true,
  displayTotalTime: true,
  elementsConfig: DEFAULT_ELEMENTS_STYLING,
} as const;

export const DEFAULT_MISSION_CONFIG: CertificateConfig = {
  ...DEFAULT_CERTIFICATE_CONFIG,
  backgroundImageUrl: 'https://s3.amazonaws.com/keeps.reports/assets/mission_certificate_bg_clean.png',
} as const;

export const MISSION_PORTRAIT_CONFIG: CertificateConfig = {
  ...DEFAULT_MISSION_CONFIG,
  portrait: true,
  width: 1022,
  height: 1920,
} as const;

export const DEFAULT_TRAIL_CONFIG: CertificateConfig = {
  ...DEFAULT_CERTIFICATE_CONFIG,
  backgroundImageUrl: 'https://s3.amazonaws.com/keeps.reports/assets/trail_certificate_bg_clean.png',
} as const;

export const TRAIL_PORTRAIT_CONFIG: CertificateConfig = {
  ...DEFAULT_TRAIL_CONFIG,
  portrait: true,
  width: 1022,
  height: 1920,
} as const;
