{"name": "custom-sections", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/custom-sections/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/custom-sections", "main": "apps/custom-sections/src/main.ts", "tsConfig": "apps/custom-sections/tsconfig.app.json", "assets": ["apps/custom-sections/src/assets"], "webpackConfig": "apps/custom-sections/webpack.config.js", "generatePackageJson": true}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "custom-sections:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "custom-sections:build:development"}, "production": {"buildTarget": "custom-sections:build:production"}}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/custom-sections/jest.config.ts"}}, "build-migration-config": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "options": {"target": "node", "compiler": "tsc", "isolatedConfig": true, "webpackConfig": "apps/custom-sections/webpack.config.js", "outputPath": "dist/apps/typeorm-migration", "main": "apps/custom-sections/src/app/db/typeorm.config.ts", "tsConfig": "apps/custom-sections/tsconfig.app.json"}}, "typeorm-generate-migrations": {"executor": "nx:run-commands", "outputs": ["{options.outputPath}"], "options": {"cwd": "apps/custom-sections", "commands": ["npx typeorm migration:generate -d ../../dist/apps/typeorm-migration/main.js ./src/app/db/migrations/{args.migration-name}"]}, "dependsOn": ["build-migration-config"]}, "typeorm-run-migrations": {"executor": "nx:run-commands", "outputs": ["{options.outputPath}"], "options": {"cwd": "apps/custom-sections", "commands": ["npx typeorm -d ../../dist/apps/typeorm-migration/main.js migration:run"]}, "dependsOn": ["build-migration-config"]}, "sonar": {"executor": "@koliveira15/nx-sonarqube:scan", "options": {"name": "custom-sections", "hostUrl": "https://sonar.keepsdev.com", "projectKey": "custom-sections-api", "skipTargetDefaults": false, "branches": false, "qualityGate": true, "qualityGateTimeout": "300", "skipImplicitDeps": false, "exclusions": "apps/custom-sections/src/app/db/**,apps/custom-sections/src/app/config/**,apps/custom-sections/src/assets/**,libs/**", "extra": {"sonar.coverage.exclusions": "apps/custom-sections/src/app/db/**, apps/custom-sections/src/app/config/**, apps/custom-sections/src/**/*.module.ts, apps/custom-sections/src/main.ts, apps/custom-sections/src/**/index.ts", "sonar.cpd.exclusions": "apps/custom-sections/src/app/db/**, apps/custom-sections/src/assets, apps/custom-sections/src/app/entities/**", "sonar.testExecutionReportPaths": "coverage/custom-sections/jest-sonar.xml", "sonar.plugins.downloadOnlyRequired": "true"}}}}}