/* eslint-disable */
export default {
  displayName: 'custom-sections',
  preset: '../../jest.preset.js',
  testEnvironment: 'node',
  transform: {
    '^.+\\.[tj]s$': ['ts-jest', { tsconfig: '<rootDir>/tsconfig.spec.json' }],
  },
  moduleFileExtensions: ['ts', 'js', 'html'],
  coverageDirectory: '../../coverage/custom-sections',
  coverageReporters: ['text-summary', ['text', { skipFull: true }], ['lcovonly', { projectRoot: __dirname }]],
  reporters: ['default', ['@casualbot/jest-sonar-reporter', { outputDirectory: 'coverage/custom-sections' }]],
  coverageThreshold: {
    global: {
      branches: 0,
      functions: 0,
      lines: 0,
      statements: 0,
    },
  },
};
