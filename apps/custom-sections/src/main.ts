import {
  CaseSerializeInterceptor,
  DEFAULT_STRATEGY,
  DomainExceptionFilter,
  KeepsErrorFilter,
  NotAllowedErrorFilter,
  TypeOrmErrorFilter,
} from '@keeps-node-apis/@core';
import { ClassSerializerInterceptor, Logger, ValidationPipe } from '@nestjs/common';
import { NestFactory, Reflector } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as bodyParser from 'body-parser';
import { AppModule } from './app/app.module';
import { MicroserviceOptions } from '@nestjs/microservices';
import { CUSTOM_SECTIONS_GRPC_OPTIONS } from './app/custom-sections-grpc-options';

async function bootstrap() {
  const isProduction = process.env.NODE_ENV === 'production';

  const app = await NestFactory.create(AppModule, {
    logger: isProduction ? ['error', 'warn', 'log'] : ['log', 'error', 'warn', 'debug', 'verbose'],
  });

  // OpenAPI (Swagger) configuration
  const config = new DocumentBuilder()
    .setTitle('Custom Sections')
    .setDescription('Custom Sections Microservice allows you to create custom sections for the platform.')
    .setVersion('1.0')
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('docs', app, document);

  // Global Validations
  app.useGlobalPipes(new ValidationPipe({ whitelist: true, transform: true, forbidNonWhitelisted: true }));
  app.useGlobalInterceptors(new CaseSerializeInterceptor(DEFAULT_STRATEGY, ['filters']));
  app.useGlobalInterceptors(
    new ClassSerializerInterceptor(app.get(Reflector), {
      strategy: 'excludeAll',
    }),
  );

  // Exception filters
  app.useGlobalFilters(
    new KeepsErrorFilter(),
    new NotAllowedErrorFilter(),
    new TypeOrmErrorFilter(),
    new DomainExceptionFilter(),
  );

  // Body parser
  app.use(bodyParser.json());
  app.use(bodyParser.urlencoded({ extended: true }));

  // CORS
  app.enableCors({
    allowedHeaders: '*',
    origin: '*',
    credentials: true,
  });

  // Microservices
  app.connectMicroservice<MicroserviceOptions>(CUSTOM_SECTIONS_GRPC_OPTIONS);
  await app.startAllMicroservices();
  const port = process.env.PORT || 3000;
  await app.listen(port);

  Logger.log(`🚀 Application is running on: http://localhost:${port}`);
  Logger.log(`📝 Application Docs is running on: http://localhost:${port}/docs`);
}

bootstrap();
