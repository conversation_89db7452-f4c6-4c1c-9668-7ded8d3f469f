import { BaseEntity } from '@keeps-node-apis/@core';
import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { LearningObjectType } from '../@core/constants';

@Entity('section')
export class Section extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'timestamptz', nullable: true, name: 'start_date' })
  startDate: Date;

  @Column({ type: 'timestamptz', nullable: true, name: 'end_date' })
  endDate: Date;

  @Column({ type: 'uuid', name: 'workspace_id' })
  workspaceId: string;

  @Column({ name: 'filters', type: 'json', nullable: true })
  filters: { [key: string]: any };

  @Column()
  title: string;

  @Column({ nullable: true })
  description: string;

  @Column('enum', {
    enumName: 'learning_object_type',
    enum: LearningObjectType,
  })
  learningObjectType: LearningObjectType;

  @Column({ name: 'order', default: 0 })
  order: number;
}
