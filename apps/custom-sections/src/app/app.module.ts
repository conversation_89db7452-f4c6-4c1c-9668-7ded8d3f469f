import { Modu<PERSON> } from '@nestjs/common';

import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { KeycloakConnectModule } from 'nest-keycloak-connect';
import { AuthConfig, configOptions, DatabaseConfig } from './config';
import { APP_PROVIDERS } from './app.providers';
import { AuthModule } from '@keeps-node-apis/@core';
import { SectionsModule } from './section/sections.module';
import { I18nModule } from 'nestjs-i18n';
import { CommonModule } from './@core/common/common.module';
import { ScheduleModule } from '@nestjs/schedule';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { I18nConfigOptions } from './config/modules/i18n.config';

@Module({
  imports: [
    I18nModule.forRoot(I18nConfigOptions),
    ConfigModule.forRoot(configOptions),
    TypeOrmModule.forRootAsync(DatabaseConfig.asProvider()),
    KeycloakConnectModule.registerAsync(AuthConfig.asProvider()),
    AuthModule.registerAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        return { authAppId: configService.get('AUTH_APPLICATION_ID') };
      },
    }),
    SectionsModule,
    CommonModule,
    ScheduleModule.forRoot(),
    EventEmitterModule.forRoot(),
  ],
  controllers: [],
  providers: APP_PROVIDERS,
})
export class AppModule {}
