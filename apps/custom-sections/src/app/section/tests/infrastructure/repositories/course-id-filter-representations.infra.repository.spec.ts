import { CourseIdFilterRepresentationsInfraRepository } from '../../../infrastructure/repositories/course-id-filter-representations.infra.repository';
import { KonquestCoursesService } from '@keeps-node-apis/konquest';
import { of } from 'rxjs';

describe('CourseIdFilterRepresentationsInfraRepository', () => {
  let repository: CourseIdFilterRepresentationsInfraRepository;
  let konquestCoursesService: jest.Mocked<KonquestCoursesService>;

  beforeEach(() => {
    konquestCoursesService = {
      findAll: jest.fn(),
    } as any;

    repository = new CourseIdFilterRepresentationsInfraRepository(konquestCoursesService);
  });

  it('should return correct representations when all values are found', async () => {
    const values = ['course-1', 'course-2'];
    const mockResults = [
      { id: 'course-1', name: 'Course One' },
      { id: 'course-2', name: 'Course Two' },
    ];

    konquestCoursesService.findAll.mockReturnValue(of({ results: mockResults } as any));

    const result = await repository.getRepresentationsByFilterValues(values, 'workspace-123', 'token-abc');

    expect(konquestCoursesService.findAll).toHaveBeenCalledWith({
      query: { id: 'course-1,course-2' },
      workspace_id: 'workspace-123',
      token: 'token-abc',
    });

    expect(result).toEqual([
      { filterValue: 'course-1', value: 'Course One' },
      { filterValue: 'course-2', value: 'Course Two' },
    ]);
  });

  it('should return null for values not found in the response', async () => {
    const values = ['course-1', 'course-2'];
    const mockResults = [{ id: 'course-1', name: 'Course One' }];

    konquestCoursesService.findAll.mockReturnValue(of({ results: mockResults } as any));

    const result = await repository.getRepresentationsByFilterValues(values);

    expect(result).toEqual([
      { filterValue: 'course-1', value: 'Course One' },
      { filterValue: 'course-2', value: null },
    ]);
  });

  it('should return an empty array if no values provided', async () => {
    konquestCoursesService.findAll.mockReturnValue(of({ results: [] } as any));

    const result = await repository.getRepresentationsByFilterValues([]);

    expect(konquestCoursesService.findAll).toHaveBeenCalledWith({
      query: { id: '' },
      workspace_id: undefined,
      token: undefined,
    });

    expect(result).toEqual([]);
  });
});
