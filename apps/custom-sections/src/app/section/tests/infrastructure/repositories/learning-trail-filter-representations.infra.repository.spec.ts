import { LearningTrailFilterRepresentationsInfraRepository } from '../../../infrastructure/repositories/learning-trail-filter-representations.infra.repository';
import { KonquestLearningTrailsService } from '@keeps-node-apis/konquest';
import { of } from 'rxjs';

describe('LearningTrailFilterRepresentationsInfraRepository', () => {
  let repository: LearningTrailFilterRepresentationsInfraRepository;
  let konquestLearningTrailsService: jest.Mocked<KonquestLearningTrailsService>;

  beforeEach(() => {
    konquestLearningTrailsService = {
      findAll: jest.fn(),
    } as any;

    repository = new LearningTrailFilterRepresentationsInfraRepository(konquestLearningTrailsService);
  });

  it('should return correct representations when all values are found', async () => {
    const mockValues = ['id1', 'id2'];
    const mockResults = [
      { id: 'id1', name: 'Trail 1' },
      { id: 'id2', name: 'Trail 2' },
    ];

    konquestLearningTrailsService.findAll.mockReturnValue(of({ results: mockResults } as any));

    const result = await repository.getRepresentationsByFilterValues(mockValues, 'workspace-1', 'token-123');

    expect(konquestLearningTrailsService.findAll).toHaveBeenCalledWith({
      query: { id: 'id1,id2' },
      workspace_id: 'workspace-1',
      token: 'token-123',
    });

    expect(result).toEqual([
      { filterValue: 'id1', value: 'Trail 1' },
      { filterValue: 'id2', value: 'Trail 2' },
    ]);
  });

  it('should return null values for unmatched ids', async () => {
    const mockValues = ['id1', 'id2', 'id3'];
    const mockResults = [{ id: 'id1', name: 'Trail 1' }];

    konquestLearningTrailsService.findAll.mockReturnValue(of({ results: mockResults } as any));

    const result = await repository.getRepresentationsByFilterValues(mockValues);

    expect(result).toEqual([
      { filterValue: 'id1', value: 'Trail 1' },
      { filterValue: 'id2', value: null },
      { filterValue: 'id3', value: null },
    ]);
  });

  it('should handle empty input gracefully', async () => {
    konquestLearningTrailsService.findAll.mockReturnValue(of({ results: [] } as any));

    const result = await repository.getRepresentationsByFilterValues([]);

    expect(konquestLearningTrailsService.findAll).toHaveBeenCalledWith({
      query: { id: '' },
      workspace_id: undefined,
      token: undefined,
    });

    expect(result).toEqual([]);
  });
});
