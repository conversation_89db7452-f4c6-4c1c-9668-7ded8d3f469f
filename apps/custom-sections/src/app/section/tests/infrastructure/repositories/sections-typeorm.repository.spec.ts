import { Test, TestingModule } from '@nestjs/testing';
import { SectionsTypeORMRepository } from '../../../infrastructure/repositories/sections-typeorm.repository';
import { Repository } from 'typeorm';
import { Section as SectionORM } from '../../../../entities/section.entity';
import { Section } from '../../../domain/entities/section';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Chance } from 'chance';
import { LearningObjectType } from '../../../../@core/constants';
import { paginate } from 'nestjs-paginate';

jest.mock('nestjs-paginate', () => {
  return {
    FilterOperator: {
      IN: 'in',
      EQ: 'eq',
    },
    paginate: jest.fn(),
  };
});

describe('SectionsTypeORMRepository', () => {
  let repository: SectionsTypeORMRepository;
  let ormRepo: jest.Mocked<Repository<SectionORM>>;
  const chance = new Chance();
  const startDate = new Date();
  startDate.setDate(startDate.getDate() + 7);
  const endDate = new Date();
  endDate.setDate(endDate.getDate() + 14);

  const sectionDomain = new Section({
    id: chance.guid(),
    workspaceId: chance.guid(),
    title: chance.string(),
    description: chance.string(),
    filters: { tag: chance.string() },
    learningObjectType: LearningObjectType.COURSE,
    startDate: startDate,
    endDate: endDate,
    order: 1,
  });

  const sectionEntity: SectionORM = {
    ...sectionDomain,
    id: sectionDomain.id,
    createdDate: undefined,
    updatedDate: undefined,
    genarate: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SectionsTypeORMRepository,
        {
          provide: getRepositoryToken(SectionORM),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            findOne: jest.fn(),
            findOneBy: jest.fn(),
            delete: jest.fn(),
          },
        },
      ],
    }).compile();

    repository = module.get(SectionsTypeORMRepository);
    ormRepo = module.get(getRepositoryToken(SectionORM));
  });

  it('should save a section', async () => {
    ormRepo.create.mockReturnValue(sectionEntity);
    ormRepo.save.mockResolvedValue(sectionEntity);

    await expect(repository.save(sectionDomain)).resolves.toBeUndefined();
    expect(ormRepo.create).toHaveBeenCalledWith(expect.objectContaining({ id: sectionDomain.id }));
    expect(ormRepo.save).toHaveBeenCalledWith(sectionEntity);
  });

  it('should find a section by ID', async () => {
    ormRepo.findOne.mockResolvedValue(sectionEntity);

    const result = await repository.findById(sectionDomain.id);
    expect(result).toBeInstanceOf(Section);
    expect(result?.id).toBe(sectionDomain.id);
    expect(ormRepo.findOne).toHaveBeenCalledWith({ where: { id: sectionDomain.id } });
  });

  it('should return null if section not found', async () => {
    ormRepo.findOne.mockResolvedValue(null);

    const result = await repository.findById('non-existent');
    expect(result).toBeNull();
  });

  it('should update a section', async () => {
    ormRepo.findOneBy.mockResolvedValue({ ...(sectionEntity as any) });
    ormRepo.save.mockResolvedValue(sectionEntity);

    const result = await repository.update(sectionDomain);
    expect(result).toBeInstanceOf(Section);
    expect(result.id).toBe(sectionDomain.id);
    expect(ormRepo.findOneBy).toHaveBeenCalledWith({ id: sectionDomain.id });
    expect(ormRepo.save).toHaveBeenCalledWith(expect.objectContaining({ id: sectionDomain.id }));
  });

  it('should delete a section by ID', async () => {
    ormRepo.delete.mockResolvedValue(undefined as any);

    await repository.delete(sectionDomain.id);
    expect(ormRepo.delete).toHaveBeenCalledWith(sectionDomain.id);
  });

  it('should find all sections by workspace with filters and map to domain', async () => {
    const workspaceId = sectionDomain.workspaceId;
    const filters = { learningObjectType: [LearningObjectType.COURSE] };

    const mockPaginateResult = {
      data: [sectionEntity],
      meta: {
        totalItems: 1,
        itemCount: 1,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1,
      },
      links: {},
    };

    (paginate as jest.Mock).mockResolvedValue(mockPaginateResult);

    const result = await repository.findAllByWorkspace(workspaceId, filters);

    expect(result).toEqual([expect.any(Section)]);
    expect(result[0].id).toBe(sectionDomain.id);
  });
});
