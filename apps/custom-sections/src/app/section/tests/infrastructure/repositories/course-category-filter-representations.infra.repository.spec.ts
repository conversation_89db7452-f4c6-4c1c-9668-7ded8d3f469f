import { CourseCategoryFilterRepresentationsInfraRepository } from '../../../infrastructure/repositories/course-category-filter-representations.infra.repository';
import { KonquestCoursesService } from '@keeps-node-apis/konquest';
import { of } from 'rxjs';

describe('CourseCategoryFilterRepresentationsInfraRepository', () => {
  let repository: CourseCategoryFilterRepresentationsInfraRepository;
  let konquestCoursesService: jest.Mocked<KonquestCoursesService>;

  beforeEach(() => {
    konquestCoursesService = {
      findAllCoursesCategories: jest.fn(),
    } as any;

    repository = new CourseCategoryFilterRepresentationsInfraRepository(konquestCoursesService);
  });

  it('should return correct representations when all category IDs are found', async () => {
    const values = ['cat-1', 'cat-2'];
    const mockResults = [
      { id: 'cat-1', name: 'Category One' },
      { id: 'cat-2', name: 'Category Two' },
    ];

    konquestCoursesService.findAllCoursesCategories.mockReturnValue(of({ results: mockResults } as any));

    const result = await repository.getRepresentationsByFilterValues(values, 'workspace-1', 'token-xyz');

    expect(konquestCoursesService.findAllCoursesCategories).toHaveBeenCalledWith({
      query: { id: 'cat-1,cat-2' },
      workspace_id: 'workspace-1',
      token: 'token-xyz',
    });

    expect(result).toEqual([
      { filterValue: 'cat-1', value: 'Category One' },
      { filterValue: 'cat-2', value: 'Category Two' },
    ]);
  });

  it('should return null for category IDs not found in the response', async () => {
    const values = ['cat-1', 'cat-2'];
    const mockResults = [{ id: 'cat-1', name: 'Category One' }];

    konquestCoursesService.findAllCoursesCategories.mockReturnValue(of({ results: mockResults } as any));

    const result = await repository.getRepresentationsByFilterValues(values);

    expect(result).toEqual([
      { filterValue: 'cat-1', value: 'Category One' },
      { filterValue: 'cat-2', value: null },
    ]);
  });

  it('should return an empty array if no category IDs are provided', async () => {
    konquestCoursesService.findAllCoursesCategories.mockReturnValue(of({ results: [] } as any));

    const result = await repository.getRepresentationsByFilterValues([]);

    expect(konquestCoursesService.findAllCoursesCategories).toHaveBeenCalledWith({
      query: { id: '' },
      workspace_id: undefined,
      token: undefined,
    });

    expect(result).toEqual([]);
  });
});
