import { Section } from '../../../domain/entities/section';
import { LearningObjectType } from '../../../../@core/constants';
import {
  EndDateBeforeStartDateException,
  StartDateInPastException,
} from '../../../domain/exceptions/sections.exception';

describe('Section Entity', () => {
  const baseProps = {
    workspaceId: 'ws-1',
    title: 'Test Section',
    description: 'A section for testing',
    filters: { tag: 'test' },
    learningObjectType: LearningObjectType.COURSE,
    order: 1,
  };

  const futureDate = (daysAhead: number) => {
    const date = new Date();
    date.setDate(date.getDate() + daysAhead);
    return date;
  };

  it('should create a section successfully with valid dates', () => {
    const startDate = futureDate(1);
    const endDate = futureDate(5);

    const section = new Section({
      id: 'section-1',
      ...baseProps,
      startDate,
      endDate,
    });

    section.validateDates(startDate, endDate); // Explicit validation

    expect(section).toBeInstanceOf(Section);
    expect(section.id).toBe('section-1');
    expect(section.startDate).toEqual(startDate);
    expect(section.endDate).toEqual(endDate);
  });

  it('should throw EndDateBeforeStartDateException if endDate is before startDate', () => {
    const startDate = futureDate(5);
    const endDate = futureDate(1);

    const section = new Section({
      ...baseProps,
      startDate,
      endDate,
      id: 'section-4',
    });

    expect(() => section.validateDates(startDate, endDate)).toThrow(EndDateBeforeStartDateException);
  });

  it('should create section without dates', () => {
    const section = new Section({
      ...baseProps,
      startDate: null,
      endDate: null,
      id: 'section-6',
    });

    expect(section.startDate).toBeNull();
    expect(section.endDate).toBeNull();
  });
});
