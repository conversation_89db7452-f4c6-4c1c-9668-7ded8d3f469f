import { SectionGroup } from '../../../domain/entities/section-group';
import { Section } from '../../../domain/entities/section';
import { LearningObjectType } from '../../../../@core/constants';

describe('SectionGroup', () => {
  const mockSection = (id: string, learningObjectType: LearningObjectType, order: number): Section => {
    const section = new Section({
      id,
      workspaceId: 'workspace-1',
      title: '',
      description: '',
      filters: {},
      learningObjectType,
      startDate: null,
      endDate: null,
      order,
    });
    return section;
  };

  describe('group()', () => {
    it('should group sections by learningObjectTypeGroup', () => {
      const s1 = mockSection('1', LearningObjectType.COURSE_ALL, 1);
      const s2 = mockSection('2', LearningObjectType.COURSE_ALL, 2);
      const s3 = mockSection('3', LearningObjectType.LEARNING_TRAIL, 3);

      const groups = SectionGroup.group([s1, s2, s3]);

      expect(groups.length).toBe(2);
      expect(groups.find((g) => g.typeGroup === 'COURSE')?.sections.length).toBe(2);
      expect(groups.find((g) => g.typeGroup === LearningObjectType.LEARNING_TRAIL)?.sections.length).toBe(1);
    });
  });

  describe('reorder()', () => {
    it('should reorder sections based on current order', () => {
      const s1 = mockSection('1', LearningObjectType.LEARNING_TRAIL, 3);
      const s2 = mockSection('2', LearningObjectType.LEARNING_TRAIL, 1);
      const s3 = mockSection('3', LearningObjectType.LEARNING_TRAIL, 2);

      const group = new SectionGroup(LearningObjectType.LEARNING_TRAIL, [s1, s2, s3]);
      group.reorder();

      expect(group.sections.map((s) => s.id)).toEqual(['2', '3', '1']);
      expect(group.sections.map((s) => s.order)).toEqual([1, 2, 3]);
    });
  });

  describe('reorderByIds()', () => {
    it('should reorder sections according to the provided ID list', () => {
      const s1 = mockSection('1', LearningObjectType.LEARNING_TRAIL, 1);
      const s2 = mockSection('2', LearningObjectType.LEARNING_TRAIL, 2);
      const s3 = mockSection('3', LearningObjectType.LEARNING_TRAIL, 3);

      const group = new SectionGroup(LearningObjectType.LEARNING_TRAIL, [s1, s2, s3]);
      group.reorderByIds(['3', '1', '2']);

      expect(group.sections.map((s) => s.id)).toEqual(['3', '1', '2']);
      expect(group.sections.map((s) => s.order)).toEqual([1, 2, 3]);
    });

    it('should ignore IDs not found in the group', () => {
      const s1 = mockSection('1', LearningObjectType.LEARNING_TRAIL, 1);
      const s2 = mockSection('2', LearningObjectType.LEARNING_TRAIL, 2);

      const group = new SectionGroup(LearningObjectType.LEARNING_TRAIL, [s1, s2]);
      group.reorderByIds(['2', 'unknown', '1']);

      expect(group.sections.map((s) => s.id)).toEqual(['2', '1']);
      expect(group.sections.map((s) => s.order)).toEqual([1, 2]);
    });
  });

  describe('getSectionsToUpdate()', () => {
    it('should return the current list of sections', () => {
      const s1 = mockSection('1', LearningObjectType.LEARNING_TRAIL, 1);
      const group = new SectionGroup(LearningObjectType.LEARNING_TRAIL, [s1]);

      expect(group.getSectionsToUpdate()).toEqual([s1]);
    });
  });
});
