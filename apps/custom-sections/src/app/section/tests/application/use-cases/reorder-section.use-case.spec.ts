import { ReorderSectionsUseCase } from '../../../application/use-cases/reorder-sections.use-case';
import { SectionsRepository } from '../../../domain/repositories/sections-repository';
import { Section } from '../../../domain/entities/section';
import { SectionAccessDeniedException } from '../../../domain/exceptions/sections.exception';
import { LearningObjectType } from '../../../../@core/constants';

describe('ReorderSectionsUseCase', () => {
  let useCase: ReorderSectionsUseCase;
  let repo: jest.Mocked<SectionsRepository>;

  beforeEach(() => {
    repo = {
      findAllByWorkspace: jest.fn(),
      update: jest.fn(),
    } as any;

    useCase = new ReorderSectionsUseCase(repo);
  });

  it('should reorder sections and update them correctly', async () => {
    const workspaceId = 'workspace-123';
    const sectionA = new Section({
      id: 'a',
      workspaceId,
      title: '',
      description: '',
      filters: {},
      learningObjectType: LearningObjectType.COURSE,
      startDate: null,
      endDate: null,
      order: 2,
    });
    const sectionB = new Section({
      id: 'b',
      workspaceId,
      title: '',
      description: '',
      filters: {},
      learningObjectType: LearningObjectType.COURSE,
      startDate: null,
      endDate: null,
      order: 1,
    });
    const sectionC = new Section({
      id: 'c',
      workspaceId,
      title: '',
      description: '',
      filters: {},
      learningObjectType: LearningObjectType.COURSE_ENROLLED,
      startDate: null,
      endDate: null,
      order: 3,
    });

    const inputIds = ['b', 'a', 'c'];
    repo.findAllByWorkspace.mockResolvedValue([sectionA, sectionB, sectionC]);

    await useCase.execute({ ids: inputIds, workspaceId });

    expect(repo.findAllByWorkspace).toHaveBeenCalledWith(workspaceId);

    expect(sectionB.order).toBe(1);
    expect(sectionA.order).toBe(2);
    expect(sectionC.order).toBe(3);

    expect(repo.update).toHaveBeenCalledTimes(3);
    expect(repo.update).toHaveBeenCalledWith(sectionB);
    expect(repo.update).toHaveBeenCalledWith(sectionA);
    expect(repo.update).toHaveBeenCalledWith(sectionC);
  });

  it('should throw SectionAccessDeniedException if ID not found in workspace', async () => {
    const workspaceId = 'workspace-456';
    const section = new Section({
      id: 'a',
      workspaceId,
      title: '',
      description: '',
      filters: {},
      learningObjectType: LearningObjectType.COURSE_ENROLLED,
      startDate: null,
      endDate: null,
      order: 1,
    });

    repo.findAllByWorkspace.mockResolvedValue([section]);

    const invalidId = 'not-found-id';

    await expect(useCase.execute({ ids: ['a', invalidId], workspaceId })).rejects.toThrow(SectionAccessDeniedException);

    expect(repo.update).not.toHaveBeenCalled();
  });
});
