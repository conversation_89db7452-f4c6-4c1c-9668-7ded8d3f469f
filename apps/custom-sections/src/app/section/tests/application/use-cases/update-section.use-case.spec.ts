import { UpdateSectionUseCase } from '../../../application/use-cases/update-new-section.use-case';
import { SectionsRepository } from '../../../domain/repositories/sections-repository';
import { UpdateSectionInput } from '../../../application/dtos/update-section-input.dto';
import { Section } from '../../../domain/entities/section';
import { SectionNotFoundException } from '../../../domain/exceptions/sections.exception';
import { Chance } from 'chance';
import { LearningObjectType } from '../../../../@core/constants';

const chance = new Chance();

describe('UpdateSectionUseCase', () => {
  let useCase: UpdateSectionUseCase;
  let repository: jest.Mocked<SectionsRepository>;

  const existingStartDate = new Date('2025-01-01');
  const existingEndDate = new Date('2025-12-31');

  const baseSection = new Section({
    id: chance.guid(),
    workspaceId: 'workspace-123',
    title: 'Old Title',
    description: 'Old Description',
    filters: { some: 'filter' },
    startDate: existingStartDate,
    endDate: existingEndDate,
    learningObjectType: LearningObjectType.COURSE_ALL,
    order: 1,
  });

  beforeEach(() => {
    repository = {
      save: jest.fn(),
      update: jest.fn(),
      findById: jest.fn(),
      delete: jest.fn(),
      findAllByWorkspace: jest.fn(),
    } as unknown as jest.Mocked<SectionsRepository>;

    useCase = new UpdateSectionUseCase(repository);
  });

  it('should update a section with new title and preserve dates when not provided', async () => {
    const input: UpdateSectionInput = {
      id: baseSection.id,
      workspaceId: baseSection.workspaceId,
      timeZone: 'UTC',
      title: 'New Title',
    };

    repository.findById.mockResolvedValue(baseSection);
    repository.update.mockImplementation(async (section) => section);

    const result = await useCase.execute(input);

    expect(result.title).toBe('New Title');
    expect(result.startDate).toEqual(existingStartDate);
    expect(result.endDate).toEqual(existingEndDate);
    expect(repository.update).toHaveBeenCalledWith(
      expect.objectContaining({
        title: 'New Title',
        startDate: existingStartDate,
        endDate: existingEndDate,
      }),
    );
  });

  it('should update startDate and endDate when explicitly provided', async () => {
    const newStart = '2025-02-01T00:00:00.000Z';
    const newEnd = '2025-03-01T00:00:00.000Z';

    const input: UpdateSectionInput = {
      id: baseSection.id,
      workspaceId: baseSection.workspaceId,
      timeZone: 'UTC',
      startDate: newStart,
      endDate: newEnd,
    };

    repository.findById.mockResolvedValue(baseSection);
    repository.update.mockImplementation(async (section) => section);

    const result = await useCase.execute(input);

    expect(result.startDate?.toISOString()).toBe(newStart);
    expect(result.endDate?.toISOString()).toBe(newEnd);
  });

  it('should clear startDate and endDate when explicitly set to null', async () => {
    const input: UpdateSectionInput = {
      id: baseSection.id,
      workspaceId: baseSection.workspaceId,
      timeZone: 'UTC',
      startDate: null,
      endDate: null,
    };

    repository.findById.mockResolvedValue(baseSection);
    repository.update.mockImplementation(async (section) => section);

    const result = await useCase.execute(input);

    expect(result.startDate).toBeNull();
    expect(result.endDate).toBeNull();
  });

  it('should throw SectionNotFoundException if section does not exist', async () => {
    repository.findById.mockResolvedValue(null);

    const input: UpdateSectionInput = {
      id: 'nonexistent',
      workspaceId: 'workspace-123',
      timeZone: 'UTC',
    };

    await expect(useCase.execute(input)).rejects.toThrow(SectionNotFoundException);
  });

  it('should throw SectionNotFoundException if workspace does not match', async () => {
    const input: UpdateSectionInput = {
      id: baseSection.id,
      workspaceId: 'other-workspace',
      timeZone: 'UTC',
    };

    repository.findById.mockResolvedValue(baseSection);

    await expect(useCase.execute(input)).rejects.toThrow(SectionNotFoundException);
  });

  it('should validate new dates if provided', async () => {
    const input: UpdateSectionInput = {
      id: baseSection.id,
      workspaceId: baseSection.workspaceId,
      timeZone: 'UTC',
      startDate: '2025-10-01T00:00:00.000Z',
      endDate: '2025-09-01T00:00:00.000Z',
    };

    repository.findById.mockResolvedValue(baseSection);

    await expect(useCase.execute(input)).rejects.toThrow();
  });

  it('should update the dates when isTemporary is updated to false', async () => {
    const input: UpdateSectionInput = {
      id: baseSection.id,
      workspaceId: baseSection.workspaceId,
      timeZone: 'UTC',
      title: 'New Title',
      isTemporary: false, // Set to false to clear dates
    };

    repository.findById.mockResolvedValue(baseSection);
    repository.update.mockImplementation(async (section) => section);

    const result = await useCase.execute(input);

    expect(result.startDate).toEqual(null);
    expect(result.endDate).toEqual(null);
    expect(repository.update).toHaveBeenCalledWith(
      expect.objectContaining({
        startDate: null,
        endDate: null,
      }),
    );
  });
});
