import { DeleteSectionUseCase } from '../../../application/use-cases/delete-section.use-case';
import { SectionsRepository } from '../../../domain/repositories/sections-repository';
import { SectionNotFoundException, SectionAccessDeniedException } from '../../../domain/exceptions/sections.exception';
import { Section } from '../../../domain/entities/section';
import { Chance } from 'chance';
import { EventsEmitter } from '../../../../@core/events/events-emmiter';

const chance = new Chance();

describe('DeleteSectionUseCase', () => {
  let useCase: DeleteSectionUseCase;
  let repository: Partial<jest.Mocked<SectionsRepository>>;
  let eventsEmitterRepository: Partial<jest.Mocked<EventsEmitter>>;

  beforeEach(() => {
    repository = {
      findById: jest.fn(),
      delete: jest.fn(),
      save: jest.fn(),
      update: jest.fn(),
    };
    eventsEmitterRepository = {
      emit: jest.fn(),
    };

    useCase = new DeleteSectionUseCase(repository as SectionsRepository, eventsEmitterRepository as EventsEmitter);
  });

  it('should delete the section if found and workspaceId matches', async () => {
    const id = chance.guid();
    const workspaceId = 'workspace-1';

    const section = {
      id,
      workspaceId,
    } as Section;

    repository.findById.mockResolvedValue(section);

    await useCase.execute({ id, workspaceId });

    expect(repository.findById).toHaveBeenCalledWith(id);
    expect(repository.delete).toHaveBeenCalledWith(id);
  });

  it('should throw SectionNotFoundException if section does not exist', async () => {
    const id = 'non-existent';
    const workspaceId = 'workspace-1';

    repository.findById.mockResolvedValue(null);

    await expect(useCase.execute({ id, workspaceId })).rejects.toThrow(SectionNotFoundException);
  });

  it('should throw SectionAccessDeniedException if workspaceId does not match', async () => {
    const id = chance.guid();

    repository.findById.mockResolvedValue({
      id,
      workspaceId: 'workspace-A',
    } as Section);

    await expect(useCase.execute({ id, workspaceId: 'workspace-B' })).rejects.toThrow(SectionAccessDeniedException);
  });
});
