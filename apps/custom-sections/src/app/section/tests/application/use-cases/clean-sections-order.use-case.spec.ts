import { CleanSectionsOrderUseCase } from '../../../application/use-cases/clean-sections-order.use-case';
import { SectionsRepository } from '../../../domain/repositories/sections-repository';
import { Section } from '../../../domain/entities/section';
import { LearningObjectType } from '../../../../@core/constants';

describe('CleanSectionsOrderUseCase', () => {
  let useCase: CleanSectionsOrderUseCase;
  let repo: jest.Mocked<SectionsRepository>;

  beforeEach(() => {
    repo = {
      findAllByWorkspace: jest.fn(),
      updateMany: jest.fn(),
    } as any;

    useCase = new CleanSectionsOrderUseCase(repo);
  });

  it('should reorder sections by group and persist updates', async () => {
    const workspaceId = 'workspace-1';

    const section1 = new Section({
      id: '1',
      workspaceId,
      title: 's1',
      description: '',
      filters: {},
      learningObjectType: LearningObjectType.COURSE_ALL,
      startDate: null,
      endDate: null,
      order: 1,
    });
    const section2 = new Section({
      id: '2',
      workspaceId,
      title: 's2',
      description: '',
      filters: {},
      learningObjectType: LearningObjectType.COURSE_ALL,
      startDate: null,
      endDate: null,
      order: 2,
    });
    const section3 = new Section({
      id: '3',
      workspaceId,
      title: 's3',
      description: '',
      filters: {},
      learningObjectType: LearningObjectType.COURSE_ENROLLED,
      startDate: null,
      endDate: null,
      order: 4,
    });

    repo.findAllByWorkspace.mockResolvedValue([section1, section2, section3]);

    await useCase.execute(workspaceId);

    expect(section1.order).toBe(1);
    expect(section2.order).toBe(2);
    expect(section3.order).toBe(3); // Section3 had order 4, but was updated to 3 to eliminate gaps and ensure sequential ordering.

    expect(repo.updateMany).toHaveBeenCalledWith([section1, section2, section3]);
  });
});
