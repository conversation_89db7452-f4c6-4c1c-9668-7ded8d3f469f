import { ListSectionsUseCase } from '../../../application/use-cases/list-sections.use-case';
import { SectionsRepository } from '../../../domain/repositories/sections-repository';
import { Section } from '../../../domain/entities/section';
import { FilterRepresentationStrategyResolver } from '../../../application/strategies/filter-strategy.resolver';
import { CourseIdFilterStrategy } from '../../../application/strategies/course-id-filter.strategy';
import { LearningObjectType } from '../../../../@core/constants';

describe('ListSectionsUseCase', () => {
  let useCase: ListSectionsUseCase;
  let sectionRepository: jest.Mocked<SectionsRepository>;
  let strategyResolver: jest.Mocked<FilterRepresentationStrategyResolver>;
  let mockStrategy: jest.Mocked<CourseIdFilterStrategy>;

  const fakeCourseSection = new Section({
    id: 'section-id',
    workspaceId: 'workspace-id',
    title: 'Title',
    description: 'Description',
    filters: { ID: ['course-1', 'course-2'] },
    learningObjectType: LearningObjectType.COURSE,
    startDate: new Date('2025-01-01'),
    endDate: new Date('2025-12-31'),
    order: 1,
  });

  beforeEach(() => {
    sectionRepository = {
      save: jest.fn(),
      update: jest.fn(),
      findById: jest.fn(),
      delete: jest.fn(),
      findAllByWorkspace: jest.fn(),
    } as unknown as jest.Mocked<SectionsRepository>;

    mockStrategy = {
      getRepresentations: jest.fn(),
    } as unknown as jest.Mocked<CourseIdFilterStrategy>;

    strategyResolver = {
      resolveStrategy: jest.fn(),
    } as unknown as jest.Mocked<FilterRepresentationStrategyResolver>;

    useCase = new ListSectionsUseCase(sectionRepository, strategyResolver);
  });

  it('should return sections with enriched filter representations', async () => {
    sectionRepository.findAllByWorkspace.mockResolvedValue([fakeCourseSection]);

    mockStrategy.getRepresentations.mockResolvedValue([
      { filterKey: 'ID', filterValue: 'course-1', value: 'Course One' },
      { filterKey: 'ID', filterValue: 'course-2', value: 'Course Two' },
    ]);

    strategyResolver.resolveStrategy.mockReturnValue(mockStrategy);

    const result = await useCase.execute({
      workspaceId: 'workspace-id',
      userToken: 'token-xyz',
    });

    expect(sectionRepository.findAllByWorkspace).toHaveBeenCalledWith('workspace-id', undefined);
    expect(strategyResolver.resolveStrategy).toHaveBeenCalledWith('ID', LearningObjectType.COURSE);
    expect(result[0].filtersRepresentations).toEqual([
      { filterKey: 'ID', filterValue: 'course-1', value: 'Course One' },
      { filterKey: 'ID', filterValue: 'course-2', value: 'Course Two' },
    ]);
  });

  it('should handle unknown filter keys gracefully', async () => {
    const section = new Section({
      ...fakeCourseSection,
      filters: { UNKNOWN_FILTER: ['x'] },
    });

    sectionRepository.findAllByWorkspace.mockResolvedValue([section]);
    strategyResolver.resolveStrategy.mockReturnValue(undefined);

    const result = await useCase.execute({
      workspaceId: 'workspace-id',
      userToken: 'token-xyz',
    });

    expect(result[0].filtersRepresentations).toEqual([]);
  });

  it('should return empty array if no sections', async () => {
    sectionRepository.findAllByWorkspace.mockResolvedValue([]);
    const result = await useCase.execute({
      workspaceId: 'workspace-id',
      userToken: 'token-xyz',
    });

    expect(result).toEqual([]);
  });
});
