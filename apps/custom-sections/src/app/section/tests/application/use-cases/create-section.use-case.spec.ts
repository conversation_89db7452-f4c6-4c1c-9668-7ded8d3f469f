import { CreateSectionUseCase } from '../../../application/use-cases/create-new-section.use-case';
import { SectionsRepository } from '../../../domain/repositories/sections-repository';
import { CreateSectionInput } from '../../../application/dtos/create-section-input.dto';
import { Section } from '../../../domain/entities/section';
import { Chance } from 'chance';
import { LearningObjectType } from '../../../../@core/constants';

const chance = new Chance();

describe('CreateSectionUseCase', () => {
  let useCase: CreateSectionUseCase;
  let sectionRepository: Partial<jest.Mocked<SectionsRepository>>;
  const startDate = new Date();
  startDate.setDate(startDate.getDate() + 7);
  const endDate = new Date();
  endDate.setDate(endDate.getDate() + 14);

  beforeEach(() => {
    sectionRepository = {
      save: jest.fn(),
      update: jest.fn(),
      findById: jest.fn(),
      delete: jest.fn(),
    };

    useCase = new CreateSectionUseCase(sectionRepository as SectionsRepository);
  });

  it('should create and save a new section', async () => {
    const input: CreateSectionInput = {
      id: chance.guid(),
      workspaceId: chance.guid(),
      title: chance.string(),
      description: chance.sentence(),
      filters: { level: 'beginner' },
      learningObjectType: LearningObjectType.COURSE,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      order: 1,
      timeZone: 'America/Sao_Paulo',
    };

    const expectedSection = new Section({
      id: input.id,
      workspaceId: input.workspaceId,
      title: input.title,
      description: input.description,
      filters: input.filters,
      learningObjectType: input.learningObjectType,
      startDate: new Date(input.startDate),
      endDate: new Date(input.endDate),
      order: input.order,
    });

    (sectionRepository.save as jest.Mock).mockResolvedValue(expectedSection);

    const result = await useCase.execute(input);

    expect(sectionRepository.save).toHaveBeenCalledWith(expect.any(Section));
    expect(result).toEqual(expectedSection);
  });

  it('should create and save a new section not temporary', async () => {
    const input: CreateSectionInput = {
      id: chance.guid(),
      workspaceId: chance.guid(),
      title: chance.string(),
      description: chance.sentence(),
      filters: { level: 'beginner' },
      learningObjectType: LearningObjectType.COURSE,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(), // Both start and end dates provided
      order: 1,
      timeZone: 'America/Sao_Paulo',
      isTemporary: false, // Not temporary
    };

    const expectedSection = new Section({
      id: input.id,
      workspaceId: input.workspaceId,
      title: input.title,
      description: input.description,
      filters: input.filters,
      learningObjectType: input.learningObjectType,
      startDate: null,
      endDate: null, // Dates will be set to null in the entity if isTemporary is false
      order: input.order,
    });

    (sectionRepository.save as jest.Mock).mockResolvedValue(expectedSection);

    const result = await useCase.execute(input);

    expect(sectionRepository.save).toHaveBeenCalledWith(expect.any(Section));
    expect(result).toEqual(expectedSection);
  });
});
