import { ListAvailableSectionsUseCase } from '../../../application/use-cases/list-available-sections.use-case';
import { SectionsRepository } from '../../../domain/repositories/sections-repository';
import { Section } from '../../../domain/entities/section';
import { LearningObjectType } from '../../../../@core/constants';

describe('ListAvailableSectionsUseCase', () => {
  let useCase: ListAvailableSectionsUseCase;
  let sectionRepository: jest.Mocked<SectionsRepository>;

  const now = new Date();

  const mockSection = new Section({
    id: 'section-id-1',
    workspaceId: 'workspace-id',
    title: 'Available Section',
    description: 'Some description',
    filters: {},
    learningObjectType: LearningObjectType.COURSE,
    startDate: new Date(now.getTime() - 1000 * 60 * 60),
    endDate: new Date(now.getTime() + 1000 * 60 * 60),
    order: 1,
  });

  beforeEach(() => {
    sectionRepository = {
      findAllByWorkspace: jest.fn(),
      save: jest.fn(),
      update: jest.fn(),
      findById: jest.fn(),
      delete: jest.fn(),
    } as unknown as jest.Mocked<SectionsRepository>;

    useCase = new ListAvailableSectionsUseCase(sectionRepository);
  });

  it('should call repository with date filters and return sections', async () => {
    sectionRepository.findAllByWorkspace.mockResolvedValue([mockSection]);

    const result = await useCase.execute({
      workspaceId: 'workspace-id',
    });

    expect(sectionRepository.findAllByWorkspace).toHaveBeenCalledWith(
      'workspace-id',
      expect.objectContaining({
        endDate: expect.arrayContaining([expect.stringMatching(/^\$gte:/), '$or:$null']),
        startDate: expect.arrayContaining([expect.stringMatching(/^\$lte:/), '$or:$null']),
      }),
    );

    expect(result).toEqual([mockSection]);
  });

  it('should merge additional filters with availability filters', async () => {
    sectionRepository.findAllByWorkspace.mockResolvedValue([mockSection]);

    const filters = { learningObjectType: [LearningObjectType.COURSE] };

    const result = await useCase.execute({
      workspaceId: 'workspace-id',
      filters,
    });

    expect(sectionRepository.findAllByWorkspace).toHaveBeenCalledWith(
      'workspace-id',
      expect.objectContaining({
        learningObjectType: [LearningObjectType.COURSE],
        endDate: expect.any(Array),
        startDate: expect.any(Array),
      }),
    );

    expect(result).toEqual([mockSection]);
  });

  it('should return an empty array if no sections found', async () => {
    sectionRepository.findAllByWorkspace.mockResolvedValue([]);

    const result = await useCase.execute({
      workspaceId: 'workspace-id',
    });

    expect(result).toEqual([]);
  });
});
