import { Section } from '../entities/section';

export type SectionFilters = {
  learningObjectType?: string[];
  endDate?: string[];
};

export abstract class SectionsRepository {
  abstract save(section: Section): Promise<void>;
  abstract update(section: Section): Promise<Section>;
  abstract findById(id: string): Promise<Section | null>;
  abstract delete(id: string): Promise<void>;
  abstract findAllByWorkspace(workspaceId: string, filters?: SectionFilters): Promise<Section[]>;
  abstract findAllBy(filters?: SectionFilters): Promise<Section[]>;
  abstract findExpired(referenceDate: Date): Promise<Section[]>;
  abstract updateMany(sections: Section[]): Promise<void>;
}
