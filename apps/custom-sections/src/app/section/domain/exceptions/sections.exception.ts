import { DomainException } from '@keeps-node-apis/@core';

export class SectionsDomainException extends DomainException {
  constructor(message: string, errorCode: string, details?: Record<string, any>) {
    super(message, `CUSTOM_SECTIONS_${errorCode}`, details);
  }
}

export class StartDateWithoutEndDateException extends SectionsDomainException {
  constructor() {
    super('If startDate is provided, endDate must also be provided.', 'START_DATE_WITHOUT_END_DATE');
  }
}

export class EndDateWithoutStartDateException extends DomainException {
  constructor() {
    super('If endDate is provided, startDate must also be provided.', 'END_DATE_WITHOUT_START_DATE');
  }
}

export class EndDateBeforeStartDateException extends DomainException {
  constructor() {
    super('endDate must be greater than startDate.', 'END_DATE_BEFORE_START_DATE');
  }
}

export class StartDateInPastException extends SectionsDomainException {
  constructor() {
    super('startDate must not be in the past.', 'START_DATE_IN_PAST');
  }
}

export class SectionNotFoundException extends SectionsDomainException {
  constructor(sectionId: string) {
    super(`Section with ID ${sectionId} was not found.`, 'NOT_FOUND', { sectionId });
  }
}

export class SectionAccessDeniedException extends SectionsDomainException {
  constructor(sectionId: string, workspaceId: string) {
    super(`Access denied to section ${sectionId} for workspace ${workspaceId}.`, 'ACCESS_DENIED', {
      sectionId,
      workspaceId,
    });
  }
}
