import { LearningObjectType } from '../../../@core/constants';
import { EndDateBeforeStartDateException } from '../exceptions/sections.exception';
import { v4 as uuidv4 } from 'uuid';

export type SectionProps = {
  workspaceId: string;
  title: string;
  description: string | null;
  filters: Record<string, any> | null;
  learningObjectType: LearningObjectType;
  startDate: Date | null;
  endDate: Date | null;
  order: number;
  id?: string;
};

export interface FilterRepresentation {
  value?: string;
  filterValue?: string;
  filterKey?: string;
}

export class Section {
  public learningObjectTypeGroup: LearningObjectType;
  public readonly id?: string;
  public readonly workspaceId: string;
  public title: string;
  public description: string | null;
  public filters: Record<string, any> | null;
  public learningObjectType: LearningObjectType;
  public startDate: Date | null;
  public endDate: Date | null;
  public order: number;
  public filtersRepresentations: FilterRepresentation[];

  constructor(props: SectionProps) {
    this.id = props.id || uuidv4();
    this.learningObjectTypeGroup = props.learningObjectType.split('.')[0] as LearningObjectType;
    this.workspaceId = props.workspaceId;
    this.title = props.title;
    this.description = props.description;
    this.filters = props.filters;
    this.learningObjectType = props.learningObjectType;
    this.startDate = props.startDate;
    this.endDate = props.endDate;
    this.order = props.order;
    this.filtersRepresentations = [];
  }

  get enabled(): boolean {
    const now = new Date();
    return (
      (!this.endDate || (this.endDate && this.endDate > now)) &&
      (!this.startDate || (this.startDate && this.startDate <= now))
    );
  }

  get isTemporary(): boolean {
    return !!(this.startDate || this.endDate);
  }

  validateDates(startDate: Date | null, endDate: Date | null): void {
    if (startDate && endDate && endDate <= startDate) {
      throw new EndDateBeforeStartDateException();
    }
  }
}
