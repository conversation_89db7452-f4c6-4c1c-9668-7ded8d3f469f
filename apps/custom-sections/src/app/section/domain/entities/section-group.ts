import { Section } from './section';

export class SectionGroup {
  sections: Section[];
  private readonly deletedSectionIds: string[];
  readonly typeGroup: string;

  constructor(typeGroup: string, sections: Section[]) {
    this.sections = sections;
    this.typeGroup = typeGroup;
    this.deletedSectionIds = [];
  }

  static group(sections: Section[]): SectionGroup[] {
    const map = new Map<string, Section[]>();

    for (const section of sections) {
      const { learningObjectTypeGroup } = section;

      if (!map.has(learningObjectTypeGroup)) map.set(learningObjectTypeGroup, []);
      map.get(learningObjectTypeGroup).push(section);
    }

    const result: SectionGroup[] = [];
    for (const [typeGroup, sectionList] of map.entries()) {
      result.push(new SectionGroup(typeGroup, sectionList));
    }

    return result;
  }

  reorder() {
    this.sections.sort((a, b) => a.order - b.order);
    this.sections.forEach((section, index) => {
      section.order = index + 1;
    });
  }

  getSectionsToUpdate(): Section[] {
    return this.sections;
  }

  reorderByIds(orderedIds: string[]) {
    const idToSection = new Map(this.sections.map((section) => [section.id, section]));

    this.sections.length = 0;
    for (let i = 0; i < orderedIds.length; i++) {
      const section = idToSection.get(orderedIds[i]);
      if (!section) {
        continue;
      }
      section.order = i + 1;
      this.sections.push(section);
    }
    this.reorder();
  }
}
