import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Section } from '../entities/section.entity';
import { SectionsController } from './presentation/controllers/sections.controller';
import { CreateSectionUseCase } from './application/use-cases/create-new-section.use-case';
import { SectionsRepository } from './domain/repositories/sections-repository';
import { SectionsTypeORMRepository } from './infrastructure/repositories/sections-typeorm.repository';
import { UpdateSectionUseCase } from './application/use-cases/update-new-section.use-case';
import { DeleteSectionUseCase } from './application/use-cases/delete-section.use-case';
import { ListSectionsUseCase } from './application/use-cases/list-sections.use-case';
import { ReorderSectionsUseCase } from './application/use-cases/reorder-sections.use-case';
import { EventsEmitter } from '../@core/events/events-emmiter';
import { SectionDeletedListener } from './presentation/listeners/sections-deleted.listener';
import { CleanSectionsOrderUseCase } from './application/use-cases/clean-sections-order.use-case';
import { NestEventsEmitter } from '../@core/events/nest-events-emmiter';
import { KonquestModule, KpCacheModule } from '@keeps-node-apis/@core';
import { SectionGrpcController } from './presentation/controllers/section.grpc.controller';
import { CourseCategoryFilterRepresentationsRepository } from './domain/repositories/course-category-filter-representations.repository';
import { CourseCategoryFilterRepresentationsInfraRepository } from './infrastructure/repositories/course-category-filter-representations.infra.repository';
import { CourseIdFilterRepresentationsRepository } from './domain/repositories/course-filter-representation.repository';
import { CourseIdFilterRepresentationsInfraRepository } from './infrastructure/repositories/course-id-filter-representations.infra.repository';
import { LearningTrailFilterRepresentationsRepository } from './domain/repositories/learning-trail-filter-representations.repository';
import { LearningTrailFilterRepresentationsInfraRepository } from './infrastructure/repositories/learning-trail-filter-representations.infra.repository';
import { CourseIdFilterStrategy } from './application/strategies/course-id-filter.strategy';
import { CourseCategoryFilterStrategy } from './application/strategies/course-category-filter.strategy';
import { LearningTrailFilterStrategy } from './application/strategies/learning-trail-filter.strategy';
import {
  FILTER_STRATEGIES_TOKEN,
  FilterRepresentationStrategyResolver,
} from './application/strategies/filter-strategy.resolver';
import { GetOneSectionUseCase } from './application/use-cases/get-one-section.use-case';
import { ListAvailableSectionsUseCase } from './application/use-cases/list-available-sections.use-case';

@Module({
  imports: [TypeOrmModule.forFeature([Section]), KpCacheModule, KonquestModule],
  controllers: [SectionsController, SectionGrpcController],
  providers: [
    CreateSectionUseCase,
    UpdateSectionUseCase,
    DeleteSectionUseCase,
    ListSectionsUseCase,
    ReorderSectionsUseCase,
    CleanSectionsOrderUseCase,
    FilterRepresentationStrategyResolver,
    CourseIdFilterStrategy,
    CourseCategoryFilterStrategy,
    LearningTrailFilterStrategy,
    ListAvailableSectionsUseCase,
    {
      provide: FILTER_STRATEGIES_TOKEN,
      useFactory: (
        courseId: CourseIdFilterStrategy,
        category: CourseCategoryFilterStrategy,
        learningTrail: LearningTrailFilterStrategy,
      ) => [courseId, category, learningTrail],
      inject: [CourseIdFilterStrategy, CourseCategoryFilterStrategy, LearningTrailFilterStrategy],
    },
    {
      provide: SectionsRepository,
      useClass: SectionsTypeORMRepository,
    },
    {
      provide: EventsEmitter,
      useClass: NestEventsEmitter,
    },
    {
      provide: CourseCategoryFilterRepresentationsRepository,
      useClass: CourseCategoryFilterRepresentationsInfraRepository,
    },
    {
      provide: CourseIdFilterRepresentationsRepository,
      useClass: CourseIdFilterRepresentationsInfraRepository,
    },
    {
      provide: LearningTrailFilterRepresentationsRepository,
      useClass: LearningTrailFilterRepresentationsInfraRepository,
    },
    SectionDeletedListener,
    GetOneSectionUseCase,
  ],
})
export class SectionsModule {}
