import { KonquestCoursesService } from '@keeps-node-apis/konquest';
import { CourseIdFilterRepresentationsRepository } from '../../domain/repositories/course-filter-representation.repository';
import { Representation } from '../../domain/repositories/filter-representations.repository';
import { Injectable } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class CourseIdFilterRepresentationsInfraRepository implements CourseIdFilterRepresentationsRepository {
  constructor(private readonly konquestCoursesService: KonquestCoursesService) {}
  async getRepresentationsByFilterValues(
    values: string[],
    workspaceId?: string,
    token?: string,
  ): Promise<Representation[]> {
    const { results } = await firstValueFrom(
      this.konquestCoursesService.findAll({
        query: { id: values.join(',') },
        workspace_id: workspaceId,
        token: token,
      }),
    );

    const foundMap = new Map(results.map((course) => [course.id, course.name]));

    return values.map((id) => ({
      filterValue: id,
      value: foundMap.get(id) ?? null,
    }));
  }
}
