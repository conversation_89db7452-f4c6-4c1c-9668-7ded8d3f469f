import { KonquestCoursesService } from '@keeps-node-apis/konquest';
import { CourseCategoryFilterRepresentationsRepository } from '../../domain/repositories/course-category-filter-representations.repository';
import { Representation } from '../../domain/repositories/filter-representations.repository';
import { firstValueFrom } from 'rxjs';
import { Injectable } from '@nestjs/common';

@Injectable()
export class CourseCategoryFilterRepresentationsInfraRepository
  implements CourseCategoryFilterRepresentationsRepository
{
  constructor(private readonly konquestCoursesService: KonquestCoursesService) {}
  async getRepresentationsByFilterValues(
    values: string[],
    workspaceId?: string,
    token?: string,
  ): Promise<Representation[]> {
    const { results } = await firstValueFrom(
      this.konquestCoursesService.findAllCoursesCategories({
        query: { id: values.join(',') },
        workspace_id: workspaceId,
        token: token,
      }),
    );

    const foundMap = new Map(results.map((category) => [category.id, category.name]));

    return values.map((id) => ({
      filterValue: id,
      value: foundMap.get(id) ?? null,
    }));
  }
}
