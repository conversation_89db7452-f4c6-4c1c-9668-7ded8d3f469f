import { Injectable } from '@nestjs/common';
import { Representation } from '../../domain/repositories/filter-representations.repository';
import { LearningTrailFilterRepresentationsRepository } from '../../domain/repositories/learning-trail-filter-representations.repository';
import { firstValueFrom } from 'rxjs';
import { KonquestLearningTrailsService } from '@keeps-node-apis/konquest';

@Injectable()
export class LearningTrailFilterRepresentationsInfraRepository implements LearningTrailFilterRepresentationsRepository {
  constructor(private readonly konquestLearningTrailsService: KonquestLearningTrailsService) {}
  async getRepresentationsByFilterValues(
    values: string[],
    workspaceId?: string,
    token?: string,
  ): Promise<Representation[]> {
    const { results } = await firstValueFrom(
      this.konquestLearningTrailsService.findAll({
        query: { id: values.join(',') },
        workspace_id: workspaceId,
        token: token,
      }),
    );

    const foundMap = new Map(results.map((learningTrail) => [learningTrail.id, learningTrail.name]));

    return values.map((id) => ({
      filterValue: id,
      value: foundMap.get(id) ?? null,
    }));
  }
}
