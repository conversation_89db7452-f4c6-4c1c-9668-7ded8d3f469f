import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { LessThan, Repository } from 'typeorm';
import { SectionFilters, SectionsRepository } from '../../domain/repositories/sections-repository';
import { Section as SectionORM } from '../../../entities/section.entity';
import { Section } from '../../domain/entities/section';
import { FilterOperator, paginate, PaginateConfig, PaginateQuery } from 'nestjs-paginate';

@Injectable()
export class SectionsTypeORMRepository implements SectionsRepository {
  constructor(
    @InjectRepository(SectionORM)
    private readonly repo: Repository<SectionORM>,
  ) {}

  static toDomain(entity: SectionORM): Section {
    return new Section({ ...entity });
  }

  async update(section: Section): Promise<Section> {
    let entity = await this.repo.findOneBy({ id: section.id });
    entity = Object.assign(entity, section);
    await this.repo.save(entity);
    return SectionsTypeORMRepository.toDomain(entity);
  }

  async updateMany(sections: Section[]): Promise<void> {
    const entities = await Promise.all(
      sections.map(async (section) => {
        const entity = await this.repo.findOneBy({ id: section.id });
        return Object.assign(entity, section);
      }),
    );

    await this.repo.save(entities);
  }

  async findById(id: string): Promise<Section | null> {
    const entity = await this.repo.findOne({ where: { id } });
    return entity ? SectionsTypeORMRepository.toDomain(entity) : null;
  }

  async save(section: Section): Promise<void> {
    const entity = this.repo.create({
      id: section.id,
      workspaceId: section.workspaceId,
      title: section.title,
      description: section.description,
      filters: section.filters,
      learningObjectType: section.learningObjectType,
      startDate: section.startDate,
      endDate: section.endDate,
      order: section.order,
    });

    await this.repo.save(entity);
  }

  async delete(id: string): Promise<void> {
    await this.repo.delete(id);
  }

  async findAllByWorkspace(workspaceId: string, filters?: SectionFilters): Promise<Section[]> {
    const query: PaginateQuery = {
      filter: filters,
      path: 'section',
      limit: -1,
    };

    const config: PaginateConfig<SectionORM> = {
      defaultSortBy: [['order', 'ASC']],
      filterableColumns: {
        learningObjectType: [FilterOperator.IN],
        endDate: [FilterOperator.GTE, FilterOperator.EQ, FilterOperator.NULL],
        startDate: [FilterOperator.LTE, FilterOperator.EQ, FilterOperator.NULL],
      },
      maxLimit: -1,
      where: [
        {
          workspaceId,
        },
      ],
      sortableColumns: ['order'],
    };

    const result = await paginate<SectionORM>(query, this.repo, config);
    return result.data.map(SectionsTypeORMRepository.toDomain);
  }

  async findExpired(referenceDate: Date): Promise<Section[]> {
    const entities = await this.repo.find({
      where: {
        endDate: LessThan(referenceDate),
      },
    });
    return entities.map(SectionsTypeORMRepository.toDomain);
  }

  async findAllBy(filters?: SectionFilters): Promise<Section[]> {
    const query: PaginateQuery = {
      filter: filters,
      path: 'section',
      limit: -1,
    };

    const config: PaginateConfig<SectionORM> = {
      defaultSortBy: [
        ['order', 'ASC'],
        ['createdDate', 'DESC'],
      ],
      filterableColumns: {
        learningObjectType: [FilterOperator.IN],
        endDate: [FilterOperator.EQ, FilterOperator.LT],
      },
      maxLimit: -1,
      sortableColumns: ['order'],
    };

    const result = await paginate<SectionORM>(query, this.repo, config);
    return result.data.map(SectionsTypeORMRepository.toDomain);
  }
}
