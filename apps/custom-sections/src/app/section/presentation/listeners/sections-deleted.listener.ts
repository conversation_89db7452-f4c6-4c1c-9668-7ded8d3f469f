import { Injectable } from '@nestjs/common';
import { EventsEmitter } from '../../../@core/events/events-emmiter';
import { SectionsDeletedEvent } from '../../../@core/events/sections-deleted.event';
import { SECTIONS_DELETED_SYMBOL } from '../../../@core/constants';
import { CleanSectionsOrderUseCase } from '../../application/use-cases/clean-sections-order.use-case';

@Injectable()
export class SectionDeletedListener {
  constructor(
    private readonly eventsEmitter: EventsEmitter,
    private readonly cleanSectionsOrderUseCase: CleanSectionsOrderUseCase,
  ) {
    this.eventsEmitter.addListener(SECTIONS_DELETED_SYMBOL, (payload) => {
      this.handleSectionDeleted(payload);
    });
  }

  handleSectionDeleted(payload: SectionsDeletedEvent) {
    this.cleanSectionsOrderUseCase.execute(payload.workspaceId);
  }
}
