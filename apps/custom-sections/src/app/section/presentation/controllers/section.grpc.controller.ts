import { Controller, UseInterceptors } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { ListSectionsUseCase } from '../../application/use-cases/list-sections.use-case';
import { SectionCreateResponseDto } from '../dtos/section-create-response.dto';
import { plainToInstance } from 'class-transformer';
import { Paginate, PaginateQuery } from 'nestjs-paginate';
import { GrpcFiltersInterceptor, GrpcWorkspaceId } from '@keeps-node-apis/@core';
import { GetOneSectionUseCase } from '../../application/use-cases/get-one-section.use-case';

@Controller()
@UseInterceptors(GrpcFiltersInterceptor)
export class SectionGrpcController {
  constructor(
    private readonly listSectionsUseCase: ListSectionsUseCase,
    private readonly getOneSectionUseCase: GetOneSectionUseCase,
  ) {}

  @GrpcMethod('SectionService', 'Get')
  async get(@Paginate() query: PaginateQuery, @GrpcWorkspaceId() workspaceId: string) {
    const sections = await this.listSectionsUseCase.execute({
      workspaceId,
      filters: query.filter,
      userToken: null,
    });

    return {
      items: plainToInstance(SectionCreateResponseDto, sections, {
        excludeExtraneousValues: true,
      }),
    };
  }

  @GrpcMethod('SectionService', 'GetById')
  async getById(data: { id: string }) {
    const section = await this.getOneSectionUseCase.execute({ id: data.id });

    const flattenedFilters: Record<string, { values: string[] }> = {};
    for (const filterKey of Object.keys(section.filters ?? {})) {
      flattenedFilters[filterKey] = { values: section.filters[filterKey] ?? [] };
    }

    return {
      section: plainToInstance(
        SectionCreateResponseDto,
        { ...section, filters: flattenedFilters },
        {
          excludeExtraneousValues: true,
        },
      ),
    };
  }
}
