import {
  APP_KONQUEST_ADMIN_ROLES,
  APP_KONQUEST_ALL_ROLES,
  APP_MYACCOUNT_ADMIN_ROLES,
  AppRoles,
  AuthUser,
  Serialize,
  TenantService,
} from '@keeps-node-apis/@core';
import { Body, Controller, Delete, Get, Headers, HttpCode, Param, Patch, Post, SerializeOptions } from '@nestjs/common';
import { ApiBearerAuth, ApiQuery, ApiResponse, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { CreateSectionUseCase } from '../../application/use-cases/create-new-section.use-case';
import { CreateSectionDto } from '../dtos/section-create.dto';
import { AuthenticatedUser } from 'nest-keycloak-connect';
import { SectionCreateResponseDto } from '../dtos/section-create-response.dto';
import { UpdateSectionDto } from '../dtos/section-update.dto';
import { UpdateSectionUseCase } from '../../application/use-cases/update-new-section.use-case';
import { DeleteSectionUseCase } from '../../application/use-cases/delete-section.use-case';
import { ListSectionsUseCase } from '../../application/use-cases/list-sections.use-case';
import { Paginate, PaginateQuery } from 'nestjs-paginate';
import { LearningObjectType } from '../../infrastructure/persistence/learning-object-type.enum';
import { ReorderSectionsDto } from '../dtos/reorder-sections.dto';
import { ReorderSectionsUseCase } from '../../application/use-cases/reorder-sections.use-case';
import { SectionListResponseDto } from '../dtos/section-list-response.dto';
import { ListAvailableSectionsUseCase } from '../../application/use-cases/list-available-sections.use-case';

@ApiTags('Section')
@ApiBearerAuth()
@ApiSecurity('x-client')
@Controller('sections')
@SerializeOptions({ strategy: 'exposeAll' })
export class SectionsController {
  constructor(
    private readonly createSectionUseCase: CreateSectionUseCase,
    private readonly updateSectionUseCase: UpdateSectionUseCase,
    private readonly deleteSectionUseCase: DeleteSectionUseCase,
    private readonly listSectionsUseCase: ListSectionsUseCase,
    private readonly reorderSectionsUseCase: ReorderSectionsUseCase,
    private readonly listAvailableSectionsUseCase: ListAvailableSectionsUseCase,
    private readonly tenantService: TenantService,
  ) {}

  @AppRoles([...APP_KONQUEST_ADMIN_ROLES, ...APP_MYACCOUNT_ADMIN_ROLES])
  @Post()
  @Serialize(SectionCreateResponseDto)
  @ApiResponse({ type: SectionCreateResponseDto, status: 201 })
  async create(@Body() createSectionDto: CreateSectionDto, @AuthenticatedUser() user: AuthUser) {
    return this.createSectionUseCase.execute({
      ...createSectionDto,
      workspaceId: this.tenantService.getTenantId(),
      timeZone: user.time_zone,
    });
  }

  @AppRoles([...APP_KONQUEST_ADMIN_ROLES, ...APP_MYACCOUNT_ADMIN_ROLES])
  @Patch(':id')
  @Serialize(SectionCreateResponseDto)
  @ApiResponse({ type: SectionCreateResponseDto, status: 200 })
  async update(@Param('id') id: string, @Body() dto: UpdateSectionDto, @AuthenticatedUser() user: AuthUser) {
    return this.updateSectionUseCase.execute({
      ...dto,
      workspaceId: this.tenantService.getTenantId(),
      id: id,
      timeZone: user.time_zone,
    });
  }

  @AppRoles([...APP_KONQUEST_ADMIN_ROLES, ...APP_MYACCOUNT_ADMIN_ROLES])
  @Delete(':id')
  @ApiResponse({ status: 204, description: 'Section successfully deleted' })
  @HttpCode(204)
  async delete(@Param('id') sectionId: string): Promise<void> {
    const workspaceId = this.tenantService.getTenantId();
    await this.deleteSectionUseCase.execute({ id: sectionId, workspaceId });
  }

  @AppRoles([...APP_KONQUEST_ADMIN_ROLES, ...APP_MYACCOUNT_ADMIN_ROLES])
  @Get()
  @Serialize(SectionListResponseDto)
  @ApiResponse({ type: [SectionListResponseDto], status: 200 })
  @ApiQuery({
    name: 'filter.learningObjectType',
    required: false,
    type: typeof LearningObjectType,
    isArray: true,
    description: 'Filter by learningObjectType values.',
    example: [LearningObjectType.COURSE, LearningObjectType.COURSE_ENROLLED],
  })
  async list(@Paginate() query: PaginateQuery, @Headers('authorization') authToken: string) {
    const workspaceId = this.tenantService.getTenantId();
    return this.listSectionsUseCase.execute({
      workspaceId,
      filters: query.filter,
      userToken: authToken.replace('Bearer ', ''),
    });
  }

  @AppRoles(APP_KONQUEST_ALL_ROLES)
  @Get('/availables')
  @Serialize(SectionListResponseDto)
  @ApiResponse({ type: [SectionListResponseDto], status: 200 })
  @ApiQuery({
    name: 'filter.learningObjectType',
    required: false,
    type: typeof LearningObjectType,
    isArray: true,
    description: 'Filter by learningObjectType values.',
    example: [LearningObjectType.COURSE, LearningObjectType.COURSE_ENROLLED],
  })
  async listAvailables(@Paginate() query: PaginateQuery) {
    const workspaceId = this.tenantService.getTenantId();
    return this.listAvailableSectionsUseCase.execute({
      workspaceId,
      filters: query.filter,
    });
  }

  @Post('reorder')
  @HttpCode(204)
  @ApiResponse({ status: 204, description: 'Seções reordenadas com sucesso' })
  @AppRoles([...APP_KONQUEST_ADMIN_ROLES, ...APP_MYACCOUNT_ADMIN_ROLES])
  async reorder(@Body() dto: ReorderSectionsDto): Promise<void> {
    const workspaceId = this.tenantService.getTenantId();
    await this.reorderSectionsUseCase.execute({ ids: dto.ids, workspaceId });
  }
}
