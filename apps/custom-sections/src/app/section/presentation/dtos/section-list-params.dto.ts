import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';
import { Expose, Transform, Type } from 'class-transformer';

export class ListSectionFiltersDto {
  @ApiPropertyOptional({
    description: 'Filter by one or more learningObjectTypes',
    type: [String],
  })
  @IsOptional()
  @Expose()
  @Transform(({ value }) => {
    return typeof value === 'string' ? value.split(',') : value;
  })
  learningObjectType?: string[];
}

export class ListSectionParamsDto {
  @Type(() => ListSectionFiltersDto)
  filter?: ListSectionFiltersDto;
}
