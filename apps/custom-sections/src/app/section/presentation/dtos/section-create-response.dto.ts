import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { LearningObjectType } from '../../../@core/constants';
import { Expose } from 'class-transformer';

export class SectionCreateResponseDto {
  @ApiProperty({ description: 'Unique identifier for the section', example: 'e834728c-fb17-4b13-8548-d4e9d9e9bfae' })
  @Expose()
  id: string;

  @ApiProperty({ description: 'Title of the section', example: 'Module 1: Fundamentals' })
  @Expose()
  title: string;

  @ApiPropertyOptional({ description: 'Description of the section', example: 'Introduction to the basics.' })
  @Expose()
  description?: string;

  @ApiPropertyOptional({
    description: 'Custom filters applied to the section',
    example: { category: 'IT', difficulty: 'Beginner' },
    type: Object,
  })
  @Expose()
  filters?: Record<string, any>;

  @ApiProperty({
    description: 'Type of learning objects associated with this section',
    enum: LearningObjectType,
    example: LearningObjectType.COURSE,
  })
  @Expose()
  learningObjectType: LearningObjectType;

  @ApiPropertyOptional({
    description: 'Start date in UTC',
    example: '2025-06-01T12:00:00.000Z',
    type: String,
    format: 'date-time',
  })
  @Expose()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'End date in UTC',
    example: '2025-06-10T18:00:00.000Z',
    type: String,
    format: 'date-time',
  })
  @Expose()
  endDate?: string;

  @ApiProperty({ description: 'Display order of the section', example: 1 })
  @Expose()
  order: number;
}
