import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsObject, IsDateString, IsInt, IsEnum, IsUUID, IsBoolean } from 'class-validator';
import { LearningObjectType } from '../../../@core/constants';

export class CreateSectionDto {
  @ApiProperty({ description: 'Id of the section', example: '7f2f8d95-9cf6-4052-8f54-7f028df9a82b' })
  @IsUUID()
  @IsOptional()
  id?: string;

  @ApiProperty({ description: 'Title of the section', example: 'Module 1: Fundamentals' })
  @IsString()
  title: string;

  @ApiPropertyOptional({ description: 'Description of the section', example: 'Introduction to basic concepts.' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Custom filters applied to this section',
    example: { category: 'IT', userCreator: 'Beginner' },
    type: Object,
  })
  @IsOptional()
  @IsObject()
  filters?: Record<string, any>;

  @ApiProperty({
    description: 'Type of learning object this section represents',
    enum: LearningObjectType,
    example: LearningObjectType.COURSE_ENROLLED,
  })
  @IsString()
  @IsEnum(LearningObjectType)
  learningObjectType: LearningObjectType;

  @ApiPropertyOptional({
    description: "Start date (in the user's timezone)",
    example: '2025-06-01T09:00:00',
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({
    description: "End date (in the user's timezone)",
    example: '2025-06-10T18:00:00',
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({ description: 'Display order of the section', example: 1 })
  @IsOptional()
  @IsInt()
  order?: number;

  @ApiPropertyOptional({
    description: 'Is temporary (has start_date or/and end_date) or is not temporary',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isTemporary = true;

  workspaceId: string;
  timeZone: string;
}
