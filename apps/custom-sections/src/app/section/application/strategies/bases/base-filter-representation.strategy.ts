import { LearningObjectType } from '../../../../@core/constants';
import { FilterRepresentation } from '../../../domain/entities/section';
import { FilterRepresentationsRepository } from '../../../domain/repositories/filter-representations.repository';
import { IFilterRepresentationStrategy } from '../interfaces/filter-representation-strategy.interface';

export abstract class BaseFilterRepresentationStrategy implements IFilterRepresentationStrategy {
  abstract getRepository(): FilterRepresentationsRepository;
  abstract getSupportedLearningObjectTypes(): LearningObjectType[];
  abstract getSupportedFilterKey(): string;

  appliesTo(filterKey: string, learningObjectType: LearningObjectType): boolean {
    return (
      this.getSupportedFilterKey() === filterKey && this.getSupportedLearningObjectTypes().includes(learningObjectType)
    );
  }

  async getRepresentations(values: string[], workspaceId: string, token: string): Promise<FilterRepresentation[]> {
    const representations = await this.getRepository().getRepresentationsByFilterValues(values, workspaceId, token);

    return representations.map((rep) => ({
      filterKey: this.getSupportedFilterKey(),
      filterValue: rep.filterValue,
      value: rep.value,
    }));
  }
}
