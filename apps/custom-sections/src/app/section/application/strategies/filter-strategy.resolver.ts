import { Inject, Injectable } from '@nestjs/common';
import { IFilterRepresentationStrategy } from './interfaces/filter-representation-strategy.interface';
import { LearningObjectType } from '../../../@core/constants';

export const FILTER_STRATEGIES_TOKEN = Symbol('FILTER_STRATEGIES_TOKEN');

@Injectable()
export class FilterRepresentationStrategyResolver {
  constructor(
    @Inject(FILTER_STRATEGIES_TOKEN)
    private readonly strategies: IFilterRepresentationStrategy[] = [],
  ) {}

  resolveStrategy(
    filterKey: string,
    learningObjectType: LearningObjectType,
  ): IFilterRepresentationStrategy | undefined {
    return this.strategies.find((strategy) => strategy.appliesTo(filterKey, learningObjectType));
  }
}
