import { Injectable } from '@nestjs/common';
import { LearningObjectType } from '../../../@core/constants';
import { CourseIdFilterRepresentationsRepository } from '../../domain/repositories/course-filter-representation.repository';
import { FilterRepresentationsRepository } from '../../domain/repositories/filter-representations.repository';
import { BaseFilterRepresentationStrategy } from './bases/base-filter-representation.strategy';

@Injectable()
export class CourseIdFilterStrategy extends BaseFilterRepresentationStrategy {
  constructor(private readonly repo: CourseIdFilterRepresentationsRepository) {
    super();
  }

  getRepository(): FilterRepresentationsRepository {
    return this.repo;
  }

  getSupportedLearningObjectTypes(): LearningObjectType[] {
    return [
      LearningObjectType.COURSE,
      LearningObjectType.EVENTS,
      LearningObjectType.HIGHLIGHT_COURSE,
      LearningObjectType.HIGHLIGHT_EVENTS,
    ];
  }

  getSupportedFilterKey(): string {
    return 'ID';
  }
}
