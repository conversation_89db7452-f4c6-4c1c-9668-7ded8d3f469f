import { Injectable } from '@nestjs/common';
import { LearningObjectType } from '../../../@core/constants';
import { LearningTrailFilterRepresentationsRepository } from '../../domain/repositories/learning-trail-filter-representations.repository';
import { FilterRepresentationsRepository } from '../../domain/repositories/filter-representations.repository';
import { BaseFilterRepresentationStrategy } from './bases/base-filter-representation.strategy';

@Injectable()
export class LearningTrailFilterStrategy extends BaseFilterRepresentationStrategy {
  constructor(private readonly repo: LearningTrailFilterRepresentationsRepository) {
    super();
  }

  getRepository(): FilterRepresentationsRepository {
    return this.repo;
  }

  getSupportedLearningObjectTypes(): LearningObjectType[] {
    return [LearningObjectType.LEARNING_TRAIL, LearningObjectType.HIGHLIGHT_LEARNING_TRAIL];
  }

  getSupportedFilterKey(): string {
    return 'ID';
  }
}
