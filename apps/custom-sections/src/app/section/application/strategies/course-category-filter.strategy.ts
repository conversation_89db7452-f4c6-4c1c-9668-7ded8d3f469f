import { Injectable } from '@nestjs/common';
import { LearningObjectType } from '../../../@core/constants';
import { CourseCategoryFilterRepresentationsRepository } from '../../domain/repositories/course-category-filter-representations.repository';
import { FilterRepresentationsRepository } from '../../domain/repositories/filter-representations.repository';
import { BaseFilterRepresentationStrategy } from './bases/base-filter-representation.strategy';

@Injectable()
export class CourseCategoryFilterStrategy extends BaseFilterRepresentationStrategy {
  constructor(private readonly repo: CourseCategoryFilterRepresentationsRepository) {
    super();
  }

  getRepository(): FilterRepresentationsRepository {
    return this.repo;
  }

  getSupportedLearningObjectTypes(): LearningObjectType[] {
    return [
      LearningObjectType.COURSE,
      LearningObjectType.EVENTS,
      LearningObjectType.HIGHLIGHT_COURSE,
      LearningObjectType.HIGHLIGHT_EVENTS,
    ];
  }

  getSupportedFilterKey(): string {
    return 'CATEGORY_IDS';
  }
}
