import { toDate } from 'date-fns-tz';
import { IUseCase } from '../../../@core/common/interfaces/use-cases/base.use-case.interface';
import { CreateSectionInput } from '../dtos/create-section-input.dto';
import { Section } from '../../domain/entities/section';
import { SectionsRepository } from '../../domain/repositories/sections-repository';
import { Injectable } from '@nestjs/common';

@Injectable()
export class CreateSectionUseCase implements IUseCase<CreateSectionInput, Section> {
  constructor(private sectionRepository: SectionsRepository) {}

  async execute(dto: CreateSectionInput): Promise<Section> {
    const section = this.toEntity(dto);

    if (dto.isTemporary === false) {
      section.startDate = null;
      section.endDate = null;
    }
    section.validateDates(section.startDate, section.endDate);
    await this.sectionRepository.save(section);
    return section;
  }

  private toUtc(date?: string, zone?: string): Date | null {
    return date ? toDate(date, { timeZone: zone }) : null;
  }

  private toEntity(dto: CreateSectionInput): Section {
    return new Section({
      workspaceId: dto.workspaceId,
      title: dto.title ?? '',
      description: dto.description ?? null,
      filters: dto.filters ?? null,
      learningObjectType: dto.learningObjectType,
      startDate: this.toUtc(dto.startDate, dto.timeZone),
      endDate: this.toUtc(dto.endDate, dto.timeZone),
      order: dto.order ?? 0,
      id: dto.id,
    });
  }
}
