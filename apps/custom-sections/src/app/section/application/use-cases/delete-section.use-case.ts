import { Injectable } from '@nestjs/common';
import { SectionsRepository } from '../../domain/repositories/sections-repository';
import { SectionAccessDeniedException, SectionNotFoundException } from '../../domain/exceptions/sections.exception';
import { IUseCase } from '../../../@core/common/interfaces/use-cases/base.use-case.interface';
import { EventsEmitter } from '../../../@core/events/events-emmiter';
import { SECTIONS_DELETED_SYMBOL } from '../../../@core/constants';
import { SectionsDeletedEvent } from '../../../@core/events/sections-deleted.event';

@Injectable()
export class DeleteSectionUseCase implements IUseCase<{ id: string; workspaceId: string }, void> {
  constructor(
    private readonly sectionRepository: SectionsRepository,
    private readonly events: EventsEmitter,
  ) {}

  async execute(input: { id: string; workspaceId: string }): Promise<void> {
    const section = await this.sectionRepository.findById(input.id);
    if (!section) {
      throw new SectionNotFoundException(input.id);
    }
    if (section.workspaceId !== input.workspaceId) {
      throw new SectionAccessDeniedException(input.id, input.workspaceId);
    }

    await this.sectionRepository.delete(input.id);

    this.events.emit(SECTIONS_DELETED_SYMBOL, new SectionsDeletedEvent(input.workspaceId));
  }
}
