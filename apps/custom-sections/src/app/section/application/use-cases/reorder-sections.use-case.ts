import { Injectable } from '@nestjs/common';
import { IUseCase } from '../../../@core/common/interfaces/use-cases/base.use-case.interface';
import { SectionsRepository } from '../../domain/repositories/sections-repository';
import { SectionAccessDeniedException } from '../../domain/exceptions/sections.exception';
import { SectionGroup } from '../../domain/entities/section-group';

interface ReorderSectionsInput {
  ids: string[];
  workspaceId: string;
}

@Injectable()
export class ReorderSectionsUseCase implements IUseCase<ReorderSectionsInput, void> {
  constructor(private readonly repo: SectionsRepository) {}

  async execute(input: ReorderSectionsInput): Promise<void> {
    const { ids, workspaceId } = input;
    const allSections = await this.repo.findAllByWorkspace(workspaceId);

    const sectionIdsSet = new Set(allSections.map((s) => s.id));
    for (const id of ids) {
      if (!sectionIdsSet.has(id)) throw new SectionAccessDeniedException(id, workspaceId);
    }

    const sectionGroups = SectionGroup.group(allSections);
    for (const group of sectionGroups) {
      group.reorderByIds(ids);
      const updatedSections = group.getSectionsToUpdate();
      await Promise.all(updatedSections.map((section) => this.repo.update(section)));
    }
  }
}
