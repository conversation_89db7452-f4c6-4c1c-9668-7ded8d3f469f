import { Injectable } from '@nestjs/common';
import { IUseCase } from '../../../@core/common/interfaces/use-cases/base.use-case.interface';
import { Section } from '../../domain/entities/section';
import { SectionsRepository } from '../../domain/repositories/sections-repository';
import { SectionNotFoundException } from '../../domain/exceptions/sections.exception';

interface GetOneSectionInput {
  id: string;
}

@Injectable()
export class GetOneSectionUseCase implements IUseCase<GetOneSectionInput, Section> {
  constructor(private readonly sectionRepository: SectionsRepository) {}

  async execute(input: GetOneSectionInput): Promise<Section> {
    const section = await this.sectionRepository.findById(input.id);
    if (!section) {
      throw new SectionNotFoundException(input.id);
    }
    return section;
  }
}
