import { Injectable } from '@nestjs/common';
import { IUseCase } from '../../../@core/common/interfaces/use-cases/base.use-case.interface';
import { SectionsRepository } from '../../domain/repositories/sections-repository';
import { Section } from '../../domain/entities/section';

interface ListAvailableSectionsInput {
  workspaceId: string;
  filters?: { learningObjectType?: string[] };
}

@Injectable()
export class ListAvailableSectionsUseCase implements IUseCase<ListAvailableSectionsInput, Section[]> {
  constructor(private readonly sectionRepository: SectionsRepository) {}

  async execute(input: ListAvailableSectionsInput): Promise<Section[]> {
    const filters = {
      ...input.filters,
      endDate: [`$gte:${new Date().toISOString()}`, `$or:$null`],
      startDate: [`$lte:${new Date().toISOString()}`, `$or:$null`],
    };
    const sections = await this.sectionRepository.findAllByWorkspace(input.workspaceId, {
      ...filters,
    });
    return sections;
  }
}
