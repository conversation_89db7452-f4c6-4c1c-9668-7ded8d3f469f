import { Injectable, Logger } from '@nestjs/common';
import { IUseCase } from '../../../@core/common/interfaces/use-cases/base.use-case.interface';
import { SectionsRepository } from '../../domain/repositories/sections-repository';
import { Section } from '../../domain/entities/section';
import { FilterRepresentationStrategyResolver } from '../strategies/filter-strategy.resolver';

interface ListSectionsInput {
  workspaceId: string;
  userToken: string;
  filters?: { learningObjectType?: string[] };
}

@Injectable()
export class ListSectionsUseCase implements IUseCase<ListSectionsInput, Section[]> {
  private logger = new Logger(ListSectionsUseCase.name);
  constructor(
    private readonly sectionRepository: SectionsRepository,
    private readonly strategyResolver: FilterRepresentationStrategyResolver,
  ) {}

  async execute(input: ListSectionsInput): Promise<Section[]> {
    const sections = await this.sectionRepository.findAllByWorkspace(input.workspaceId, input.filters);

    if (input.userToken) {
      await Promise.all(sections.map((section) => this.enrichSectionFiltersRepresentations(section, input.userToken)));
    }

    return sections;
  }

  private async enrichSectionFiltersRepresentations(section: Section, token: string): Promise<void> {
    const representations = [];

    for (const filterKey of Object.keys(section.filters ?? {})) {
      const rawValues = section.filters[filterKey];
      const values = Array.isArray(rawValues) ? rawValues : [rawValues];
      const strategy = this.strategyResolver.resolveStrategy(filterKey, section.learningObjectType);

      if (!strategy) continue;

      try {
        const strategyResults = await strategy.getRepresentations(values, section.workspaceId, token);
        representations.push(...strategyResults);
      } catch (error) {
        this.logger.error(`Error getting representations for filter (${filterKey}): ${error.message}`, error.stack);
        continue;
      }
    }

    section.filtersRepresentations = representations;
  }
}
