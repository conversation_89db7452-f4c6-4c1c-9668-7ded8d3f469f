import { Injectable } from '@nestjs/common';
import { IUseCase } from '../../../@core/common/interfaces/use-cases/base.use-case.interface';
import { SectionsRepository } from '../../domain/repositories/sections-repository';
import { SectionGroup } from '../../domain/entities/section-group';

@Injectable()
export class CleanSectionsOrderUseCase implements IUseCase<string, void> {
  constructor(private readonly sectionRepo: SectionsRepository) {}

  async execute(workspaceId: string): Promise<void> {
    const sections = await this.sectionRepo.findAllByWorkspace(workspaceId);
    const sectionGroups = SectionGroup.group(sections);

    for (const sectionGroup of sectionGroups) {
      sectionGroup.reorder();
      const toUpdate = sectionGroup.getSectionsToUpdate();
      await this.sectionRepo.updateMany(toUpdate);
    }
  }
}
