import { Injectable } from '@nestjs/common';
import { IUseCase } from '../../../@core/common/interfaces/use-cases/base.use-case.interface';
import { Section } from '../../domain/entities/section';
import { SectionsRepository } from '../../domain/repositories/sections-repository';
import { UpdateSectionInput } from '../dtos/update-section-input.dto';
import { SectionNotFoundException } from '../../domain/exceptions/sections.exception';
import { toDate } from 'date-fns-tz';

@Injectable()
export class UpdateSectionUseCase implements IUseCase<UpdateSectionInput, Section> {
  constructor(private sectionRepository: SectionsRepository) {}

  async execute(dto: UpdateSectionInput): Promise<Section> {
    const current = await this.sectionRepository.findById(dto.id);

    if (!current || current.workspaceId !== dto.workspaceId) {
      throw new SectionNotFoundException(dto.id);
    }
    let updated = this.toEntity(current, dto);
    updated = await this.sectionRepository.update(updated);
    return updated;
  }

  private toUtc(date?: string, zone?: string): Date | null {
    return date ? toDate(date, { timeZone: zone }) : null;
  }

  private toEntity(currentSection: Section, dto: UpdateSectionInput): Section {
    let startDate = dto.startDate !== undefined ? this.toUtc(dto.startDate, dto.timeZone) : currentSection.startDate;
    let endDate = dto.endDate !== undefined ? this.toUtc(dto.endDate, dto.timeZone) : currentSection.endDate;

    if (dto.isTemporary === false) {
      startDate = null;
      endDate = null;
    }

    if (dto.startDate !== undefined || dto.endDate !== undefined) {
      currentSection.validateDates(startDate, endDate);
    }

    return new Section({
      ...currentSection,
      title: dto.title ?? currentSection.title,
      description: dto.description ?? currentSection.description,
      filters: dto.filters ?? currentSection.filters,
      startDate,
      endDate,
      id: currentSection.id,
    });
  }
}
