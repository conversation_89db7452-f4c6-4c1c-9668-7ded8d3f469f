import { Transport } from '@nestjs/microservices';
import { join } from 'path';

export const CUSTOM_SECTIONS_GRPC_OPTIONS = {
  transport: Transport.GRPC,
  options: {
    package: 'sections',
    protoPath: join(__dirname, './assets/protos/sections.proto'),
    url: process.env.GRPC_URL || '0.0.0.0:50051',
    loader: {
      keepCase: false,
      longs: String,
      enums: String,
      defaults: true,
      oneofs: true,
    },
  },
} as const;
