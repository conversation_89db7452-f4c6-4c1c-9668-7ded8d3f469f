import { MigrationInterface, QueryRunner } from "typeorm";

export class INITIAL1749057463559 implements MigrationInterface {
    name = 'INITIAL1749057463559'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."learning_object_type" AS ENUM('LEARNING_TRAIL', 'LEARNING_TRAIL.ENROLLED', 'LEARNING_TRAIL.ALL', 'COURSE', 'COURSE.ENROLLED', 'COURSE.ALL', 'EVENTS', 'EVENTS.ENROLLED', 'EVENTS.ALL')`);
        await queryRunner.query(`CREATE TABLE "section" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_date" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_date" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "start_date" TIMESTAMP WITH TIME ZONE, "end_date" TIMESTAMP WITH TIME ZONE, "workspace_id" uuid NOT NULL, "filters" json, "title" character varying NOT NULL, "description" character varying, "learningObjectType" "public"."learning_object_type" NOT NULL, "order" integer NOT NULL DEFAULT '0', CONSTRAINT "PK_3c41d2d699384cc5e8eac54777d" PRIMARY KEY ("id"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "section"`);
        await queryRunner.query(`DROP TYPE "public"."learning_object_type"`);
    }

}
