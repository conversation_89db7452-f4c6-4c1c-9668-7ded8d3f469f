import { MigrationInterface, QueryRunner } from "typeorm";

export class HIGHLIGHT<PERSON>ARNINGOBJECTTYPES1751372023740 implements MigrationInterface {
    name = 'HIGHLIGHTLEARNINGOBJECTTYPES1751372023740'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TYPE "public"."learning_object_type" RENAME TO "learning_object_type_old"`);
        await queryRunner.query(`CREATE TYPE "public"."learning_object_type" AS ENUM('LEARNING_TRAIL', 'LEARNING_TRAIL.ENROLLED', 'LEARNING_TRAIL.ALL', 'COURSE', 'COURSE.ENROLLED', 'COURSE.ALL', 'EVENTS', 'EVENTS.ENROLLED', 'EVENTS.ALL', 'HIGHLIGHT.LEARNING_TRAIL', 'HIGHLIGHT.LEARNING_TRAIL.ENROLLED', 'HIGHLIGHT.LEARNING_TRAIL.ALL', 'HIGHLIGHT.COURSE', 'HIGHLIGHT.COURSE.ENROLLED', 'HIGHLIGHT.COURSE.ALL', 'HIGHLIGHT.EVENTS', 'HIGHLIGHT.EVENTS.ENROLLED', 'HIGHLIGHT.EVENTS.ALL')`);
        await queryRunner.query(`ALTER TABLE "section" ALTER COLUMN "learningObjectType" TYPE "public"."learning_object_type" USING "learningObjectType"::"text"::"public"."learning_object_type"`);
        await queryRunner.query(`DROP TYPE "public"."learning_object_type_old"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."learning_object_type_old" AS ENUM('LEARNING_TRAIL', 'LEARNING_TRAIL.ENROLLED', 'LEARNING_TRAIL.ALL', 'COURSE', 'COURSE.ENROLLED', 'COURSE.ALL', 'EVENTS', 'EVENTS.ENROLLED', 'EVENTS.ALL')`);
        await queryRunner.query(`ALTER TABLE "section" ALTER COLUMN "learningObjectType" TYPE "public"."learning_object_type_old" USING "learningObjectType"::"text"::"public"."learning_object_type_old"`);
        await queryRunner.query(`DROP TYPE "public"."learning_object_type"`);
        await queryRunner.query(`ALTER TYPE "public"."learning_object_type_old" RENAME TO "learning_object_type"`);
    }

}
