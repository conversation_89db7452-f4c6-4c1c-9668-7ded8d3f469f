export enum LearningObjectType {
  LEARNING_TRAIL = 'LEARNING_TRAIL',
  LEARNING_TRAIL_ENROLLED = 'LEARNING_TRAIL.ENROLLED',
  LEARNING_TRAIL_ALL = 'LEARNING_TRAIL.ALL',
  COURSE = 'COURSE',
  COURSE_ENROLLED = 'COURSE.ENROLLED',
  COURSE_ALL = 'COURSE.ALL',
  EVENTS = 'EVENTS',
  EVENTS_ENROLLED = 'EVENTS.ENROLLED',
  EVENTS_ALL = 'EVENTS.ALL',
  HIGHLIGHT_LEARNING_TRAIL = 'HIGHLIGHT.LEARNING_TRAIL',
  HIGHLIGHT_LEARNING_TRAIL_ENROLLED = 'HIGHLIGHT.LEARNING_TRAIL.ENROLLED',
  HIGHLIGHT_LEARNING_TRAIL_ALL = 'HIGHLIGHT.LEARNING_TRAIL.ALL',
  HIGHLIGHT_COURSE = 'HIGHLIGHT.COURSE',
  HIGHLIGHT_COURSE_ENROLLED = 'HIGHLIGHT.COURSE.ENROLLED',
  HIGHLIGHT_COURSE_ALL = 'HIGHLIGHT.COURSE.ALL',
  HIGHLIGHT_EVENTS = 'HIGHLIGHT.EVENTS',
  HIGHLIGHT_EVENTS_ENROLLED = 'HIGHLIGHT.EVENTS.ENROLLED',
  HIGHLIGHT_EVENTS_ALL = 'HIGHLIGHT.EVENTS.ALL',
}

export const SECTIONS_DELETED_SYMBOL = Symbol('SECTIONS.DELETED');
