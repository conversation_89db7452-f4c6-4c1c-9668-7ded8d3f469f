/**
 * Represents an event identifier.
 * Can be either a `string` or a `symbol` to allow for flexible event naming.
 */
export type event = symbol | string;

/**
 * Defines the structure of a listener function for an event.
 *
 * A listener receives any number of arguments when an event is emitted.
 */
export type ListenerFn = (...values: any[]) => void;

/**
 * Abstract base class for an event emitter.
 *
 * Provides a contract for emitting events and registering listeners
 * in a decoupled, framework-agnostic way.
 *
 * Implementations of this class should define how events are dispatched
 * and how listeners are registered (e.g., using NestJS's EventEmitter2).
 */
export abstract class EventsEmitter {
  /**
   * Emits an event with optional arguments.
   *
   * @param event - The name or symbol of the event to emit.
   * @param values - Additional values to be passed to the listeners.
   * @returns `true` if the event had listeners, otherwise `false`.
   */
  abstract emit(event: event, ...values: any[]): boolean;

  /**
   * Registers a listener function for a specific event type.
   *
   * @param eventType - The name or symbol of the event to listen for.
   * @param listener - The function to call when the event is emitted.
   */
  abstract addListener(eventType: event, listener: ListenerFn): void;
}
