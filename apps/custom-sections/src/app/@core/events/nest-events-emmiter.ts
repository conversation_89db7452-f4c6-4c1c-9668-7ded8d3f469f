import { Injectable } from '@nestjs/common';
import { EventsEmitter, ListenerFn } from './events-emmiter';
import { EventEmitter2 } from '@nestjs/event-emitter';

/**
 * Implementation of the domain-level `EventsEmitter` interface
 * using NestJS's built-in EventEmitter2.
 *
 * This class serves as a bridge between the domain layer and the
 * NestJS event system, allowing domain events to be emitted and
 * listened to without acoplando diretamente com o framework.
 */
@Injectable()
export class NestEventsEmitter implements EventsEmitter {
  /**
   * @param emitter The underlying NestJS EventEmitter2 instance.
   */
  constructor(private readonly emitter: EventEmitter2) {}

  /**
   * Emits an event with optional payload(s) to all registered listeners.
   *
   * @param event - The name or symbol of the event to emit.
   * @param values - Optional arguments to pass to the listeners.
   * @returns `true` if the event had listeners, `false` otherwise.
   */
  emit(event: string, ...values: any[]): boolean {
    return this.emitter.emit(event, ...values);
  }

  /**
   * Registers a listener for a given event type.
   *
   * @param eventType - The event name or symbol to listen for.
   * @param listener - A callback function that handles the event.
   */
  addListener(eventType: string, listener: ListenerFn): void {
    this.emitter.on(eventType, listener);
  }
}
