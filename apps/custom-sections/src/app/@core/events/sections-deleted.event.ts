/**
 * Event representing that one or more sections have been deleted
 * within a specific workspace.
 *
 * This event is typically emitted after a cleanup or deletion
 * operation, such as removing expired sections, to trigger any
 * downstream processes (e.g., reordering sections).
 */
export class SectionsDeletedEvent {
  /**
   * @param workspaceId - The identifier of the workspace where sections were deleted.
   */
  constructor(public readonly workspaceId: string) {}
}
