NODE_ENV=development

DB_NAME=custom-sections_dev_db
DB_USER=postgres
DB_PASS=postgres
DB_HOST=localhost
DB_PORT=5432
DB_DEBUG=false
DB_MIGRATIONS_RUN=false

AUTH_URL=https://iam.keepsdev.com/auth/
AUTH_REALM=keeps-dev
AUTH_CLIENT_ID=custom-sections-microservice
AUTH_CLIENT_SECRET=6eFnyGC4TF5cKUJ5ss3ckIdm3ObTLDlP
AUTH_REALM_PUBLIC_KEY=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtmbT6XgN9buXZMF6EAjHRNmfGaarVUbik+KtON1YBp0KgTdJn0lCtxhCq5DtvFLOOBrDccw6/RDjeSWlGEcgqggwjPnDIgdtAtgP83mofiILj0mQZfap3o/WknpR6LlveEeM09jnszCTy+WKGZ+HkOJRnhOo/JR7cKSmQxKjEI6NCCNzC4CkwUrPnE5nBhLDk5MvU9KLsBy1TyXoi7c5sh66SLJwD5yn2EzsIxxxje4fYOsXEf6PD6vsAHvjNQNlFELapQTomCECkAwg0fsDZlGAbyK+FoXt/jquKFZ/rcutoXu5tVVNNSfvs5Q4W931KAzqLozd/grR9HlSZAvVWwIDAQAB
AUTH_DEBUG=false

## MyAccount
AUTH_APPLICATION_ID=ad7e5ad2-1552-43ab-a471-710954f0e66a
MYACCOUNT_API_URL=https://learning-platform-api-stage.keepsdev.com/myaccount-v2

# REDIS
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
