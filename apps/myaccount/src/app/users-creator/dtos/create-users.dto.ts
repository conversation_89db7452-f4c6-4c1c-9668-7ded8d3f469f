import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ArrayNotEmpty, IsArray, IsOptional, IsUUID, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { UserDataWithMoreDto } from '../../users/application/dtos/create-user.dto';

export class CreateUsersDto {
  @ApiProperty({
    type: () => [UserDataWithMoreDto],
    description: 'List of users to be created with their respective information',
    example: [
      {
        name: '<PERSON>',
        email: '<EMAIL>',
      },
      {
        name: '<PERSON>',
        email: '<EMAIL>',
      },
    ],
  })
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => UserDataWithMoreDto)
  users: UserDataWithMoreDto[];

  @ApiPropertyOptional({
    type: [String],
    description: 'List of permissions to be assigned to the users (optional)',
    example: ['b3a0d9a7-0f85-4b19-b75f-9512be4f9210', '9419a921-dbcc-471b-b65b-a482c06d4e23'],
  })
  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  permissions?: string[];
}
