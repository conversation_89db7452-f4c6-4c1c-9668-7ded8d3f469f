import { ApiPropertyOptional, IntersectionType } from '@nestjs/swagger';
import { IsArray, IsOptional, IsUUID } from 'class-validator';
import { Expose, Type } from 'class-transformer';
import { UserDataWithMoreDto } from '../../users/application/dtos/create-user.dto';
import { LanguageDto } from '../../users/application/dtos/language.dto';

export class CreateUserDto extends IntersectionType(UserDataWithMoreDto) {
  @ApiPropertyOptional({
    type: [String],
    description: 'List of permissions to be assigned to the users (optional)',
    example: ['9419a921-dbcc-471b-b65b-a482c06d4e23'],
  })
  @IsArray()
  @IsOptional()
  @IsUUID('4', { each: true })
  permissions?: string[];
}

export class CreateUserResultDto {
  @Expose()
  id: string;

  @Expose()
  email: string;

  @Expose()
  name: string;

  @Expose()
  ein: string;

  @Expose()
  nickname: string;

  @Expose()
  secondaryEmail: string;

  @Expose()
  phone: string;

  @Expose()
  gender: string;

  @Expose()
  birthday: string;

  @Expose()
  address: string;

  @Expose()
  avatar: string;

  @Expose()
  status: boolean;

  @Expose()
  @Type(() => LanguageDto)
  language: LanguageDto;

  @Expose()
  country: string;

  @Expose()
  relatedUserLeaderId: string;

  @Expose()
  emailVerified: boolean;

  @Expose()
  timeZone: string;

  @Expose()
  admissionDate: string;

  @Expose()
  contractType: string;

  @Expose()
  cpf: string;

  @Expose()
  education: string;

  @Expose()
  ethnicity: string;

  @Expose()
  hierarchicalLevel: string;

  @Expose()
  maritalStatus: string;
}
