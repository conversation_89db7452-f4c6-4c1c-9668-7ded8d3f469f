import { UsersTypeOrmRepository } from '../users/infrastructure/repositories/users-type-orm.repository';
import { UserBatchService } from './services/user-batch.service';
import { UserCreationService } from './services/user-creation.service';
import { UserJobService } from './services/user-job.service';
import { UserLanguageService } from './services/user-language.service';
import { UpdateUsersService } from '../users/application/services/update-users.service';
import { UserNotificationService } from './services/user-notification.service';
import { WorkspacePermissionService } from './services/workspace-permission.service';
import { UserIdentityService } from './services/user-identity.service';
import { PasswordService } from './services/password.service';
import { UserCreateGrpcService } from './services/user-create-grpc.service';

export default [
  PasswordService,
  UserIdentityService,
  WorkspacePermissionService,
  UserNotificationService,
  UpdateUsersService,
  UsersTypeOrmRepository,
  UserCreationService,
  UserJobService,
  UserLanguageService,
  UserBatchService,
  UserCreateGrpcService,
];
