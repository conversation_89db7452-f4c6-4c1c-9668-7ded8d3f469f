import { Controller } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { Metadata } from '@grpc/grpc-js';
import { UserCreateGrpcService } from '../services/user-create-grpc.service';
import { CreateUsersDto } from '../dtos/create-users.dto';
import { plainToInstance } from 'class-transformer';
import { BatchResultDto } from '../../users/application/dtos/create-user.dto';

@Controller()
export class UserGrpcController {
  constructor(private readonly userCreateGrpcService: UserCreateGrpcService) {}

  @GrpcMethod('UserService', 'CreateUser')
  async CreateUser(data: CreateUsersDto, metadata: Metadata): Promise<{ users: BatchResultDto[] }> {
    const createUsersDto = plainToInstance(CreateUsersDto, data);
    const workspaceId = this.userCreateGrpcService.validateAndExtractWorkspaceId(metadata);
    const result = await this.userCreateGrpcService.processUserCreation(createUsersDto, workspaceId);
    return { users: result };
  }
}
