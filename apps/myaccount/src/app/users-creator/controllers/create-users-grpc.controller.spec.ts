import { Test, TestingModule } from '@nestjs/testing';
import { UserCreateGrpcService } from '../services/user-create-grpc.service';
import { CreateUsersDto } from '../dtos/create-users.dto';
import { Metadata } from '@grpc/grpc-js';
import { UserGrpcController } from './create-users-grpc.controller';
import { BatchResultDto } from '../../users/application/dtos/create-user.dto';

describe('UserGrpcController', () => {
  let controller: UserGrpcController;
  let userCreateGrpcService: UserCreateGrpcService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserGrpcController],
      providers: [
        {
          provide: UserCreateGrpcService,
          useValue: {
            validateAndExtractWorkspaceId: jest.fn(),
            processUserCreation: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<UserGrpcController>(UserGrpcController);
    userCreateGrpcService = module.get<UserCreateGrpcService>(UserCreateGrpcService);
  });

  describe('CreateUser (gRPC)', () => {
    it('should process user creation and return the created users', async () => {
      const metadataMock = {
        get: jest.fn().mockReturnValue(['workspace-id-123']),
      } as unknown as Metadata;

      const createUsersDto: CreateUsersDto = {
        users: [
          { email: '<EMAIL>', name: 'User One' },
          { email: '<EMAIL>', name: 'User Two' },
        ],
        permissions: ['read', 'write'],
      };

      const expectedResult: BatchResultDto[] = [
        { email: '<EMAIL>', id: 'id-1' },
        { email: '<EMAIL>', id: 'id-2' },
      ];

      (userCreateGrpcService.validateAndExtractWorkspaceId as jest.Mock).mockReturnValue('workspace-id-123');
      (userCreateGrpcService.processUserCreation as jest.Mock).mockResolvedValue(expectedResult);

      const result = await controller.CreateUser(createUsersDto, metadataMock);

      expect(userCreateGrpcService.validateAndExtractWorkspaceId).toHaveBeenCalledWith(metadataMock);
      expect(userCreateGrpcService.processUserCreation).toHaveBeenCalledWith(
        expect.objectContaining({
          users: createUsersDto.users,
          permissions: createUsersDto.permissions,
        }),
        'workspace-id-123',
      );

      expect(result).toEqual({ users: expectedResult });
    });

    it('should throw error if workspaceId is missing in metadata', async () => {
      const metadataMock = {
        get: jest.fn().mockReturnValue([]),
      } as unknown as Metadata;

      const createUsersDto: CreateUsersDto = {
        users: [{ email: '<EMAIL>', name: 'User' }],
        permissions: [],
      };

      (userCreateGrpcService.validateAndExtractWorkspaceId as jest.Mock).mockImplementation(() => {
        throw { code: 3, message: 'Workspace ID (x-client) is required' };
      });

      await expect(controller.CreateUser(createUsersDto, metadataMock)).rejects.toEqual({
        code: 3,
        message: 'Workspace ID (x-client) is required',
      });
    });
  });
});
