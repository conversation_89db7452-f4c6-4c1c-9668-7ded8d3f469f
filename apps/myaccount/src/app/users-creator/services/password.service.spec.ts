import { Test, TestingModule } from '@nestjs/testing';
import { PasswordService } from './password.service';
import { KeycloakRepository } from '@keeps-node-apis/@core';

describe('PasswordService', () => {
  let service: PasswordService;
  let keycloakRepository: jest.Mocked<KeycloakRepository>;

  beforeEach(async () => {
    const mockKeycloakRepository = {
      resetUserPassword: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PasswordService,
        {
          provide: KeycloakRepository,
          useValue: mockKeycloakRepository,
        },
      ],
    }).compile();

    service = module.get<PasswordService>(PasswordService);
    keycloakRepository = module.get(KeycloakRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('generatePassword', () => {
    it('should generate password with correct length', () => {
      const password = service.generatePassword();
      expect(password).toHaveLength(8);
    });

    it('should generate only digits', () => {
      const password = service.generatePassword();
      expect(password).toMatch(/^\d+$/);
    });

    it('should generate different passwords on subsequent calls', () => {
      const password1 = service.generatePassword();
      const password2 = service.generatePassword();
      expect(password1).not.toBe(password2);
    });
  });

  describe('updatePassword', () => {
    it('should call repository with correct parameters and temporary true by default', async () => {
      const userId = 'test-user-id';
      const password = '123456';

      await service.updatePassword(userId, password);

      expect(keycloakRepository.resetUserPassword).toHaveBeenCalledWith(userId, password, true);
    });

    it('should call repository with temporary false when specified', async () => {
      const userId = 'test-user-id';
      const password = '123456';

      await service.updatePassword(userId, password, false);

      expect(keycloakRepository.resetUserPassword).toHaveBeenCalledWith(userId, password, false);
    });

    it('should throw error if repository call fails', async () => {
      const userId = 'test-user-id';
      const password = '123456';
      const expectedError = new Error('Repository error');

      keycloakRepository.resetUserPassword.mockRejectedValueOnce(expectedError);

      await expect(service.updatePassword(userId, password)).rejects.toThrow(expectedError);
    });
  });
});
