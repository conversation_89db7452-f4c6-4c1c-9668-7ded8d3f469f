import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { plainToInstance } from 'class-transformer';
import { v4 as uuidv4 } from 'uuid';

import { PasswordService } from './password.service';
import { UserIdentityService } from './user-identity.service';
import { WorkspacePermissionService } from './workspace-permission.service';
import { UserNotificationService } from './user-notification.service';
import { UserLanguageService } from './user-language.service';
import { UserJobService } from './user-job.service';
import { UsersTypeOrmRepository } from '../../users/infrastructure/repositories/users-type-orm.repository';
import { User } from '../../entities/user.entity';
import { DEFAULT_TIMEZONE } from '../../constants/timezone.constant';

import { UserCreationService } from './user-creation.service';
import { UserRoleWorkspace } from '../../entities/user-role-workspace.entity';
import { IdpWorkspace } from '../../entities/idp-workspace.entity';
import { Repository } from 'typeorm';
import { UserRolesService } from '../../users/application/services/user-roles.service';
import { EmployeeInfoService } from '../../users/application/services/employee-info.service';
import { UserKeycloakService } from '../../users/application/services/user-keycloak.service';
import { UserDataWithMoreDto } from '../../users/application/dtos/create-user.dto';
import { EmployeeInfoDto } from '../../users/application/dtos/employee-info.dto';
import { UsersRepository } from '../../users/domain/repositories/users.repository';
import { EmployeeInfoCreateDto } from '../../users/application/dtos/employee-info-create.dto';

describe('UserCreatorService', () => {
  let service: UserCreationService;
  let mockUserRepository: jest.Mocked<Partial<UsersTypeOrmRepository>>;
  let mockUserRolesService: jest.Mocked<Partial<UserRolesService>>;
  let mockPasswordService: jest.Mocked<Partial<PasswordService>>;
  let mockUserIdentityService: jest.Mocked<Partial<UserIdentityService>>;
  let mockEmployeeInfoService: jest.Mocked<Partial<EmployeeInfoService>>;
  let mockWorkspacePermissionService: jest.Mocked<Partial<WorkspacePermissionService>>;
  let mockUserNotificationService: jest.Mocked<Partial<UserNotificationService>>;
  let mockUserLanguageService: jest.Mocked<Partial<UserLanguageService>>;
  let mockUserJobService: jest.Mocked<Partial<UserJobService>>;
  let mockIdpWorkspaceRepository: jest.Mocked<Partial<Repository<IdpWorkspace>>>;
  let mockUserKeycloakService: jest.Mocked<Partial<UserKeycloakService>>;

  const workspaceId = uuidv4();
  const userId = uuidv4();
  const mockKCUserId = uuidv4();
  const languageId = 'en_US';
  const defaultLanguageIdDb = uuidv4();

  const baseUserData: UserDataWithMoreDto = {
    email: '<EMAIL>',
    name: 'Test User',
    language: languageId,
  };

  const mockUser = plainToInstance(User, {
    id: userId,
    email: '<EMAIL>',
    name: 'Test User',
    status: true,
    emailVerified: false,
    timeZone: DEFAULT_TIMEZONE,
    isUserIntegration: false,
    languageId: defaultLanguageIdDb,
  });

  const mockEmployeeInfo = {
    id: uuidv4(),
    userId: userId,
    director: 'Director Name',
  } as EmployeeInfoDto;

  beforeEach(async () => {
    mockUserRepository = {
      findByEmail: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
      update: jest.fn(),
      findById: jest.fn(),
    };
    mockUserRolesService = {
      addDefaultRole: jest.fn(),
      addRoles: jest.fn(),
    };
    mockPasswordService = {
      generatePassword: jest.fn(),
      updatePassword: jest.fn(),
    };
    mockUserIdentityService = {
      linkIdentityProvider: jest.fn(),
    };
    mockEmployeeInfoService = {
      findByUserAndWorkspace: jest.fn(),
      upsert: jest.fn(),
    };
    mockWorkspacePermissionService = {
      getDefaultPermissions: jest.fn(),
    };
    mockUserNotificationService = {
      notifyUserWithRoles: jest.fn(),
    };
    mockUserKeycloakService = {
      syncUserProfile: jest.fn(),
      createUser: jest.fn(),
    };
    mockUserLanguageService = {
      getLanguagePreferenceId: jest.fn().mockImplementation((lang) => {
        if (lang === languageId) return Promise.resolve(defaultLanguageIdDb);
        return Promise.resolve(defaultLanguageIdDb);
      }),
    };
    mockUserJobService = {
      setJobData: jest.fn(),
    };
    mockIdpWorkspaceRepository = {
      findOne: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserCreationService,
        { provide: UsersRepository, useValue: mockUserRepository },
        { provide: UserRolesService, useValue: mockUserRolesService },
        { provide: PasswordService, useValue: mockPasswordService },
        { provide: UserIdentityService, useValue: mockUserIdentityService },
        { provide: EmployeeInfoService, useValue: mockEmployeeInfoService },
        { provide: UserKeycloakService, useValue: mockUserKeycloakService },
        { provide: WorkspacePermissionService, useValue: mockWorkspacePermissionService },
        { provide: UserNotificationService, useValue: mockUserNotificationService },
        { provide: UserLanguageService, useValue: mockUserLanguageService },
        { provide: UserJobService, useValue: mockUserJobService },
        {
          provide: getRepositoryToken(IdpWorkspace),
          useValue: mockIdpWorkspaceRepository,
        },
      ],
    }).compile();

    service = module.get<UserCreationService>(UserCreationService);

    mockUserJobService.setJobData.mockImplementation(async (data) => data);
    mockUserLanguageService.getLanguagePreferenceId.mockResolvedValue(defaultLanguageIdDb);
    mockPasswordService.generatePassword.mockReturnValue('generated-temp-password');
    mockUserRepository.create.mockImplementation((data) => plainToInstance(User, data));
    mockUserRepository.save.mockImplementation(async (userPassedToSave) => {
      return plainToInstance(User, { ...mockUser, ...userPassedToSave, languageId: userPassedToSave.languageId });
    });
    mockEmployeeInfoService.findByUserAndWorkspace.mockResolvedValue(null);
    mockUserRolesService.addDefaultRole.mockResolvedValue(undefined);
    mockPasswordService.updatePassword.mockResolvedValue(undefined);
    mockEmployeeInfoService.upsert.mockResolvedValue(undefined);
    mockUserIdentityService.linkIdentityProvider.mockResolvedValue(undefined);
    mockWorkspacePermissionService.getDefaultPermissions.mockResolvedValue([]);
    mockUserRolesService.addRoles.mockResolvedValue([]);
    mockUserNotificationService.notifyUserWithRoles.mockResolvedValue(undefined);
    mockIdpWorkspaceRepository.findOne.mockResolvedValue({
      id: 'd3d3d3d3-1404-4c3a-add6-d944881a5874',
      workspaceId: 'workspace-id',
      idp: 'google',
      generatets: new Date(),
      workspace: null,
      genarate: () => {
        uuidv4();
      },
    } as IdpWorkspace);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createUser', () => {
    it('should create a new user if one does not exist', async () => {
      const userData = plainToInstance(UserDataWithMoreDto, baseUserData);
      const cleanedData = { email: userData.email, name: userData.name };

      mockUserRepository.findByEmail.mockResolvedValue(null);
      mockUserKeycloakService.createUser.mockResolvedValue(mockKCUserId);
      mockUserRepository.create.mockImplementation((data) => plainToInstance(User, data));
      mockUserRepository.save.mockImplementation(async (userPassedToSave) => {
        expect(userPassedToSave.id).toBe(mockKCUserId);
        expect(userPassedToSave.languageId).toBe(defaultLanguageIdDb);
        mockUserRepository.findById.mockResolvedValue({ ...mockUser, id: mockKCUserId } as User);
        return plainToInstance(User, { ...mockUser, ...userPassedToSave });
      });

      const result = await service.upsertUser(userData, workspaceId);

      expect(mockUserJobService.setJobData).toHaveBeenCalledWith(userData, workspaceId);

      expect(mockUserLanguageService.getLanguagePreferenceId).toHaveBeenCalledTimes(1);

      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(userData.email);
      expect(mockPasswordService.generatePassword).toHaveBeenCalledTimes(1);
      expect(mockUserRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          ...cleanedData,
          id: mockKCUserId,
          languageId: defaultLanguageIdDb,
        }),
      );
      expect(mockUserRepository.save).toHaveBeenCalled();
      expect(mockUserRolesService.addDefaultRole).toHaveBeenCalledWith(workspaceId, mockKCUserId, false);
      expect(mockPasswordService.updatePassword).toHaveBeenCalledWith(mockKCUserId, 'generated-temp-password', true);

      expect(result.user.id).toBe(mockKCUserId);
      expect(result.user.languageId).toBe(defaultLanguageIdDb);
      expect(result).toEqual({
        user: expect.objectContaining({
          id: mockKCUserId,
          email: userData.email,
          languageId: defaultLanguageIdDb,
        }),
        password: 'generated-temp-password',
      });
    });

    it('should update an existing user', async () => {
      const existingUserId = uuidv4();
      const existingUserLangId = uuidv4();
      const existingUser = plainToInstance(User, {
        ...mockUser,
        id: existingUserId,
        name: 'Old Name',
        languageId: existingUserLangId,
      });
      const userData = plainToInstance(UserDataWithMoreDto, { ...baseUserData, name: 'New Name', language: 'pt_BR' });
      const mappedNewLangId = uuidv4();

      mockUserRepository.findByEmail.mockResolvedValue(existingUser);
      mockUserLanguageService.getLanguagePreferenceId.mockResolvedValue(mappedNewLangId);

      // Update the existingUser object with the new languageId
      existingUser.languageId = mappedNewLangId;

      mockUserRepository.save.mockImplementation(async (userPassedToSave) => {
        expect(userPassedToSave.id).toBe(existingUserId);
        // Simulate updating the existingUser with the new values
        return plainToInstance(User, {
          ...existingUser,
          ...userPassedToSave,
        });
      });

      const result = await service.upsertUser(userData, workspaceId);

      expect(mockUserRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          id: existingUserId,
          name: 'New Name',
          email: userData.email,
          languageId: mappedNewLangId,
          status: existingUser.status,
          timeZone: existingUser.timeZone,
        }),
      );

      expect(result).toEqual({
        user: expect.objectContaining({
          id: existingUserId,
          name: 'New Name',
          languageId: mappedNewLangId,
        }),
        password: null,
      });
    });

    it('should use provided password and set temporary based on options', async () => {
      const userData = plainToInstance(UserDataWithMoreDto, { ...baseUserData, password: 'provided-password' });
      mockUserRepository.findByEmail.mockResolvedValue(null);
      mockUserRepository.save.mockImplementation(async (userPassedToSave) => {
        mockUserRepository.findById.mockResolvedValue({ ...mockUser, id: userId } as User);
        return plainToInstance(User, { ...mockUser, ...userPassedToSave, id: userId });
      });

      await service.upsertUser(userData, workspaceId, { temporaryPassword: false });
      expect(mockPasswordService.generatePassword).not.toHaveBeenCalled();
      expect(mockPasswordService.updatePassword).toHaveBeenCalledWith(userId, 'provided-password', false);

      jest.clearAllMocks();

      mockUserRepository.findByEmail.mockResolvedValue(null);
      mockUserLanguageService.getLanguagePreferenceId.mockResolvedValue(defaultLanguageIdDb);
      mockUserRepository.create.mockImplementation((data) => plainToInstance(User, data));

      mockUserRepository.save.mockImplementation(async (userPassedToSave) => {
        return plainToInstance(User, { ...mockUser, ...userPassedToSave, id: userId });
      });

      await service.upsertUser(userData, workspaceId, { temporaryPassword: true });
      expect(mockPasswordService.generatePassword).not.toHaveBeenCalled();
      expect(mockPasswordService.updatePassword).toHaveBeenCalledWith(userId, 'provided-password', true);
    });

    it('should update employee profile if provided and exists', async () => {
      const existingProfile = { ...mockEmployeeInfo, userId: mockUser.id };
      const profileData = plainToInstance(EmployeeInfoCreateDto, { director: 'New Director' });
      const userData = plainToInstance(UserDataWithMoreDto, { ...baseUserData, profile: profileData });

      mockUserRepository.findByEmail.mockResolvedValue(mockUser);
      mockEmployeeInfoService.findByUserAndWorkspace.mockResolvedValue(existingProfile);
      mockUserRepository.save.mockImplementation(async (userPassedToSave) => {
        return plainToInstance(User, { ...mockUser, ...userPassedToSave });
      });

      await service.upsertUser(userData, workspaceId);

      expect(mockUserRepository.save).toHaveBeenCalled();
      const savedUserId = mockUser.id;

      expect(mockEmployeeInfoService.upsert).toHaveBeenCalledWith(
        savedUserId,
        workspaceId,
        expect.objectContaining({
          director: 'New Director',
        }),
      );
    });

    it('should link identity provider if provided', async () => {
      const userData = plainToInstance(UserDataWithMoreDto, { ...baseUserData, idpAlias: 'google' });
      mockUserRepository.findByEmail.mockResolvedValue(null);
      mockUserRepository.save.mockImplementation(async (userPassedToSave) => {
        mockUserRepository.findById.mockResolvedValue({ ...mockUser, id: mockKCUserId } as User);
        return plainToInstance(User, { ...mockUser, ...userPassedToSave, id: mockKCUserId });
      });

      await service.upsertUser(userData, workspaceId);

      expect(mockUserRepository.save).toHaveBeenCalled();
      expect(mockUserIdentityService.linkIdentityProvider).toHaveBeenCalledWith(mockKCUserId, userData.email, 'google');
    });

    it('should correctly determine selfRegister flag based on password or identity_provider', async () => {
      mockUserRepository.findByEmail.mockResolvedValue(null);
      mockUserKeycloakService.createUser.mockResolvedValue(mockKCUserId);
      mockUserRepository.save.mockImplementation(async (userPassedToSave) => {
        mockUserRepository.findById.mockResolvedValue({ ...mockUser, id: mockKCUserId } as User);
        return plainToInstance(User, { ...mockUser, ...userPassedToSave, id: mockKCUserId });
      });

      let userData = plainToInstance(UserDataWithMoreDto, { ...baseUserData, password: 'some-password' });
      await service.upsertUser(userData, workspaceId, { selfRegister: true });
      expect(mockUserRolesService.addDefaultRole).toHaveBeenCalledWith(workspaceId, mockKCUserId, true);

      mockUserRolesService.addDefaultRole.mockClear();
      mockPasswordService.updatePassword.mockClear();

      userData = plainToInstance(UserDataWithMoreDto, { ...baseUserData, identity_provider: { alias: 'test' } });
      await service.upsertUser(userData, workspaceId, { selfRegister: true });
      expect(mockUserRolesService.addDefaultRole).toHaveBeenCalledWith(workspaceId, mockKCUserId, true);

      mockUserRolesService.addDefaultRole.mockClear();
      mockPasswordService.generatePassword.mockClear();
      mockPasswordService.updatePassword.mockClear();

      userData = plainToInstance(UserDataWithMoreDto, { ...baseUserData });
      await service.upsertUser(userData, workspaceId, { selfRegister: false });
      expect(mockUserRolesService.addDefaultRole).toHaveBeenCalledWith(workspaceId, mockKCUserId, false);
    });
  });

  describe('createUserWithPermissions', () => {
    beforeEach(() => {
      mockUserRepository.findByEmail.mockResolvedValue(null);
      mockPasswordService.generatePassword.mockReturnValue('generated-temp-password');
      mockUserKeycloakService.createUser.mockResolvedValue(mockKCUserId);
      mockUserRepository.save.mockImplementation(async (userPassedToSave) => {
        return plainToInstance(User, { ...mockUser, ...userPassedToSave, id: mockKCUserId });
      });
    });

    it('should call createUser, add roles, and notify for each added role', async () => {
      const userData = plainToInstance(UserDataWithMoreDto, { ...baseUserData });
      const permissions = ['perm1', 'perm2'];
      const finalPermissions = ['perm1', 'perm2', 'defaultPerm'];
      const addedRoles = [
        plainToInstance(UserRoleWorkspace, { id: 'role1' }),
        plainToInstance(UserRoleWorkspace, { id: 'role2' }),
      ];
      const generatedPassword = 'generated-temp-password';
      const isSelfSignup = false;

      mockWorkspacePermissionService.getDefaultPermissions.mockResolvedValue(finalPermissions);
      mockUserRolesService.addRoles.mockResolvedValue(addedRoles);
      mockUserRepository.findById.mockResolvedValue({ ...mockUser, id: mockKCUserId } as User);

      const result = await service.upsertUserWithPermissions(userData, permissions, workspaceId);

      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(userData.email);
      expect(mockUserKeycloakService.createUser).toHaveBeenCalled();
      expect(mockPasswordService.updatePassword).toHaveBeenCalledWith(mockKCUserId, generatedPassword, true);

      expect(mockWorkspacePermissionService.getDefaultPermissions).toHaveBeenCalledWith(workspaceId, permissions);
      expect(mockUserRolesService.addRoles).toHaveBeenCalledWith(
        workspaceId,
        mockKCUserId,
        finalPermissions,
        isSelfSignup,
      );

      expect(mockUserNotificationService.notifyUserWithRoles).toHaveBeenCalledTimes(1);
      expect(mockUserNotificationService.notifyUserWithRoles).toHaveBeenCalledWith(
        addedRoles,
        generatedPassword,
        isSelfSignup,
      );

      expect(result).toEqual({
        user: expect.objectContaining({ id: mockKCUserId }),
        password: generatedPassword,
        created: true,
      });
    });

    it('should determine isSelfSignup correctly for roles/notifications and password temporary flag', async () => {
      const userPassword = 'user-set-password';
      let userData = plainToInstance(UserDataWithMoreDto, { ...baseUserData, password: userPassword });
      const permissions = ['perm1'];
      const finalPermissions = ['perm1', 'defaultPerm'];
      const addedRoles = [plainToInstance(UserRoleWorkspace, { id: 'role-A' })];
      const isSelfSignup = true;

      mockPasswordService.generatePassword.mockClear();
      mockPasswordService.updatePassword.mockClear();

      mockWorkspacePermissionService.getDefaultPermissions.mockResolvedValue(finalPermissions);
      mockUserRolesService.addRoles.mockResolvedValue(addedRoles);
      mockUserRepository.findById.mockResolvedValue({ ...mockUser, id: mockKCUserId } as User);

      const result = await service.upsertUserWithPermissions(userData, permissions, workspaceId);

      expect(mockPasswordService.generatePassword).not.toHaveBeenCalled();
      expect(mockPasswordService.updatePassword).toHaveBeenCalledWith(mockKCUserId, userPassword, false);

      expect(mockUserRolesService.addRoles).toHaveBeenCalledWith(
        workspaceId,
        mockKCUserId,
        finalPermissions,
        isSelfSignup,
      );
      expect(mockUserNotificationService.notifyUserWithRoles).toHaveBeenCalledWith(
        addedRoles,
        userPassword,
        isSelfSignup,
      );

      expect(result.password).toBe(userPassword);
      expect(result.created).toBe(true);

      jest.clearAllMocks();
      mockUserRepository.findByEmail.mockResolvedValue(null);
      mockPasswordService.generatePassword.mockReturnValue('temp-pass-again');
      mockUserKeycloakService.createUser.mockResolvedValue(mockKCUserId);
      mockUserRepository.save.mockImplementation(async (userPassedToSave) => {
        return plainToInstance(User, { ...mockUser, ...userPassedToSave, id: mockKCUserId });
      });
      mockUserLanguageService.getLanguagePreferenceId.mockResolvedValue(defaultLanguageIdDb);
      mockUserRepository.create.mockImplementation((data) => plainToInstance(User, data));

      userData = plainToInstance(UserDataWithMoreDto, { ...baseUserData });
      const generatedPasswordCase2 = 'temp-pass-again';
      const isSelfSignupCase2 = false;
      mockWorkspacePermissionService.getDefaultPermissions.mockResolvedValue(finalPermissions);
      mockUserRolesService.addRoles.mockResolvedValue(addedRoles);

      const result2 = await service.upsertUserWithPermissions(userData, permissions, workspaceId);

      expect(mockPasswordService.generatePassword).toHaveBeenCalledTimes(1);
      expect(mockPasswordService.updatePassword).toHaveBeenCalledWith(mockKCUserId, generatedPasswordCase2, true);

      expect(mockUserRolesService.addRoles).toHaveBeenCalledWith(
        workspaceId,
        mockKCUserId,
        finalPermissions,
        isSelfSignupCase2,
      );
      expect(mockUserNotificationService.notifyUserWithRoles).toHaveBeenCalledWith(
        addedRoles,
        generatedPasswordCase2,
        isSelfSignupCase2,
      );

      expect(result2.password).toBe(generatedPasswordCase2);
      expect(result2.created).toBe(true);
    });
  });
});
