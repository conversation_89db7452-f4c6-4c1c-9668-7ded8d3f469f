import { Injectable } from '@nestjs/common';
import { JobFunctionsRepository } from '../../job/interfaces/job-functions.repository';
import { JobsRepository } from '../../job/interfaces/jobs.repository';
import { UserDataWithMoreDto } from '../../users/application/dtos/create-user.dto';

@Injectable()
export class UserJobService {
  constructor(
    private readonly jobRepository: JobsRepository,
    private readonly jobFunctionRepository: JobFunctionsRepository,
  ) {}

  async setJobData(userDataDto: UserDataWithMoreDto, workspaceId: string): Promise<UserDataWithMoreDto> {
    userDataDto.profile = userDataDto.profile || {};
    const jobName = userDataDto.job || userDataDto.profile?.job;
    const jobFunctionName = userDataDto.jobFunction || userDataDto.profile?.jobFunction;

    if (jobName) {
      const job = await this.jobRepository.findOrCreate(jobName.trim().toUpperCase(), workspaceId);
      userDataDto.profile.jobPositionId = job.id;
    }

    if (jobFunctionName) {
      const jobFunction = await this.jobFunctionRepository.findOrCreate(
        jobFunctionName.trim().toUpperCase(),
        workspaceId,
      );
      userDataDto.profile.jobFunctionId = jobFunction.id;
    }

    return userDataDto;
  }
}
