import { Injectable, Logger } from '@nestjs/common';
import { CreateUsersDto } from '../dtos/create-users.dto';
import { UserCreationService } from './user-creation.service';
import { BatchResultDto } from '../../users/application/dtos/create-user.dto';

@Injectable()
export class UserBatchService {
  private readonly logger = new Logger(UserBatchService.name);

  constructor(private readonly userCreatorService: UserCreationService) {}

  async saveUsers(createUsersDto: CreateUsersDto, workspaceId: string): Promise<BatchResultDto[]> {
    const results: BatchResultDto[] = [];

    for (const userData of createUsersDto.users) {
      try {
        const result = await this.userCreatorService.upsertUserWithPermissions(
          userData,
          createUsersDto.permissions,
          workspaceId,
        );
        results.push({ email: userData.email, id: result.user.id });
      } catch (error) {
        this.logger.error(`Failed to save user ${userData.email}: ${error.message}`, error.stack);
        results.push({ email: userData.email, error: error.message });
      }
    }

    return results;
  }
}
