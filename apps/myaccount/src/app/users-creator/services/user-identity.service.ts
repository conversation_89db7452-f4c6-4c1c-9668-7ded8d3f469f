import { Injectable } from '@nestjs/common';
import { KeycloakRepository } from '@keeps-node-apis/@core';

@Injectable()
export class UserIdentityService {
  constructor(private readonly keycloakRepository: KeycloakRepository) {}

  async linkIdentityProvider(userId: string, email: string, idpAlias: string): Promise<void> {
    if (!idpAlias) {
      return;
    }

    await this.keycloakRepository.addFederatedIdentity(userId, {
      identityProvider: idpAlias,
      userId: email,
      userName: email,
    });
  }
}
