import { Test, TestingModule } from '@nestjs/testing';
import { UserNotificationService } from './user-notification.service';
import { NotificationService, MobileNotificationStrategy } from '@keeps-node-apis/@core';
import { ConfigService } from '@nestjs/config';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserRoleWorkspace } from '../../entities/user-role-workspace.entity';
import { Role } from '../../entities/role.entity';
import { LanguagePreference } from '../../entities/language-preference.entity';
import UserRolesRepository from '../../users/domain/repositories/user-roles.repository';

describe('UserNotificationService', () => {
  let service: UserNotificationService;
  let notificationService: jest.Mocked<NotificationService>;
  let configService: jest.Mocked<ConfigService>;
  let mobileNotificationStrategy: jest.Mocked<MobileNotificationStrategy>;
  let userRoleWorkspaceRepository: jest.Mocked<Repository<UserRoleWorkspace>>;
  let mockUserRolesRepository: jest.Mocked<Partial<UserRolesRepository>>;

  let roleRepository: jest.Mocked<Repository<Role>>;

  const mockApplication = {
    id: 'konquest-id',
    name: 'Konquest',
  };

  const mockLanguage: Partial<LanguagePreference> = {
    id: 'lang-id',
    name: 'pt-br',
    status: true,
    createdDate: new Date(),
    updatedDate: new Date(),
  };

  const mockRole: Partial<Role> = {
    id: 'role-id',
    key: 'user-role',
    description: 'User Role',
    status: true,
    createdDate: new Date(),
    updatedDate: new Date(),
    application: mockApplication as any,
    applicationId: 'konquest-id',
    name: 'User Role',
  };

  const mockUserRoleWorkspace: Partial<UserRoleWorkspace> = {
    id: 'urw-id',
    status: true,
    createdDate: new Date(),
    updatedDate: new Date(),
    workspaceId: 'workspace-id',
    roleId: 'role-id',
    userId: 'user-id',
    selfSignUp: false,
    user: {
      id: 'user-id',
      email: '<EMAIL>',
      name: 'Test User',
      phone: '**********',
      language: mockLanguage as LanguagePreference,
      status: true,
      createdDate: new Date(),
      updatedDate: new Date(),
    } as any,
    role: mockRole as Role,
    workspace: {
      id: 'workspace-id',
      name: 'Test Workspace',
      logoUrl: 'logo-url',
      status: true,
      createdDate: new Date(),
      updatedDate: new Date(),
    } as any,
  };

  beforeEach(async () => {
    mockUserRolesRepository = {
      repository: {
        count: jest.fn(),
        findOne: jest.fn(),
      } as any,
    };

    const mockNotificationService = {
      notifyViaEmail: jest.fn(),
    };

    const mockConfigService = {
      get: jest.fn(),
    };

    const mockMobileNotificationStrategy = {
      sendNotification: jest.fn(),
    };

    const mockUserRoleWorkspaceRepo = {
      findOne: jest.fn(),
      count: jest.fn(),
      find: jest.fn().mockResolvedValue([mockUserRoleWorkspace]),
    };

    const mockRoleRepository = {
      find: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserNotificationService,
        {
          provide: NotificationService,
          useValue: mockNotificationService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: MobileNotificationStrategy,
          useValue: mockMobileNotificationStrategy,
        },
        {
          provide: getRepositoryToken(UserRoleWorkspace),
          useValue: mockUserRoleWorkspaceRepo,
        },
        {
          provide: UserRolesRepository,
          useValue: mockUserRolesRepository,
        },
        {
          provide: getRepositoryToken(Role),
          useValue: mockRoleRepository,
        },
      ],
    }).compile();

    service = module.get<UserNotificationService>(UserNotificationService);
    notificationService = module.get(NotificationService);
    configService = module.get(ConfigService);
    mobileNotificationStrategy = module.get(MobileNotificationStrategy);
    userRoleWorkspaceRepository = module.get(getRepositoryToken(UserRoleWorkspace));
    roleRepository = module.get(getRepositoryToken(Role));

    configService.get.mockImplementation((key: string) => {
      const config = {
        KONQUEST_ID: 'konquest-id',
        KONQUEST_WEB_URL: 'https://konquest.example.com',
        SMARTZAP_ADMIN_ROLE: 'smartzap-admin-role',
        SMARTZAP_WEB_URL: 'https://smartzap.example.com',
      };
      return config[key];
    });
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('notifyUserWithRoles', () => {
    beforeEach(() => {
      (mockUserRolesRepository.repository.findOne as jest.Mock).mockResolvedValue(
        mockUserRoleWorkspace as UserRoleWorkspace,
      );
      roleRepository.find.mockResolvedValue([mockRole as Role]);
      (mockUserRolesRepository.repository.count as jest.Mock).mockResolvedValue(1);
    });

    it('should send notification for new user with password', async () => {
      await service.notifyUserWithRoles([mockUserRoleWorkspace as UserRoleWorkspace], 'password123', true);

      expect(notificationService.notifyViaEmail).toHaveBeenCalledWith(
        expect.objectContaining({
          subject: 'KONQUEST_ACESS_EMAIL_SUBJECT',
          template: 'konquest_onboarding.html',
          workspaceId: 'workspace-id',
          language: 'pt-BR',
          templateData: expect.objectContaining({
            user_password: 'password123',
          }),
        }),
        '<EMAIL>',
      );
    });

    it('should send invite notification for existing user', async () => {
      await service.notifyUserWithRoles([mockUserRoleWorkspace as UserRoleWorkspace], undefined, false);

      expect(notificationService.notifyViaEmail).toHaveBeenCalledWith(
        expect.objectContaining({
          template: 'konquest_invite.html',
          templateData: expect.not.objectContaining({
            user_password: expect.anything(),
          }),
        }),
        '<EMAIL>',
      );
    });

    it('should not send notification if UserRoleWorkspaces array is empty', async () => {
      await service.notifyUserWithRoles([]);
      expect(notificationService.notifyViaEmail).not.toHaveBeenCalled();
    });

    it('should not send notification if application is not Konquest and not SmartZap admin', async () => {
      const differentApp = {
        ...mockUserRoleWorkspace,
        role: {
          ...mockRole,
          id: 'some-other-role',
          applicationId: 'other-app',
          application: { ...mockApplication, id: 'other-app' },
        },
      };
      (mockUserRolesRepository.repository.findOne as jest.Mock).mockResolvedValue(differentApp as any);

      await service.notifyUserWithRoles([differentApp as UserRoleWorkspace]);
      expect(notificationService.notifyViaEmail).not.toHaveBeenCalled();
    });

    it('should use default language if user language not set', async () => {
      const noLanguageUser = {
        ...mockUserRoleWorkspace,
        user: { ...mockUserRoleWorkspace.user, language: null },
      };
      (mockUserRolesRepository.repository.findOne as jest.Mock).mockResolvedValue(noLanguageUser as UserRoleWorkspace);

      await service.notifyUserWithRoles([noLanguageUser as UserRoleWorkspace], 'password123', true);

      expect(notificationService.notifyViaEmail).toHaveBeenCalledWith(
        expect.objectContaining({
          language: 'pt-BR',
        }),
        expect.anything(),
      );
    });

    it('should handle non-Portuguese language correctly', async () => {
      const englishLanguageUser = {
        ...mockUserRoleWorkspace,
        user: {
          ...mockUserRoleWorkspace.user,
          language: { ...mockLanguage, name: 'en' } as LanguagePreference,
        },
      };
      (mockUserRolesRepository.repository.findOne as jest.Mock).mockResolvedValue(
        englishLanguageUser as UserRoleWorkspace,
      );

      await service.notifyUserWithRoles([englishLanguageUser as UserRoleWorkspace]);

      expect(notificationService.notifyViaEmail).toHaveBeenCalledWith(
        expect.objectContaining({
          language: 'en',
        }),
        expect.anything(),
      );
    });

    it('should send notification for SmartZap admin role', async () => {
      const smartZapAdminRole = {
        ...mockUserRoleWorkspace,
        role: {
          ...mockRole,
          id: 'smartzap-admin-role',
          applicationId: 'other-app',
          application: { ...mockApplication, id: 'other-app' },
        },
      };
      (mockUserRolesRepository.repository.findOne as jest.Mock).mockResolvedValue(
        smartZapAdminRole as UserRoleWorkspace,
      );

      await service.notifyUserWithRoles([smartZapAdminRole as UserRoleWorkspace], 'password123', true);

      expect(notificationService.notifyViaEmail).toHaveBeenCalledWith(
        expect.objectContaining({
          subject: 'SMARTZAP_ACESS_EMAIL_SUBJECT',
          template: 'smartzap_onboarding.html',
          workspaceId: 'workspace-id',
          templateData: expect.objectContaining({
            app_web_link: 'https://smartzap.example.com',
            user_password: 'password123',
          }),
        }),
        '<EMAIL>',
      );
    });

    it('should send mobile notification when user has a phone number for Konquest onboarding', async () => {
      await service.notifyUserWithRoles([mockUserRoleWorkspace as UserRoleWorkspace], 'password123', true);

      expect(mobileNotificationStrategy.sendNotification).toHaveBeenCalledWith(
        'MYACCOUNT_KONQUEST_ONBOARDING',
        '**********',
        expect.objectContaining({
          '1': 'Test Workspace',
          '2': '<EMAIL>',
          '3': 'password123',
          '4': 'https://konquest.example.com',
        }),
        'pt-BR',
        'workspace-id',
      );
    });

    it('should send mobile notification when user has a phone number for Konquest invite', async () => {
      await service.notifyUserWithRoles([mockUserRoleWorkspace as UserRoleWorkspace], undefined, false);

      expect(mobileNotificationStrategy.sendNotification).toHaveBeenCalledWith(
        'MYACCOUNT_KONQUEST_INVITE',
        '**********',
        expect.objectContaining({
          '1': 'Test Workspace',
          '2': '<EMAIL>',
          '3': 'https://konquest.example.com',
        }),
        'pt-BR',
        'workspace-id',
      );
    });

    it('should send mobile notification when user has a phone number for SmartZap onboarding', async () => {
      const smartZapAdminRole = {
        ...mockUserRoleWorkspace,
        role: {
          ...mockRole,
          id: 'smartzap-admin-role',
          applicationId: 'other-app',
          application: { ...mockApplication, id: 'other-app' },
        },
      };
      userRoleWorkspaceRepository.findOne.mockResolvedValue(smartZapAdminRole as UserRoleWorkspace);

      await service.notifyUserWithRoles([smartZapAdminRole as UserRoleWorkspace], 'password123', true);

      expect(mobileNotificationStrategy.sendNotification).toHaveBeenCalledWith(
        'MYACCOUNT_SMARTZAP_ONBOARDING',
        '**********',
        expect.objectContaining({
          '1': 'Test Workspace',
          '2': '<EMAIL>',
          '3': 'password123',
          '4': 'https://smartzap.example.com',
        }),
        'pt-BR',
        'workspace-id',
      );
    });

    it('should not send mobile notification when user has no phone number', async () => {
      const noPhoneUser = {
        ...mockUserRoleWorkspace,
        user: { ...mockUserRoleWorkspace.user, phone: null },
      };
      (mockUserRolesRepository.repository.findOne as jest.Mock).mockResolvedValue(noPhoneUser as UserRoleWorkspace);

      await service.notifyUserWithRoles([noPhoneUser as UserRoleWorkspace], 'password123', true);

      expect(mobileNotificationStrategy.sendNotification).not.toHaveBeenCalled();
    });
  });
});
