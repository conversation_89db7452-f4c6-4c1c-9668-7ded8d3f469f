import { Injectable } from '@nestjs/common';
import { Metadata } from '@grpc/grpc-js';
import { Status } from '@grpc/grpc-js/build/src/constants';
import { plainToInstance } from 'class-transformer';
import { CreateUsersDto } from '../dtos/create-users.dto';
import { UserBatchService } from './user-batch.service';
import { BatchResultDto, UserDataWithMoreDto } from '../../users/application/dtos/create-user.dto';

@Injectable()
export class UserCreateGrpcService {
  constructor(private readonly userBatchService: UserBatchService) {}

  validateAndExtractWorkspaceId(metadata: Metadata): string {
    const workspaceId = metadata.get('x-client')[0];
    if (!workspaceId) {
      throw {
        code: Status.INVALID_ARGUMENT,
        message: 'Workspace ID (x-client) is required',
      };
    }
    return workspaceId.toString();
  }

  async processUserCreation(data: CreateUsersDto, workspaceId: string): Promise<BatchResultDto[]> {
    const createUsersDto = this.createUsersDtoFromRequest(data);
    const users = await this.userBatchService.saveUsers(createUsersDto, workspaceId);
    return users.map((user) => ({
      email: user.email,
      id: user.id,
      error: user.error || null,
    }));
  }

  private createUsersDtoFromRequest(data: CreateUsersDto): CreateUsersDto {
    return plainToInstance(CreateUsersDto, {
      users: data.users.map((user) => this.mapUserData(user)),
      permissions: data.permissions,
    });
  }

  private mapUserData(userData: any): UserDataWithMoreDto {
    const cleanedData = this.deepRemoveUnderscoreProperties(userData);
    return plainToInstance(UserDataWithMoreDto, cleanedData, {
      enableImplicitConversion: true,
    });
  }

  private deepRemoveUnderscoreProperties(obj: any): any {
    if (!obj || typeof obj !== 'object') {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map((item) => this.deepRemoveUnderscoreProperties(item));
    }

    const cleaned = {};
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key) && !key.startsWith('_')) {
        cleaned[key] = this.deepRemoveUnderscoreProperties(obj[key]);
      }
    }
    return cleaned;
  }
}
