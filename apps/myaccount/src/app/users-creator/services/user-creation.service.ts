import { Injectable } from '@nestjs/common';

import { PasswordService } from './password.service';
import { UserIdentityService } from './user-identity.service';
import { WorkspacePermissionService } from './workspace-permission.service';
import { UserNotificationService } from './user-notification.service';
import { EmployeeInfoService } from '../../users/application/services/employee-info.service';
import { UserRolesService } from '../../users/application/services/user-roles.service';
import { UsersRepository } from '../../users/domain/repositories/users.repository';
import { UserLanguageService } from './user-language.service';
import { UserJobService } from './user-job.service';
import { User } from '../../entities/user.entity';
import { UserSanitizer } from '../../users/application/user-sanitizer';
import { UserKeycloakService } from '../../users/application/services/user-keycloak.service';
import { validate as isUuid } from 'uuid';
import { UserDataDto, UserDataWithMoreDto } from '../../users/application/dtos/create-user.dto';

interface SaveUserResultDto {
  user: User;
  password?: string;
  created?: boolean;
}

@Injectable()
export class UserCreationService {
  constructor(
    private readonly userRepository: UsersRepository,
    private readonly userRolesService: UserRolesService,
    private readonly passwordService: PasswordService,
    private readonly userIdentityService: UserIdentityService,
    private readonly employeeInfoService: EmployeeInfoService,
    private readonly workspacePermissionService: WorkspacePermissionService,
    private readonly userNotificationService: UserNotificationService,
    private readonly userLanguageService: UserLanguageService,
    private readonly userJobService: UserJobService,
    private readonly userKeycloakService: UserKeycloakService,
  ) {}

  async getRelatedUserLeader(relatedUserLeader: string): Promise<string> {
    if (relatedUserLeader?.includes('@')) {
      const leader = await this.userRepository.findByEmail(relatedUserLeader);
      return leader?.id;
    }

    if (relatedUserLeader && isUuid(relatedUserLeader)) {
      return relatedUserLeader;
    }

    return null;
  }

  private async prepareUserData(userData: UserDataWithMoreDto, workspaceId: string): Promise<UserDataDto> {
    const enrichedData = await this.userJobService.setJobData(userData, workspaceId);
    const normalized = UserSanitizer.cleanUserData(enrichedData);

    normalized.languageId = await this.getLanguagePreferenceId(normalized);
    delete normalized.language;
    normalized.relatedUserLeaderId = await this.getRelatedUserLeader(normalized.relatedUserLeader);
    delete normalized.relatedUserLeader;

    return normalized as UserDataDto;
  }

  private async resolveOrCreateUser(
    userData: UserDataDto,
    workspaceId: string,
    selfRegister: boolean,
  ): Promise<{ user: User; password?: string }> {
    let user = await this.userRepository.findByEmail(userData.email);
    const password = userData.password || (!user ? this.passwordService.generatePassword() : null);

    if (!user) {
      user = await this.createNewUser(userData, workspaceId, selfRegister);
    } else {
      user = await this.updateUser(user, userData);
    }

    return { user, password };
  }

  private async handlePostUserCreationTasks(
    user: User,
    userData: UserDataDto,
    password: string | null,
    workspaceId: string,
    temporaryPassword: boolean,
  ): Promise<void> {
    if (password) {
      await this.passwordService.updatePassword(user.id, password, temporaryPassword);
    }

    const employeeInfo = userData.employeeInfo || userData.profile;
    if (employeeInfo) {
      employeeInfo.userId = user.id;
      await this.employeeInfoService.upsert(user.id, workspaceId, employeeInfo);
    }

    if (userData.idpAlias) {
      await this.userIdentityService.linkIdentityProvider(user.id, userData.email, userData.idpAlias);
    }
  }

  async upsertUser(
    userData: UserDataWithMoreDto,
    workspaceId: string,
    options: { temporaryPassword?: boolean; selfRegister?: boolean } = {},
  ): Promise<SaveUserResultDto> {
    const { temporaryPassword = true, selfRegister = false } = options;
    const preparedData = await this.prepareUserData(userData, workspaceId);

    const { user, password } = await this.resolveOrCreateUser(preparedData, workspaceId, selfRegister);
    await this.handlePostUserCreationTasks(user, preparedData, password, workspaceId, temporaryPassword);

    return { user, password };
  }

  private async updateUser(userToUpdate: User, updateDto: Partial<UserDataWithMoreDto>) {
    const user = Object.assign(userToUpdate, updateDto);
    const updatedUser = await this.userRepository.save(user);

    // todo: use events to update or create KC user data
    await this.userKeycloakService.syncUserProfile(updatedUser);
    return updatedUser;
  }

  private async createNewUser(
    userData: UserDataWithMoreDto,
    workspaceId: string,
    selfRegister: boolean,
  ): Promise<User> {
    // todo: use events to update or create KC user data
    const userId = await this.userKeycloakService.createUser(userData);
    const user = this.userRepository.create({
      ...userData,
      id: userId,
      languageId: userData.languageId,
      language: null,
      relatedUserLeader: null,
      employeeInfo: null,
    });

    await this.userRepository.save(user);
    await this.userRolesService.addDefaultRole(workspaceId, user.id, selfRegister);

    return this.userRepository.findById(user.id, workspaceId);
  }

  private async getLanguagePreferenceId(userData: UserDataWithMoreDto) {
    const languageName = userData.language;
    if (userData.languageId) return userData.languageId;
    return this.userLanguageService.getLanguagePreferenceId(languageName);
  }

  async upsertUserWithPermissions(
    userData: UserDataWithMoreDto,
    permissions: string[],
    workspaceId: string,
  ): Promise<SaveUserResultDto> {
    userData = UserSanitizer.cleanObject<UserDataWithMoreDto>(userData);

    const isSelfSignup = !!(userData.password || userData.idpAlias);
    const result = await this.upsertUser(userData, workspaceId, {
      selfRegister: isSelfSignup,
      temporaryPassword: !isSelfSignup,
    });
    const finalPermissions = await this.workspacePermissionService.getDefaultPermissions(workspaceId, permissions);
    const addedRoles = await this.userRolesService.addRoles(
      workspaceId,
      result.user.id,
      finalPermissions,
      isSelfSignup,
    );
    if (addedRoles.length > 0) {
      await this.userNotificationService.notifyUserWithRoles(addedRoles, result.password, isSelfSignup);
    }
    return { ...result, created: true };
  }
}
