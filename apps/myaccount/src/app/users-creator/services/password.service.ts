import { Injectable } from '@nestjs/common';
import { KeycloakRepository } from '@keeps-node-apis/@core';

@Injectable()
export class PasswordService {
  private readonly DIGITS = '0123456789';
  private readonly PASSWORD_LENGTH = 8;

  constructor(private readonly keycloakRepository: KeycloakRepository) {}

  generatePassword(): string {
    return Array.from(
      { length: this.PASSWORD_LENGTH },
      () => this.DIGITS[Math.floor(Math.random() * this.DIGITS.length)],
    ).join('');
  }

  async updatePassword(userId: string, password: string, temporary = true): Promise<void> {
    await this.keycloakRepository.resetUserPassword(userId, password, temporary);
  }
}
