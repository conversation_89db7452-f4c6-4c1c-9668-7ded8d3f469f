import { Injectable } from '@nestjs/common';
import { EmployeeInfo } from '../../../entities/employee-info.entity';
import { PageDto, PageOptionsWithSearchDto, paginate } from '@keeps-node-apis/@core';
import EmployeeInfosRepository from '../../domain/repositories/employee-infos.repository';
import { EmployeeInfoListParamsDto } from '../dtos/employee-info-list-params.dto';
import { EmployeeInfoDto } from '../dtos/employee-info.dto';
import { EmployeeInfoBaseCreateDto } from '../dtos/employee-info-create.dto';

@Injectable()
export class EmployeeInfoService {
  constructor(private readonly repository: EmployeeInfosRepository) {}

  async findAllowed(workspaceId: string, listParams: EmployeeInfoListParamsDto): Promise<PageDto<EmployeeInfoDto>> {
    const queryBuilder = this.repository.findAllowed(workspaceId, listParams);
    return await paginate<EmployeeInfo>(queryBuilder, listParams, 'user_profile_workspace');
  }

  async findUniqueValues(
    workspaceId: string,
    listParams: PageOptionsWithSearchDto,
    column: keyof EmployeeInfo,
  ): Promise<PageDto<string>> {
    const queryBuilder = this.repository.findUniqueValues(workspaceId, column, listParams?.search);
    const originalPage = await paginate(queryBuilder, listParams, 'user_profile_workspace', null, true);
    const managers = originalPage.items.map((item) => item[column]);
    return new PageDto(managers, listParams, originalPage.total) as PageDto<undefined>;
  }

  async upsert(userId: string, workspaceId: string, profileDto: EmployeeInfoBaseCreateDto): Promise<EmployeeInfo> {
    const employeeInfo = await this.findByUserAndWorkspace(userId, workspaceId);
    if (employeeInfo) {
      profileDto.id = employeeInfo.id;
      Object.assign(employeeInfo, { ...profileDto });
      return this.repository.repository.save(employeeInfo);
    }

    const newEmployeeInfo = this.repository.repository.create({ userId, workspaceId, ...profileDto });
    return this.repository.repository.save(newEmployeeInfo);
  }

  async findByUserAndWorkspace(userId: string, workspaceId: string): Promise<EmployeeInfoDto | null> {
    return this.repository.repository.findOneBy({
      userId,
      workspaceId,
    });
  }
}
