import { Injectable } from '@nestjs/common';
import { LanguagePreference } from '../../../entities/language-preference.entity';
import LanguagePreferencesRepository from '../../domain/repositories/language-preferences.repository';
import { LanguageDto } from '../dtos/language.dto';

@Injectable()
export class LanguagePreferencesService {
  constructor(private languagePreferencesRepository: LanguagePreferencesRepository) {}

  async findAll(): Promise<LanguageDto[]> {
    return await this.languagePreferencesRepository.repository.findBy({ status: true });
  }

  async findOneById(id: string): Promise<LanguagePreference> {
    return this.languagePreferencesRepository.repository.findOneByOrFail({ id, status: true });
  }
}
