import { Injectable, NotFoundException } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';

import { User } from '../../../entities/user.entity';
import { EmployeeInfo } from '../../../entities/employee-info.entity';
import { CannotDisableOwnAccountException } from '../../domain/exceptions/cannot-disable-own-account-exception';
import { AuthUser, KEEPS_ADMIN, KeycloakRepository, KpCacheService, MYACCOUNT_ROLES } from '@keeps-node-apis/@core';
import { RoleNotFoundException } from '../../domain/exceptions/role-not-found.exception';
import { UsersRepository } from '../../domain/repositories/users.repository';
import UserRolesRepository from '../../domain/repositories/user-roles.repository';
import { EmployeeInfoService } from './employee-info.service';
import { UserKeycloakService } from './user-keycloak.service';
import { UserSanitizer } from '../user-sanitizer';
import { UpdateUserDto } from '../dtos/update-user.dto';
import { UpdateUserStatusDto } from '../dtos/update-user-status.dto';
import { UserIdentityService } from '../../../users-creator/services/user-identity.service';

@Injectable()
export class UpdateUsersService {
  constructor(
    private readonly userRepository: UsersRepository,
    private readonly userKeycloakService: UserKeycloakService,
    private readonly employeeInfoService: EmployeeInfoService,
    private readonly userRolesRepository: UserRolesRepository,
    private readonly keycloakRepository: KeycloakRepository,
    private readonly userIdentityService: UserIdentityService,
    private readonly cache: KpCacheService,
  ) {}

  private async prepareUpdateData(data: UpdateUserDto): Promise<UpdateUserDto> {
    const clean = UserSanitizer.cleanUserData<UpdateUserDto>(data);

    if (!clean.relatedUserLeaderEmail) return clean;

    const leader = await this.userRepository.findByEmail(clean.relatedUserLeaderEmail);
    if (!leader) throw new NotFoundException(`Leader provided with email ${clean.relatedUserLeaderEmail} not found`);

    clean.relatedUserLeaderId = leader.id;
    delete clean.relatedUserLeaderEmail;

    return clean;
  }

  async update(
    authenticatedUser: AuthUser,
    userId: string,
    updateUserDto: UpdateUserDto,
    workspaceId: string,
  ): Promise<UpdateUserDto> {
    const updatedUserHasRoleWorkspace = await this.userRolesRepository.userHasRoleOnWorkspace(userId, workspaceId);
    const cannotUpdateUser = !this.canUpdateUser(authenticatedUser, userId) || !updatedUserHasRoleWorkspace;
    if (cannotUpdateUser) {
      throw new RoleNotFoundException();
    }

    const user = await this.getValidUser(userId);
    updateUserDto = await this.prepareUpdateData(updateUserDto);

    const updatedUser = await this.updateUserEntity(user, { ...updateUserDto, employeeInfo: null });

    let employeeInfo: EmployeeInfo;
    if (updateUserDto.employeeInfo) {
      employeeInfo = await this.employeeInfoService.upsert(userId, workspaceId, updateUserDto.employeeInfo);
    }

    const key = ['userinfo', userId, workspaceId];
    await this.cache.del(key);

    await this.userKeycloakService.syncUserProfile(updatedUser);
    if (updateUserDto.idpAlias) {
      await this.userIdentityService.linkIdentityProvider(userId, user.email, updateUserDto.idpAlias);
    }

    return this.buildResponseDto(updatedUser, employeeInfo);
  }

  async updateUserStatus(
    userId: string,
    authenticatedUserId: string,
    workspaceId: string,
    updateUserStatusDto: UpdateUserStatusDto,
  ): Promise<User> {
    const cannotUpdateUser = !(await this.userRolesRepository.userHasRoleOnWorkspace(userId, workspaceId));
    if (cannotUpdateUser) {
      throw new RoleNotFoundException();
    }

    const status = updateUserStatusDto.status;
    if (userId === authenticatedUserId && !status) {
      throw new CannotDisableOwnAccountException();
    }

    const user = await this.getValidUser(userId);
    await this.userRepository.update(userId, { status });
    user.status = status;
    this.keycloakRepository.updateUser(userId, { enabled: status });
    return user;
  }

  private async getValidUser(userId: string): Promise<User> {
    const user = await this.userRepository.findByIdAndNotIntegration(userId);
    if (!user) throw new NotFoundException(`User ${userId} not found`);
    return user;
  }

  private async updateUserEntity(user: User, data: Partial<User>): Promise<User> {
    const updatedUser = Object.assign(user, data);
    return this.userRepository.save(updatedUser);
  }

  private buildResponseDto(user: User, employeeInfo?: EmployeeInfo): UpdateUserDto {
    const userDto = plainToInstance(UpdateUserDto, user);
    if (employeeInfo) {
      const { workspaceId, ...employeeInfoData } = employeeInfo;
      return plainToInstance(UpdateUserDto, {
        ...userDto,
        ...employeeInfoData,
      });
    }
    return userDto;
  }

  private canUpdateUser(authUser: AuthUser, userId: string) {
    const isCompanyAdmin = authUser.roles?.includes(MYACCOUNT_ROLES.COMPANY_ADMIN);
    const isKeepsAdmin = authUser.roles?.includes(KEEPS_ADMIN);
    const isAccountAdmin = authUser.roles?.includes(MYACCOUNT_ROLES.ACCOUNT_ADMIN);
    const canUpdateOwnUser = authUser.sub === userId && isAccountAdmin;
    return isKeepsAdmin || isCompanyAdmin || canUpdateOwnUser;
  }
}
