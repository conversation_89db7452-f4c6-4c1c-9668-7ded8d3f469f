import { Injectable, Logger } from '@nestjs/common';
import { KeycloakCreateUserDTO, KeycloakUser, Locale, KeycloakRepository } from '@keeps-node-apis/@core';
import { LANGS_BY_ID } from '../../../users-creator/services/lang-map';

@Injectable()
export class UserKeycloakService {
  private readonly logger = new Logger(UserKeycloakService.name);

  constructor(private readonly keycloakRepository: KeycloakRepository) {}

  /**
   * Synchronizes user profile data (like locale, name, email) to Keycloak.
   */
  async syncUserProfile(user: { id: string; name: string; email: string; languageId?: string }): Promise<void> {
    const keycloakUser = this.userToKeycloakUser(user);

    try {
      await this.keycloakRepository.updateUser(user.id, keycloakUser);
    } catch (error) {
      this.logger.error(`Failed to sync user to Keycloak: ${user.email}`, error.stack);
      throw error;
    }
  }

  /**
   * Creates a new user in Keycloak and returns the assigned Keycloak ID.
   */
  async createUser(userData: { email: string; name: string; languageId?: string }): Promise<string> {
    const keycloakUserAlreadyCreatedBefore = await this.keycloakRepository.getUserByEmail(userData.email);
    if (keycloakUserAlreadyCreatedBefore) {
      await this.syncUserProfile({ ...userData, id: keycloakUserAlreadyCreatedBefore.id });
      return keycloakUserAlreadyCreatedBefore.id;
    }

    const keycloakUserDto = this.userToKeycloakUser(userData);
    const keycloakCreateUserDto = new KeycloakCreateUserDTO(
      keycloakUserDto.email,
      keycloakUserDto.firstName,
      keycloakUserDto.lastName,
      keycloakUserDto.attributes.locale[0],
      true,
    );

    try {
      await this.keycloakRepository.createUser(keycloakCreateUserDto);
    } catch (error) {
      this.logger.error(`Error creating Keycloak user: ${userData.email}`, error.stack);
      throw error;
    }

    const keycloakUserCreated = await this.keycloakRepository.getUserByEmail(userData.email);
    return keycloakUserCreated.id;
  }

  private splitUserName(fullName: string): [string, string] {
    const [firstName = '', ...lastNameParts] = fullName?.split(' ') || [];
    return [firstName, lastNameParts.join(' ') || ''];
  }

  private userToKeycloakUser(user: { name: string; email: string; languageId?: string }): KeycloakUser {
    const [firstName, lastName] = this.splitUserName(user.name);

    return {
      username: user.email,
      email: user.email,
      firstName,
      lastName,
      attributes: {
        locale: Locale[LANGS_BY_ID[user.languageId]] ?? Locale.PT_BR,
      },
    };
  }
}
