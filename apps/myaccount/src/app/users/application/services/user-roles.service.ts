import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { UserRoleWorkspace } from '../../../entities/user-role-workspace.entity';
import UserRolesRepository from '../../domain/repositories/user-roles.repository';
import { plainToInstance } from 'class-transformer';
import { NotAllowedException, PageDto, paginate } from '@keeps-node-apis/@core';
import { Role } from '../../../entities/role.entity';
import { In, Repository } from 'typeorm';
import { RoleNotFoundException } from '../../domain/exceptions/role-not-found.exception';
import { v4 } from 'uuid';
import { UserRoleListParamsDto } from '../dtos/user-role-list-params.dto';
import { UserRoleListDto } from '../dtos/user-role-list.dto';
import { RoleDto } from '../dtos/role.dto';
import { UserRoleCreateDto } from '../dtos/user-role-create.dto';

@Injectable()
export class UserRolesService {
  constructor(
    private userRolesWorkspace: UserRolesRepository,
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
  ) {}

  async verifyUserPermission(userId: string, workspaceId: string) {
    const userHasRoleOnWorkspace = await this.userRolesWorkspace.userHasRoleOnWorkspace(userId, workspaceId);
    if (!userHasRoleOnWorkspace) throw new NotAllowedException();
  }

  async findAllowed(
    userId: string,
    workspaceId: string,
    filters: UserRoleListParamsDto,
  ): Promise<PageDto<UserRoleListDto>> {
    await this.verifyUserPermission(userId, workspaceId);
    const query = this.userRolesWorkspace.findAllowed(workspaceId, filters);
    const pageDto = await paginate<UserRoleWorkspace>(query, filters, 'UserRoleWorkspace');
    return plainToInstance(PageDto<UserRoleListDto>, {
      ...pageDto,
      items: plainToInstance(UserRoleListDto, pageDto.items),
    });
  }

  async createAdminUser(userId: string, workspaceId: string): Promise<void> {
    const companyAdminRole = await this.roleRepository.findOneBy({ key: 'company_admin' });
    const userCompanyRole = this.userRolesWorkspace.repository.create({
      userId,
      workspaceId,
      role: companyAdminRole,
      selfSignUp: true,
    });
    await this.userRolesWorkspace.repository.save(userCompanyRole);

    const accountAdminRole = await this.roleRepository.findOneBy({ key: 'account_admin' });
    const userAccountRole = this.userRolesWorkspace.repository.create({
      userId,
      workspaceId,
      role: accountAdminRole,
    });
    await this.userRolesWorkspace.repository.save(userAccountRole);
  }

  async findUserWorkspace(userId: string, workspaceId: string, userAuthId: string) {
    await this.verifyUserPermission(userAuthId, workspaceId);
    const userRole = await this.userRolesWorkspace.repository.findBy({ userId, workspaceId });
    return plainToInstance(RoleDto, userRole);
  }

  async checkAllowedAddUserRole(userRoles: UserRoleWorkspace[], roleToAddOrRemove: UserRoleWorkspace) {
    const rolesKey = userRoles.map((userRole) => userRole.role.key);
    if (rolesKey.includes('keeps_admin')) return true;
    if (rolesKey.includes('company_admin') && roleToAddOrRemove.role.key !== 'keeps_admin') return true;
    return false;
  }

  async deleteUserRole(userRoleId: string, userLoggedId: string, isIntegration: boolean) {
    if (isIntegration) {
      return this.userRolesWorkspace.repository.delete(userRoleId);
    }
    const userRole = await this.userRolesWorkspace.repository.findOneOrFail({ where: { id: userRoleId } });
    const userLoggedRole = await this.userRolesWorkspace.repository.findBy({
      userId: userLoggedId,
      workspaceId: userRole.workspaceId,
    });
    if (!(await this.checkAllowedAddUserRole(userLoggedRole, userRole))) throw new NotAllowedException();
    return this.userRolesWorkspace.repository.delete(userRoleId);
  }

  async createUserRole(userRoleCreateDto: UserRoleCreateDto) {
    const role = this.userRolesWorkspace.repository.create(userRoleCreateDto);
    const roleWithRelations = await this.userRolesWorkspace.repository.findOneOrFail({
      where: { id: role.id },
      relations: ['role', 'user', 'workspace'],
    });
    return plainToInstance(UserRoleListDto, roleWithRelations);
  }

  async addDefaultRole(workspaceId: string, userId: string, selfSignUp = false): Promise<UserRoleWorkspace> {
    const accountAdminRole = await this.roleRepository.findOneBy({ key: 'account_admin' });

    if (!accountAdminRole) {
      throw new RoleNotFoundException();
    }

    let userRoleWorkspace = await this.userRolesWorkspace.repository.findOne({
      where: {
        userId,
        role: accountAdminRole,
        workspaceId,
      },
    });

    if (!userRoleWorkspace) {
      userRoleWorkspace = this.userRolesWorkspace.repository.create({
        id: v4(),
        userId,
        role: accountAdminRole,
        workspaceId,
        selfSignUp,
        status: true,
      });
    } else {
      userRoleWorkspace.selfSignUp = selfSignUp;
    }

    return await this.userRolesWorkspace.repository.save(userRoleWorkspace);
  }

  async addRoles(
    workspaceId: string,
    userId: string,
    roleIds: string[],
    selfSignUp = false,
  ): Promise<UserRoleWorkspace[]> {
    const roles = await this.roleRepository.findBy({
      id: In(roleIds),
    });
    const existingUserRoles = await this.userRolesWorkspace.repository.find({
      where: {
        userId,
        workspaceId,
      },
      relations: ['role'],
    });

    const existingRoleIds = new Set(existingUserRoles.map((ur) => ur.role.id));

    const newRoles = roles.filter((role) => !existingRoleIds.has(role.id));

    const userRolesWorkspaceToCreate = newRoles.map((role) => {
      return this.userRolesWorkspace.repository.create({
        id: v4(),
        userId,
        workspaceId,
        role,
        selfSignUp,
        status: true,
      });
    });

    const createdUserRolesWorkspace = await this.userRolesWorkspace.repository.save(userRolesWorkspaceToCreate);

    return createdUserRolesWorkspace;
  }
}
