import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>_ADMIN,
  KeycloakRepository,
  KpCacheService,
  MYACCOUNT_ROLES,
  NotAllowedException,
} from '@keeps-node-apis/@core';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { plainToInstance } from 'class-transformer';
import { PaginateQuery } from 'nestjs-paginate';
import { Workspace } from '../../../entities/workspace.entity';
import { WorkspacesRepository } from '../../../workspaces/repositories/workspaces-repository';
import { UsersRepository } from '../../domain/repositories/users.repository';
import { ChangePasswordDto } from '../dtos/change-password.dto';
import { UserSimpleResponseDto } from '../dtos/user-reponse.dto';
import { UserRolesService } from './user-roles.service';

@Injectable()
export class UsersService {
  constructor(
    private readonly userRepository: UsersRepository,
    @InjectRepository(Workspace)
    private readonly workspaceRepository: WorkspacesRepository,
    private readonly userRolesService: UserRolesService,
    private readonly cache: KpCacheService,
    private readonly keycloakRepository: KeycloakRepository,
  ) {}

  async findAllowed(userId: string, workspaceId: string, query: PaginateQuery) {
    await this.userRolesService.verifyUserPermission(userId, workspaceId);
    return this.userRepository.findAllowed(workspaceId, false, query);
  }

  async findOneById(userId: string, requestUser: AuthUser, workspaceId: string) {
    if (!this.canSeeUser(requestUser, userId)) {
      throw new NotAllowedException("Not Allowed to Retrieve users's data");
    }

    return this.userRepository.findInfo(userId, workspaceId);
  }

  async findInfo(userId: string, workspaceId: string): Promise<UserSimpleResponseDto> {
    // console.log('[MyAcc::User] findOne');
    const key = ['userinfo', userId, workspaceId];
    let dto: UserSimpleResponseDto = await this.cache.get(key);

    if (dto) {
      // console.log('cache hit');
      dto = plainToInstance(UserSimpleResponseDto, dto);
    } else {
      // console.log('cache miss');
      const user = await this.userRepository.findInfo(userId, workspaceId);

      dto = plainToInstance(UserSimpleResponseDto, user, {
        excludeExtraneousValues: true,
      });

      dto.languageName = user.language?.name;

      user.roles?.forEach((roleSource) => {
        const roleTarget = dto.roles.find((role) => role.id == roleSource.id);
        roleTarget.roleName = roleSource.role.name;
        roleTarget.key = roleSource.role.key;
        roleTarget.applicationId = roleSource.role.applicationId;
        roleTarget.applicationName = roleSource.role.application.name;
      });

      await this.cache.set(key, dto);
    }

    // console.log('[MyAcc:User] dto', dto);

    return dto;
  }

  async findAllLeaders(workspaceId: string) {
    return this.userRepository.findLeaders(workspaceId);
  }

  async setPassword(changePasswordDto: ChangePasswordDto) {
    this.keycloakRepository.resetUserPassword(
      changePasswordDto.userId,
      changePasswordDto.password,
      changePasswordDto.temporary,
    );
  }

  async setPasswordBatch(changePasswordsDto: ChangePasswordDto[]) {
    changePasswordsDto.forEach((changePasswordDto) => {
      this.keycloakRepository.resetUserPassword(
        changePasswordDto.userId,
        changePasswordDto.password,
        changePasswordDto.temporary,
      );
    });
  }

  async getUserRoles(userId: string, workspaceId: string): Promise<string[]> {
    const user = await this.findInfo(userId, workspaceId);
    if (!user) {
      return [];
    }

    return user.roles?.map((role) => role.key);
  }

  private canSeeUser(authUser: AuthUser, userId: string) {
    const isCompanyAdmin = authUser.roles?.includes(MYACCOUNT_ROLES.COMPANY_ADMIN);
    const isKeepsAdmin = authUser.roles?.includes(KEEPS_ADMIN);
    const isOwnAccountAdmin = authUser.roles?.includes(MYACCOUNT_ROLES.ACCOUNT_ADMIN);
    const canSeeOwnUser = authUser.sub === userId && isOwnAccountAdmin;
    return isKeepsAdmin || isCompanyAdmin || canSeeOwnUser;
  }
}
