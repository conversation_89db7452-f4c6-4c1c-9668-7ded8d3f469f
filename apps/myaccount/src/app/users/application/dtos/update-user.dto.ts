import { Expose, Transform, Type } from 'class-transformer';
import {
  IsDateString,
  IsEmail,
  IsOptional,
  IsString,
  Validate,
  ValidateNested,
  ValidationArguments,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsCPF } from '@keeps-node-apis/@core';
import { TransformCpf } from '../../../@core/common/transformers/cpf.transformer';
import { EmployeeInfoCreateDto } from './employee-info-create.dto';

@ValidatorConstraint({ name: 'relatedUserLeaderConstraint', async: true })
export class RelatedUserLeaderConstraint implements ValidatorConstraintInterface {
  validate(value: any, args: ValidationArguments) {
    const object = args.object as UpdateUserDto;
    return !(object.relatedUserLeaderEmail && object.relatedUserLeaderId);
  }

  defaultMessage(args: ValidationArguments) {
    return "Inform 'relatedUserLeaderEmail' or 'relatedUserLeaderId', never both";
  }
}

export class UpdateUserDto {
  @ApiProperty({
    type: String,
    description: 'Full name of the user',
    example: 'John Doe',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Expose()
  name?: string;

  @ApiProperty({
    type: String,
    description: 'Employee identification number',
    example: 'EMP123456',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Expose()
  ein?: string;

  @ApiProperty({
    type: String,
    description: "Email of the user's leader/manager",
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsEmail()
  @Expose()
  relatedUserLeaderEmail?: string;

  @ApiProperty({
    type: String,
    description: "ID of the user's leader/manager",
    example: '123e4567-e89b-12d3-a456-426614174000',
    required: false,
  })
  @IsOptional()
  @Validate(RelatedUserLeaderConstraint)
  @Expose()
  relatedUserLeaderId?: string;

  @ApiProperty({
    type: String,
    description: "User's date of birth (YYYY-MM-DD)",
    example: '1990-01-01',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  @Expose()
  birthday?: string | null;

  @ApiProperty({
    type: String,
    description: "User's preferred language ID",
    example: 'en-US',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Expose()
  languageId?: string;

  @ApiProperty({
    type: String,
    description: "User's nickname/display name",
    example: 'Johnny',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Expose()
  nickname?: string | null;

  @ApiProperty({
    type: String,
    description: 'Secondary email address of the user',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Expose()
  secondaryEmail?: string | null;

  @ApiProperty({
    type: String,
    description: "User's phone number",
    example: '+****************',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Expose()
  phone?: string | null;

  @ApiProperty({
    type: String,
    description: "User's gender",
    example: 'Male',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Expose()
  gender?: string | null;

  @ApiProperty({
    type: String,
    description: "User's address",
    example: '123 Main St, City, Country',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Expose()
  address?: string | null;

  @ApiProperty({
    type: String,
    description: "URL of the user's avatar/profile picture",
    example: 'https://example.com/avatars/user123.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Expose()
  avatar?: string | null;

  @ApiProperty({
    type: String,
    description: "User's country",
    example: 'United States',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Expose()
  country?: string | null;

  @ApiProperty({
    type: String,
    description: "User's timezone",
    example: 'America/New_York',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Expose()
  timeZone?: string;

  @ApiProperty({
    type: String,
    description: "User's admission date (YYYY-MM-DD)",
    example: '2023-01-15',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  @Expose()
  admissionDate?: string | null;

  @ApiProperty({
    type: String,
    description: 'Type of employment contract',
    example: 'Full-time',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Expose()
  contractType?: string | null;

  @ApiProperty({
    type: String,
    description: "User's tax ID number",
    example: '123.456.789-00',
    required: false,
  })
  @IsOptional()
  @IsCPF()
  @Transform(TransformCpf)
  cpf?: string | null;

  @ApiProperty({
    type: String,
    description: "User's education level",
    example: "Bachelor's Degree",
    required: false,
  })
  @IsOptional()
  @IsString()
  @Expose()
  education?: string | null;

  @ApiProperty({
    type: String,
    description: "User's ethnicity",
    example: 'Caucasian',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Expose()
  ethnicity?: string | null;

  @ApiProperty({
    type: String,
    description: "User's hierarchical level in the organization",
    example: 'Senior',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Expose()
  hierarchicalLevel?: string | null;

  @ApiProperty({
    type: String,
    description: "User's marital status",
    example: 'Married',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Expose()
  maritalStatus?: string | null;

  @ValidateNested()
  @Type(() => EmployeeInfoCreateDto)
  @IsOptional()
  @Expose()
  employeeInfo?: EmployeeInfoCreateDto;

  @ApiPropertyOptional({
    type: String,
    description: 'The identity provider alias',
    example: 'google-identity-platform',
  })
  @IsString()
  @IsOptional()
  idpAlias?: string;
}
