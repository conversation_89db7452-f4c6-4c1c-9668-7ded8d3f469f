import { ApiProperty } from '@nestjs/swagger';
import { RoleInfoDto } from './role-info.dto';
import { Expose, Type } from 'class-transformer';
import { WorkspaceSlimDto } from '../../../workspaces/dto/workspace-slim.dto';

export class RoleDto {
  @ApiProperty({ type: Boolean })
  @Expose()
  selfSignUp: boolean;

  @ApiProperty({ type: String })
  @Expose()
  id: string;

  @ApiProperty({ type: String })
  @Expose()
  createdDate: string;

  @ApiProperty({ type: String })
  @Expose()
  updatedDate: string;

  @ApiProperty({ type: String })
  @Expose()
  userId: string;

  @ApiProperty({ type: String })
  @Expose()
  roleId: string;

  @ApiProperty({ type: String })
  @Expose()
  workspaceId: string;

  @ApiProperty({ type: RoleInfoDto })
  @Expose()
  @Type(() => RoleInfoDto)
  role: RoleInfoDto;

  @ApiProperty({ type: WorkspaceSlimDto })
  @Expose()
  @Type(() => WorkspaceSlimDto)
  workspace: WorkspaceSlimDto;
}

export class RoleSimpleDto {
  @ApiProperty({ type: String })
  @Expose()
  id: string;

  @ApiProperty({ type: String })
  @Expose()
  key: string;

  @ApiProperty({ type: String })
  @Expose()
  roleId: string;

  @ApiProperty({ type: String })
  @Expose()
  roleName: string;

  @ApiProperty({ type: String })
  @Expose()
  workspaceId: string;

  @ApiProperty({ type: String })
  @Expose()
  applicationId: string;

  @ApiProperty({ type: String })
  @Expose()
  applicationName: string;
}
