import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { JobFunctionDto } from '../../../job/presentation/dtos/job-function.dto';
import { JobDto } from '../../../job/presentation/dtos/job.dto';

class UserSlimDto {
  @ApiProperty({ type: String })
  @Expose()
  id: string;

  @ApiProperty({ type: String })
  @Expose()
  name: string;

  @ApiProperty({ type: String })
  @Expose()
  email: string;
}

export class EmployeeInfoDto {
  @ApiProperty({ type: String })
  @Expose()
  id: string;

  @ApiProperty({ type: Date })
  @Expose()
  createdDate: Date;

  @ApiProperty({ type: Date })
  @Expose()
  updatedDate: Date;

  @ApiProperty({ type: UserSlimDto })
  @Expose()
  @Type(() => UserSlimDto)
  user: UserSlimDto;

  @ApiProperty({ type: String })
  @Expose()
  userId: string;

  @ApiProperty({ type: JobFunctionDto })
  @Type(() => JobFunctionDto)
  @Expose()
  jobFunction: JobFunctionDto;

  @ApiProperty({ type: JobDto })
  @Type(() => JobDto)
  @Expose()
  jobPosition: JobDto;

  @ApiProperty({ type: String })
  @Expose()
  director: string;

  @ApiProperty({ type: String })
  @Expose()
  manager: string;

  @ApiProperty({ type: String })
  @Expose()
  areaOfActivity: string;
}

export class EmployeeInfoManagerDto {
  @ApiProperty({ type: String })
  @Expose()
  manager: string;
}
