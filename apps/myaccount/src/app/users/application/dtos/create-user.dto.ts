import {
  IsBoolean,
  IsDate<PERSON>tring,
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsTimeZone,
  IsUUID,
  ValidateNested,
} from 'class-validator';
import { Expose, Transform, Type } from 'class-transformer';
import { EmployeeInfoBaseCreateDto, EmployeeInfoCreateDto } from './employee-info-create.dto';
import { IsCPF } from '@keeps-node-apis/@core';
import { TransformCpf } from '../../../@core/common/transformers/cpf.transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class UserDataDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsOptional()
  ein?: string;

  @IsString()
  @IsOptional()
  job?: string;

  @IsString()
  @IsOptional()
  jobFunction?: string;

  @IsString()
  @IsOptional()
  password?: string;

  @ApiPropertyOptional({
    type: String,
    description: 'The identity provider alias',
    example: 'google-identity-platform',
  })
  @IsString()
  @IsOptional()
  idpAlias?: string;

  @ValidateNested()
  @Type(() => EmployeeInfoCreateDto)
  @IsOptional()
  profile?: EmployeeInfoCreateDto;

  @ValidateNested()
  @Type(() => EmployeeInfoBaseCreateDto)
  @IsOptional()
  employeeInfo?: EmployeeInfoBaseCreateDto;

  @IsString()
  @IsOptional()
  nickname?: string;

  @IsEmail()
  @IsOptional()
  secondaryEmail?: string;

  @IsString()
  @IsOptional()
  phone?: string;

  @IsString()
  @IsOptional()
  gender?: string;

  @IsDateString()
  @IsOptional()
  birthday?: string;

  @IsString()
  @IsOptional()
  address?: string;

  @IsString()
  @IsOptional()
  avatar?: string;

  @IsBoolean()
  @IsOptional()
  status?: boolean;

  @IsUUID()
  @IsOptional()
  languageId?: string;

  @IsString()
  @IsOptional()
  country?: string;

  @IsUUID()
  @IsOptional()
  relatedUserLeaderId?: string;

  @IsBoolean()
  @IsOptional()
  emailVerified?: boolean;

  @IsTimeZone()
  @IsOptional()
  timeZone?: string;

  @IsDateString()
  @IsOptional()
  admissionDate?: string;

  @IsString()
  @IsOptional()
  contractType?: string;

  @IsOptional()
  @IsCPF()
  @Transform(TransformCpf)
  cpf?: string;

  @IsString()
  @IsOptional()
  education?: string;

  @IsString()
  @IsOptional()
  ethnicity?: string;

  @IsString()
  @IsOptional()
  hierarchicalLevel?: string;

  @IsString()
  @IsOptional()
  maritalStatus?: string;
}

export class UserDataWithMoreDto extends UserDataDto {
  @IsString()
  @IsOptional()
  language?: string;

  @IsString()
  @IsOptional()
  relatedUserLeader?: string;
}

export class BatchResultDto {
  @Expose()
  email: string;

  @Expose()
  error?: string;

  @Expose()
  id?: string;
}
