import { Exclude } from 'class-transformer';
import { EmployeeInfoDto } from './employee-info.dto';
import { RoleDto } from './role.dto';
import { UserResponseDto } from './user-reponse.dto';

export class UserDetailPublicResultDto extends UserResponseDto {
  @Exclude()
  status: boolean;

  @Exclude()
  timeZone: string;

  @Exclude()
  createdDate: string;

  @Exclude()
  updatedDate: string;

  @Exclude()
  nickname: string;

  @Exclude()
  emailVerified: boolean;

  @Exclude()
  secondaryEmail: string;

  @Exclude()
  gender: string;

  @Exclude()
  birthday: string;

  @Exclude()
  address: string;

  @Exclude()
  relatedUserLeaderId: string;

  @Exclude()
  roles: RoleDto[];

  @Exclude()
  employeeInfo: EmployeeInfoDto;

  @Exclude()
  relatedUserLeader: UserResponseDto;
}
