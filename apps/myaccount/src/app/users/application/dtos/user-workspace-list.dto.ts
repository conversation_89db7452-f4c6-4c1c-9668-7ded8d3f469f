import { PickType } from '@nestjs/mapped-types';
import { Expose, Type } from 'class-transformer';
import { RoleDto } from './role.dto';
import { ApiProperty } from '@nestjs/swagger';
import { WorkspaceDto } from '../../../workspaces/dto/workspace.dto';

export class UserWorkspaceListDto extends PickType(WorkspaceDto, ['id', 'name']) {
  @ApiProperty({ type: RoleDto })
  @Expose()
  @Type(() => RoleDto)
  roles: RoleDto[];
}
