import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { EmployeeInfoDto } from './employee-info.dto';
import { RoleDto, RoleSimpleDto } from './role.dto';
import { LanguageDto } from './language.dto';
import { User } from '../../../entities/user.entity';

export class UserResponseDto {
  @ApiProperty({
    type: Boolean,
    description: 'Whether the user is active or not',
    example: true,
  })
  @Expose()
  status: boolean;

  @ApiProperty({
    type: String,
    description: "User's timezone",
    example: 'America/New_York',
  })
  @Expose()
  timeZone: string;

  @ApiProperty({
    type: String,
    description: 'Unique identifier of the user',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  id: string;

  @ApiProperty({
    type: String,
    description: 'Record creation timestamp',
    example: '2024-01-15T10:30:00Z',
  })
  @Expose()
  createdDate: string;

  @ApiProperty({
    type: String,
    description: 'Record last update timestamp',
    example: '2024-01-16T15:45:00Z',
  })
  @Expose()
  updatedDate: string;

  @ApiProperty({
    type: String,
    description: 'Full name of the user',
    example: 'John Doe',
  })
  @Expose()
  name: string;

  @ApiProperty({
    type: String,
    description: "User's nickname/display name",
    example: 'Johnny',
  })
  @Expose()
  nickname: string;

  @ApiProperty({
    type: String,
    description: 'Primary email address',
    example: '<EMAIL>',
  })
  @Expose()
  email: string;

  @ApiProperty({
    type: Boolean,
    description: 'Indicates if the email has been verified',
    example: true,
  })
  @Expose()
  emailVerified: boolean;

  @ApiProperty({
    type: String,
    description: 'Secondary email address',
    example: '<EMAIL>',
  })
  @Expose()
  secondaryEmail: string;

  @ApiProperty({
    type: String,
    description: 'Phone number',
    example: '+****************',
  })
  @Expose()
  phone: string;

  @ApiProperty({
    type: String,
    description: "URL of the user's avatar/profile picture",
    example: 'https://example.com/avatars/user123.jpg',
  })
  @Expose()
  avatar: string;

  @ApiProperty({
    type: String,
    description: "User's gender",
    example: 'Male',
  })
  @Expose()
  gender: string;

  @ApiProperty({
    type: String,
    description: 'Job title/role',
    example: 'Senior Software Engineer',
  })
  @Expose()
  job: string;

  @ApiProperty({
    type: String,
    description: 'Date of birth (YYYY-MM-DD)',
    example: '1990-01-01',
  })
  @Expose()
  birthday: string;

  @ApiProperty({
    type: String,
    description: 'Full address',
    example: '123 Main St, City, Country',
  })
  @Expose()
  address: string;

  @ApiProperty({
    type: String,
    description: 'Country of residence',
    example: 'United States',
  })
  @Expose()
  country: string;

  @ApiProperty({
    type: String,
    description: 'Preferred language identifier',
    example: 'en-US',
  })
  @Expose()
  languageId: string;

  @ApiProperty({
    type: String,
    description: 'Employee identification number',
    example: 'EMP123456',
  })
  @Expose()
  ein: string;

  @ApiProperty({
    type: String,
    description: 'ID of the direct leader/manager',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  relatedUserLeaderId: string;

  @ApiProperty({
    type: LanguageDto,
    description: 'Detailed language preferences information',
  })
  @Expose()
  @Type(() => LanguageDto)
  language: LanguageDto;

  @ApiProperty({
    type: [RoleDto],
    description: 'List of user roles and permissions',
  })
  @Expose()
  @Type(() => RoleDto)
  roles: RoleDto[];

  @ApiProperty({
    type: EmployeeInfoDto,
    description: 'Additional employee information',
  })
  @Expose()
  @Type(() => EmployeeInfoDto)
  employeeInfo: EmployeeInfoDto;

  @ApiProperty({
    type: User,
    description: "Information about the user's leader/manager",
  })
  @Expose()
  @Type(() => UserResponseDto)
  relatedUserLeader: UserResponseDto;
}

export class UserSimpleResponseDto {
  @ApiProperty({
    type: String,
    description: 'Unique identifier of the user',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  id: string;

  @ApiProperty({
    type: String,
    description: 'Full name of the user',
    example: 'John Doe',
  })
  @Expose()
  name: string;

  @ApiProperty({
    type: String,
    description: "User's nickname/display name",
    example: 'Johnny',
  })
  @Expose()
  nickname: string;

  @ApiProperty({
    type: String,
    description: 'Primary email address',
    example: '<EMAIL>',
  })
  @Expose()
  email: string;

  @ApiProperty({
    type: String,
    description: 'Secondary email address',
    example: '<EMAIL>',
  })
  @Expose()
  secondaryEmail: string;

  @ApiProperty({
    type: String,
    description: 'Phone number',
    example: '+****************',
  })
  @Expose()
  phone: string;

  @ApiProperty({
    type: String,
    description: "URL of the user's avatar/profile picture",
    example: 'https://example.com/avatars/user123.jpg',
  })
  @Expose()
  avatar: string;

  @ApiProperty({
    type: String,
    description: "User's gender",
    example: 'Male',
  })
  @Expose()
  gender: string;

  @ApiProperty({
    type: String,
    description: 'Job title/role',
    example: 'Senior Software Engineer',
  })
  @Expose()
  job: string;

  @ApiProperty({
    type: String,
    description: 'Date of birth (YYYY-MM-DD)',
    example: '1990-01-01',
  })
  @Expose()
  birthday: string;

  @ApiProperty({
    type: String,
    description: 'Full address',
    example: '123 Main St, City, Country',
  })
  @Expose()
  address: string;

  @ApiProperty({
    type: String,
    description: 'Country of residence',
    example: 'United States',
  })
  @Expose()
  country: string;

  @ApiProperty({
    type: Boolean,
    description: 'User status (active/inactive)',
    example: true,
  })
  @Expose()
  status: boolean;

  @ApiProperty({
    type: String,
    description: "User's timezone",
    example: 'America/New_York',
  })
  @Expose()
  timeZone: string;

  @ApiProperty({
    type: String,
    description: 'Preferred language identifier',
    example: 'en-US',
  })
  @Expose()
  languageId: string;

  @ApiProperty({
    type: String,
    description: 'Name of the preferred language',
    example: 'English (US)',
  })
  @Expose()
  languageName: string;

  @ApiProperty({
    type: [RoleSimpleDto],
    description: 'Simplified list of user roles and permissions',
  })
  @Expose()
  @Type(() => RoleSimpleDto)
  roles: RoleSimpleDto[];
}
