import { UserResponseDto } from './user-reponse.dto';
import { RoleInfoDto } from './role-info.dto';
import { IsBoolean, IsOptional, IsUUID, IsNotEmpty } from 'class-validator';
import { WorkspaceSlimDto } from '../../../workspaces/dto/workspace-slim.dto';

export class UserRoleCreateDto {
  @IsNotEmpty()
  @IsUUID()
  user: UserResponseDto;

  @IsNotEmpty()
  @IsUUID()
  role: RoleInfoDto;

  @IsNotEmpty()
  @IsUUID()
  workspace: WorkspaceSlimDto;

  @IsBoolean()
  @IsOptional()
  self_sign_up: boolean;
}
