import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional, IsString, IsUUID } from 'class-validator';

export class EmployeeInfoBaseCreateDto {
  @ApiProperty({ type: String, example: 'b1b9298e-1494-4638-976e-501eac013641' })
  @Expose()
  @IsUUID()
  @IsOptional()
  id?: string;

  @ApiProperty({ type: String, example: 'b1b9298e-1494-4638-976e-501eac013641' })
  @IsUUID()
  @IsOptional()
  jobFunctionId?: string;

  @ApiProperty({ type: String, example: 'b1b9298e-1494-4638-976e-501eac013641' })
  @IsUUID()
  @IsOptional()
  jobPositionId?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  @IsString()
  director?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  @IsString()
  manager?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  @IsString()
  areaOfActivity?: string;

  userId?: string;
  workspaceId?: string;
}

export class EmployeeInfoCreateDto extends EmployeeInfoBaseCreateDto {
  @ApiProperty({ type: String, example: 'b1b9298e-1494-4638-976e-501eac013641' })
  @IsString()
  @IsOptional()
  jobFunction?: string;

  @ApiProperty({ type: String, example: 'b1b9298e-1494-4638-976e-501eac013641' })
  @IsString()
  @IsOptional()
  job?: string;
}
