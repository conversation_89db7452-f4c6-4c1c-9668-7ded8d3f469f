import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { ApplicationDto } from '../../../workspaces/dto/application.dto';

export class RoleInfoDto {
  @ApiProperty({ type: Boolean })
  @Expose()
  status: boolean;

  @ApiProperty({ type: String })
  @Expose()
  id: string;

  @ApiProperty({ type: String })
  @Expose()
  createdDate: string;

  @ApiProperty({ type: String })
  @Expose()
  updatedDate: string;

  @ApiProperty({ type: String })
  @Expose()
  name: string;

  @ApiProperty({ type: String })
  @Expose()
  key: string;

  @ApiProperty({ type: String })
  @Expose()
  description: string;

  @ApiProperty({ type: String })
  @Expose()
  applicationId: string;

  @ApiProperty({ type: ApplicationDto })
  @Expose()
  @Type(() => ApplicationDto)
  application: ApplicationDto;
}
