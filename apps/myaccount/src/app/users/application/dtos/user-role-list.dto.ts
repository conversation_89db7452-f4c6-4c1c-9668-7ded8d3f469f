import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { UserResponseDto } from './user-reponse.dto';
import { PickType } from '@nestjs/mapped-types';
import { RoleInfoDto } from './role-info.dto';
import { WorkspaceDto } from '../../../workspaces/dto/workspace.dto';
import { WorkspaceSlimDto } from '../../../workspaces/dto/workspace-slim.dto';

export class UserSlimDto extends PickType(UserResponseDto, [
  'id',
  'name',
  'email',
  'phone',
  'job',
  'ein',
  'country',
  'language',
  'avatar',
] as const) {}

export class UserRoleListDto {
  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty({ type: UserSlimDto })
  @Expose()
  @Type(() => UserSlimDto)
  user: UserSlimDto;

  @ApiProperty({ type: RoleInfoDto })
  @Expose()
  @Type(() => RoleInfoDto)
  role: RoleInfoDto;

  @ApiProperty({ type: WorkspaceDto })
  @Expose()
  @Type(() => WorkspaceSlimDto)
  workspace: WorkspaceSlimDto;
}
