import { PageOptionsDto, ToBoolean } from '@keeps-node-apis/@core';
import { Type } from 'class-transformer';
import { IsOptional, IsUUID } from 'class-validator';
import { ApiProperty, IntersectionType } from '@nestjs/swagger';

export class UserRoleListParamsDto extends IntersectionType(PageOptionsDto) {
  @IsOptional()
  @IsUUID()
  @ApiProperty({ required: false, description: 'The role id' })
  readonly roleId?: string;

  @IsOptional()
  @Type(() => Boolean)
  @ToBoolean()
  @ApiProperty({ required: false, description: 'The activation status of the user' })
  readonly userStatus?: boolean;
}
