import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsString, IsUUID, Max<PERSON>ength, MinLength } from 'class-validator';

export class ChangePasswordDto {
  @ApiProperty({
    type: String,
    description: 'User ID whose password will be changed',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  userId: string;

  @ApiProperty({
    type: String,
    description: 'New password for the user',
    example: 'NewP@ssw0rd123',
    minLength: 8,
    maxLength: 50,
  })
  @IsString()
  @MaxLength(50)
  @MinLength(8)
  password: string;

  @ApiProperty({
    type: Boolean,
    description: 'Indicates if the password is temporary and must be changed on next login',
    example: true,
    default: false,
  })
  @IsBoolean()
  temporary = false;
}
