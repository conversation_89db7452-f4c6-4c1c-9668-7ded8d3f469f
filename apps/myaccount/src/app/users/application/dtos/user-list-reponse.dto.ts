import { ApiProperty, PickType } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { EmployeeInfoDto } from './employee-info.dto';
import { LanguageDto } from './language.dto';
import { RoleInfoDto } from './role-info.dto';
import { UserResponseDto } from './user-reponse.dto';
import { ApplicationDto } from '../../../workspaces/dto/application.dto';
import { WorkspaceDto } from '../../../workspaces/dto/workspace.dto';
import { JobDto } from '../../../job/presentation/dtos/job.dto';
import { JobFunctionDto } from '../../../job/presentation/dtos/job-function.dto';
import { BasicUserResponseDto } from './basic-user-response.dto';

class SimplifiedApplicationDto extends PickType(ApplicationDto, ['id', 'name'] as const) {}

class SimplifiedRoleDto extends PickType(RoleInfoDto, ['id', 'name'] as const) {
  @Expose()
  @Type(() => SimplifiedApplicationDto)
  @ApiProperty({ type: SimplifiedApplicationDto, description: 'Aplicação relacionada ao papel' })
  application: SimplifiedApplicationDto;
}

class SimplifiedWorkspaceDto extends PickType(WorkspaceDto, ['id', 'name'] as const) {}

class SimplifiedUserRoleDto extends PickType(WorkspaceDto, ['id', 'name'] as const) {
  @Expose()
  @Type(() => SimplifiedRoleDto)
  @ApiProperty({ type: SimplifiedRoleDto })
  role: SimplifiedRoleDto;

  @Expose()
  @Type(() => SimplifiedWorkspaceDto)
  @ApiProperty({ type: SimplifiedWorkspaceDto })
  workspace: SimplifiedWorkspaceDto;
}

class SimplifiedLanguageDto extends PickType(LanguageDto, ['name'] as const) {}

class SimplifiedJobDto extends PickType(JobDto, ['name'] as const) {}

class SimplifiedFunctionDto extends PickType(JobFunctionDto, ['name'] as const) {}

class SimplifiedEmployeeInfoDto extends PickType(EmployeeInfoDto, [
  'id',
  'director',
  'manager',
  'areaOfActivity',
] as const) {
  @Expose()
  @Type(() => SimplifiedJobDto)
  @ApiProperty({ type: SimplifiedJobDto })
  jobPosition: SimplifiedJobDto;

  @Expose()
  @Type(() => SimplifiedFunctionDto)
  @ApiProperty({ type: SimplifiedFunctionDto })
  jobFunction: SimplifiedFunctionDto;
}

export class UserListResponseDto extends PickType(UserResponseDto, [
  'id',
  'name',
  'email',
  'phone',
  'avatar',
  'status',
  'createdDate',
] as const) {
  @Expose()
  @Type(() => SimplifiedUserRoleDto)
  @ApiProperty({ type: [SimplifiedUserRoleDto] })
  roles: SimplifiedUserRoleDto[];

  @Expose()
  @Type(() => SimplifiedLanguageDto)
  @ApiProperty({ type: SimplifiedLanguageDto })
  language: SimplifiedLanguageDto;

  @Expose()
  @Type(() => BasicUserResponseDto)
  @ApiProperty({ type: BasicUserResponseDto })
  relatedUserLeader: BasicUserResponseDto;

  @Expose()
  @Type(() => SimplifiedEmployeeInfoDto)
  @ApiProperty({ type: [SimplifiedEmployeeInfoDto] })
  employeeInfos: SimplifiedEmployeeInfoDto[];
}
