import { User } from '../../entities/user.entity';
import { UserDataWithMoreDto } from './dtos/create-user.dto';
import { InvalidEmailException } from '../domain/exceptions/invalid-email.exception';

type SupportedUserDtos = Partial<UserDataWithMoreDto> | Partial<User>;

export class UserSanitizer {
  /**
   * Cleans user data fields such as email, ein, phone, and name.
   * Works with both CreateUserDto and UpdateUserDto shapes.
   */
  static cleanUserData<T extends SupportedUserDtos>(userData: T): T {
    const result: SupportedUserDtos = { ...userData };

    if ('email' in result && result.email) {
      const cleanedEmail = UserSanitizer.cleanEmail(result.email);
      if (!cleanedEmail) {
        throw new InvalidEmailException();
      }
      result.email = cleanedEmail.toLowerCase().trim();
    }

    if ('ein' in result && result.ein) {
      result.ein = this.formatEin(result.ein);
    }

    if ('birthday' in result && !result.birthday) {
      result.birthday = null;
    }

    if ('phone' in result && result.phone) {
      result.phone = this.formatPhoneNumber(result.phone);
    }

    if ('name' in result && result.name) {
      result.name = result.name.trim();
    }

    return result as T;
  }

  static formatPhoneNumber(phone: string | number): string {
    const phoneString = String(phone);
    return phoneString.replace(/[(), .-]/g, '');
  }

  static formatEin(ein: string): string {
    return /^[0-9.]+/.test(ein) ? ein.split('.')[0] : ein;
  }

  static cleanObject<T>(data: Record<string, any>): T {
    return Object.fromEntries(Object.entries(data).filter(([_, value]) => value != null)) as T;
  }

  static cleanEmail(email?: string): string {
    if (!email) {
      throw new InvalidEmailException();
    }

    const cleanedEmail = email.replace(/[^a-zA-Z0-9@._+-]/g, '');
    const parts = cleanedEmail.split('@');

    if (parts.length !== 2 || !parts[0] || !parts[1]) {
      throw new InvalidEmailException();
    }

    return cleanedEmail;
  }
}
