import { Repository, SelectQueryBuilder } from 'typeorm';
import { EmployeeInfo } from '../../../entities/employee-info.entity';
import { EmployeeInfoListParamsDto } from '../../application/dtos/employee-info-list-params.dto';

export default abstract class EmployeeInfosRepository {
  abstract findAllowed(workspaceId: string, filters?: EmployeeInfoListParamsDto): SelectQueryBuilder<EmployeeInfo>;
  abstract findUniqueValues(
    workspaceId: string,
    column: keyof EmployeeInfo,
    search?: string,
  ): SelectQueryBuilder<EmployeeInfo>;
  repository: Repository<EmployeeInfo>;
}
