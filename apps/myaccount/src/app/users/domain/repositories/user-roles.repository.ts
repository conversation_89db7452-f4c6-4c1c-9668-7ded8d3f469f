import { Repository, SelectQueryBuilder } from 'typeorm';
import { UserRoleWorkspace } from '../../../entities/user-role-workspace.entity';
import { UserRoleListParamsDto } from '../../application/dtos/user-role-list-params.dto';

export default abstract class UserRolesRepository {
  abstract findAllowed(workspaceId: string, filters?: UserRoleListParamsDto): SelectQueryBuilder<UserRoleWorkspace>;
  abstract userHasRoleOnWorkspace(userId: string, workspaceId: string): Promise<boolean>;
  abstract deleteUserWorkspace(userId: string, workspaceId: string);
  abstract userHasRoleOnApplication(userId: string, workspaceId: string, applicationId: string): Promise<boolean>;
  abstract userHasAnyRole(userId: string, workspaceId: string, roleIds: string[]): Promise<boolean>;
  repository: Repository<UserRoleWorkspace>;
}
