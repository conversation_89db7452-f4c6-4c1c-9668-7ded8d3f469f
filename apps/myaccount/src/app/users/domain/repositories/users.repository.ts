import { Paginated, PaginateQuery } from 'nestjs-paginate';
import { User } from '../../../entities/user.entity';

export abstract class UsersRepository {
  abstract findAllowed(
    workspaceId: string,
    filterIntegrationUsers: boolean,
    query: PaginateQuery,
  ): Promise<Paginated<User>>;
  abstract findAllowedIgnoringPagination(
    workspaceId: string,
    filterIntegrationUsers: boolean,
    query: PaginateQuery,
  ): Promise<Paginated<User>>;
  abstract findInfo(userId: string, workspaceId: string): Promise<User>;
  abstract findByEmail(email: string): Promise<User>;
  abstract findById(id: string, workspaceId: string): Promise<User>;
  abstract findByIdAndNotIntegration(id: string): Promise<User>;
  abstract create(entityLikeArray: Partial<User>): User;
  abstract save(entityLikeArray: User): Promise<User>;
  abstract update(criteria: any, partialEntity: Partial<User>);
  abstract findLeaders(workspaceId: string): Promise<Pick<User, 'id' | 'name' | 'email'>[]>;
}
