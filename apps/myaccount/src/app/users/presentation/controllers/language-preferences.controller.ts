import { Controller, Get, Param } from '@nestjs/common';
import { LanguagePreferencesService } from '../../application/services/language-preferences.service';
import { LanguageDto } from '../../application/dtos/language.dto';
import { ApiOkResponse } from '@nestjs/swagger';
import { Serialize } from '@keeps-node-apis/@core';

@Controller('languages')
export class LanguagePreferencesController {
  constructor(private service: LanguagePreferencesService) {}

  @Get()
  @ApiOkResponse({ type: LanguageDto, isArray: true })
  @Serialize(LanguageDto)
  findAll(): Promise<LanguageDto[]> {
    return this.service.findAll();
  }

  @Get(':id')
  @ApiOkResponse({ type: LanguageDto })
  @Serialize(LanguageDto)
  retrieveOneById(@Param('id') id: string): Promise<LanguageDto> {
    return this.service.findOneById(id);
  }
}
