import {
  AuthUser,
  KONQUEST_ADMIN_ROLES,
  MYACCOUNT_ADMIN_ROLES,
  MYACCOUNT_ALL_ROLES,
  PageOptionsWithSearchDto,
  PaginatedResponseDto,
  Roles,
  Serialize,
  SerializePagination,
  X_CLIENT,
} from '@keeps-node-apis/@core';
import { Body, Controller, Get, Headers, Param, ParseUUIDPipe, Patch, Post, Query } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiResponse,
  ApiSecurity,
  ApiTags,
} from '@nestjs/swagger';
import { plainToInstance } from 'class-transformer';
import { AuthenticatedUser } from 'nest-keycloak-connect';
import { Paginate, PaginatedSwaggerDocs, PaginateQuery } from 'nestjs-paginate';
import { EmployeeInfoDto } from '../../application/dtos/employee-info.dto';
import { UserListResponseDto } from '../../application/dtos/user-list-reponse.dto';
import { UserResponseDto } from '../../application/dtos/user-reponse.dto';
import { EmployeeInfoService } from '../../application/services/employee-info.service';
import { UsersService } from '../../application/services/users.service';
import { USER_PAGINATION_CONFIG } from '../../users.query-config';
import { UpdateUsersService } from '../../application/services/update-users.service';
import { UpdateUserDto } from '../../application/dtos/update-user.dto';
import { ChangePasswordDto } from '../../application/dtos/change-password.dto';
import { EmployeeInfoListParamsDto } from '../../application/dtos/employee-info-list-params.dto';
import { UpdateUserStatusResponseDto } from '../../application/dtos/update-user-status-response.dto';
import { UpdateUserStatusDto } from '../../application/dtos/update-user-status.dto';
import { UserDetailResponseDto } from '../../application/dtos/user-detail-response.dto';
import { BasicUserResponseDto } from '../../application/dtos/basic-user-response.dto';

@ApiTags('Users')
@ApiBearerAuth()
@ApiSecurity('x-client')
@Controller('users')
export class UsersController {
  constructor(
    private readonly usersService: UsersService,
    private readonly employeeInfoService: EmployeeInfoService,
    private readonly updateUsersService: UpdateUsersService,
  ) {}

  @Roles([...KONQUEST_ADMIN_ROLES, ...MYACCOUNT_ADMIN_ROLES])
  @Get()
  @PaginatedSwaggerDocs(UserListResponseDto, USER_PAGINATION_CONFIG)
  @ApiResponse({ status: 401, description: 'Unauthorized - Invalid or missing authentication token' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions to access this resource' })
  async findAll(
    @AuthenticatedUser() user: AuthUser,
    @Headers('x-client') workspaceId: string,
    @Paginate() query: PaginateQuery,
  ) {
    const result = await this.usersService.findAllowed(user.sub, workspaceId, query);
    return plainToInstance(PaginatedResponseDto, {
      ...result,
      data: result.data.map((item) => plainToInstance(UserListResponseDto, item)),
    });
  }

  @Roles(MYACCOUNT_ALL_ROLES)
  @Get('info')
  @ApiOperation({
    summary: 'Get authenticated user information',
    description: 'Retrieves detailed information about the currently authenticated user',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved user information',
    type: UserResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized - Invalid or missing authentication token' })
  async findAllInfoViewProjection(@AuthenticatedUser() user: AuthUser, @Headers('x-client') workspaceId: string) {
    return this.usersService.findInfo(user.sub, workspaceId);
  }

  @Roles(MYACCOUNT_ADMIN_ROLES)
  @Post('set-password')
  @ApiOperation({ summary: 'Set a user password' })
  @ApiResponse({ status: 200 })
  async setPassword(@Body() changePasswordDto: ChangePasswordDto) {
    await this.usersService.setPassword(changePasswordDto);
  }

  @Roles(MYACCOUNT_ADMIN_ROLES)
  @Post('set-password/batch')
  @ApiOperation({ summary: 'Set multiple users passwords in batch' })
  @ApiResponse({
    status: 200,
    schema: {
      type: 'array',
      items: { $ref: '#/components/schemas/ChangePasswordDto' },
    },
  })
  async setPasswordBatch(@Body() changePasswordsDto: ChangePasswordDto[]) {
    await this.usersService.setPasswordBatch(changePasswordsDto);
  }

  @Roles(MYACCOUNT_ADMIN_ROLES)
  @Get('employee-infos')
  @SerializePagination(EmployeeInfoDto)
  @ApiOperation({ summary: 'List employee information with pagination' })
  @ApiHeader({ name: X_CLIENT, description: 'workspace Id' })
  @ApiResponse({
    status: 200,
    schema: {
      type: 'object',
      properties: {
        items: {
          type: 'array',
          items: { $ref: '#/components/schemas/EmployeeInfoDto' },
        },
        meta: {
          type: 'object',
          properties: {
            page: { type: 'number' },
            take: { type: 'number' },
            itemCount: { type: 'number' },
            pageCount: { type: 'number' },
            hasPreviousPage: { type: 'boolean' },
            hasNextPage: { type: 'boolean' },
          },
        },
      },
    },
  })
  findUserEmployeeInfos(@Query() listParamsDto: EmployeeInfoListParamsDto, @Headers(X_CLIENT) workspaceId: string) {
    return this.employeeInfoService.findAllowed(workspaceId, listParamsDto);
  }

  @Roles(MYACCOUNT_ADMIN_ROLES)
  @Get('employee-infos/managers')
  @SerializePagination(String)
  @ApiOperation({ summary: 'List all managers' })
  @ApiHeader({ name: X_CLIENT, description: 'workspace Id' })
  @ApiResponse({
    status: 200,
    schema: {
      type: 'object',
      properties: {
        items: {
          type: 'array',
          items: { type: 'string' },
        },
        meta: {
          type: 'object',
          properties: {
            page: { type: 'number' },
            take: { type: 'number' },
            itemCount: { type: 'number' },
            pageCount: { type: 'number' },
            hasPreviousPage: { type: 'boolean' },
            hasNextPage: { type: 'boolean' },
          },
        },
      },
    },
  })
  findManagers(@Query() listParamsDto: PageOptionsWithSearchDto, @Headers(X_CLIENT) workspaceId: string) {
    return this.employeeInfoService.findUniqueValues(workspaceId, listParamsDto, 'manager');
  }

  @Roles(MYACCOUNT_ADMIN_ROLES)
  @Get('employee-infos/directors')
  @SerializePagination(String)
  @ApiOperation({ summary: 'List all directors' })
  @ApiHeader({ name: X_CLIENT, description: 'workspace Id' })
  @ApiResponse({
    status: 200,
    schema: {
      type: 'object',
      properties: {
        items: {
          type: 'array',
          items: { type: 'string' },
        },
        meta: {
          type: 'object',
          properties: {
            page: { type: 'number' },
            take: { type: 'number' },
            itemCount: { type: 'number' },
            pageCount: { type: 'number' },
            hasPreviousPage: { type: 'boolean' },
            hasNextPage: { type: 'boolean' },
          },
        },
      },
    },
  })
  findDirectors(@Query() listParamsDto: PageOptionsWithSearchDto, @Headers(X_CLIENT) workspaceId: string) {
    return this.employeeInfoService.findUniqueValues(workspaceId, listParamsDto, 'director');
  }

  @Roles(MYACCOUNT_ADMIN_ROLES)
  @Get('employee-infos/areas-of-activity')
  @SerializePagination(String)
  @ApiOperation({ summary: 'List all areas of activity' })
  @ApiHeader({ name: X_CLIENT, description: 'workspace Id' })
  @ApiResponse({
    status: 200,
    schema: {
      type: 'object',
      properties: {
        items: {
          type: 'array',
          items: { type: 'string' },
        },
        meta: {
          type: 'object',
          properties: {
            page: { type: 'number' },
            take: { type: 'number' },
            itemCount: { type: 'number' },
            pageCount: { type: 'number' },
            hasPreviousPage: { type: 'boolean' },
            hasNextPage: { type: 'boolean' },
          },
        },
      },
    },
  })
  findAreasOfActivity(@Query() listParamsDto: PageOptionsWithSearchDto, @Headers(X_CLIENT) workspaceId: string) {
    return this.employeeInfoService.findUniqueValues(workspaceId, listParamsDto, 'areaOfActivity');
  }

  @Roles(MYACCOUNT_ALL_ROLES)
  @Patch(':id')
  @ApiOperation({
    summary: 'Update user',
    description: "Updates an existing user's information",
  })
  @ApiResponse({
    status: 200,
    description: 'User successfully updated',
    type: UserResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad Request - Invalid input data' })
  @ApiResponse({ status: 401, description: 'Unauthorized - Invalid or missing authentication token' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions to update user' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async updateUser(
    @AuthenticatedUser() authUser: AuthUser,
    @Param('id', ParseUUIDPipe) userId: string,
    @Body() updateUserDto: UpdateUserDto,
    @Headers(X_CLIENT) workspaceId: string,
  ) {
    return await this.updateUsersService.update(authUser, userId, updateUserDto, workspaceId);
  }

  @Patch(':id/update-status')
  @Serialize(UpdateUserStatusResponseDto)
  @Roles(MYACCOUNT_ADMIN_ROLES)
  @ApiOkResponse({ type: UpdateUserStatusResponseDto })
  @ApiOperation({ summary: 'Updates an user status' })
  async updateUserStatus(
    @Param('id') userId: string,
    @AuthenticatedUser() authUser: AuthUser,
    @Headers('x-client') workspaceId: string,
    @Body() updateUserStatusDto: UpdateUserStatusDto,
  ) {
    return this.updateUsersService.updateUserStatus(userId, authUser.sub, workspaceId, updateUserStatusDto);
  }

  @Roles(MYACCOUNT_ALL_ROLES)
  @Get('leaders')
  @Serialize(BasicUserResponseDto)
  @ApiOperation({
    summary: 'List leaders',
    description: 'Get the users that are leaders of other users in the workspace',
  })
  @ApiOkResponse({ type: BasicUserResponseDto, isArray: true })
  @ApiResponse({ status: 401, description: 'Unauthorized - Invalid or missing authentication token' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions' })
  async findLeaders(@Headers('x-client') workspaceId: string) {
    return this.usersService.findAllLeaders(workspaceId);
  }

  @Roles(MYACCOUNT_ALL_ROLES)
  @Get(':id')
  @Serialize(UserDetailResponseDto)
  @ApiOperation({
    summary: 'Get user details',
    description: 'Retrieves detailed information for a specific user by ID',
  })
  @ApiOkResponse({ type: UserDetailResponseDto })
  @ApiResponse({ status: 401, description: 'Unauthorized - Invalid or missing authentication token' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async findOneUser(
    @Param('id', ParseUUIDPipe) userId: string,
    @AuthenticatedUser() authUser: AuthUser,
    @Headers('x-client') workspaceId: string,
  ) {
    return this.usersService.findOneById(userId, authUser, workspaceId);
  }
}
