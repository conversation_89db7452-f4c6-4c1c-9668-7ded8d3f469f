import { Body, Controller, Delete, Get, Headers, Param, Post, Query } from '@nestjs/common';
import { AuthenticatedUser } from 'nest-keycloak-connect';
import { UserRolesService } from '../../application/services/user-roles.service';
import { UserRoleListDto } from '../../application/dtos/user-role-list.dto';
import { AuthUser, PageDto } from '@keeps-node-apis/@core';
import { UserRoleCreateDto } from '../../application/dtos/user-role-create.dto';
import { UserRoleListParamsDto } from '../../application/dtos/user-role-list-params.dto';

@Controller('users-roles')
export class UserRolesController {
  constructor(private userRolesService: UserRolesService) {}

  @Get()
  findAll(
    @AuthenticatedUser() user: AuthUser,
    @Headers('x-client') workspaceId: string,
    @Query() filtersDto: UserRoleListParamsDto,
  ): Promise<PageDto<UserRoleListDto>> {
    return this.userRolesService.findAllowed(user.sub, workspaceId, filtersDto);
  }

  @Delete(':id')
  delete(@AuthenticatedUser() user: AuthUser, @Param('id') id: string) {
    return this.userRolesService.deleteUserRole(id, user.sub, false);
  }

  @Post()
  create(@Body() userRoleCreateDto: UserRoleCreateDto) {
    return this.userRolesService.createUserRole(userRoleCreateDto);
  }
}
