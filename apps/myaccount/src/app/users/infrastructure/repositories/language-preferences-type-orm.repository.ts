import { Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import LanguagePreferencesRepository from '../../domain/repositories/language-preferences.repository';
import { LanguagePreference } from '../../../entities/language-preference.entity';

@Injectable()
export class LanguagePreferencesTypeORMRepository implements LanguagePreferencesRepository {
  constructor(@InjectRepository(LanguagePreference) readonly _repository: Repository<LanguagePreference>) {}
  get repository(): Repository<LanguagePreference> {
    return this._repository;
  }
}
