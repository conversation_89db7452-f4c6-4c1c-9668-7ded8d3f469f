import { Injectable } from '@nestjs/common';
import { paginate, PaginateConfig, Paginated, PaginateQuery } from 'nestjs-paginate';
import { Repository } from 'typeorm';
import { User } from '../../../entities/user.entity';
import { USER_PAGINATION_CONFIG } from '../../users.query-config';
import { UsersRepository } from '../../domain/repositories/users.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { UserRoleWorkspace } from '../../../entities/user-role-workspace.entity';

@Injectable()
export class UsersTypeOrmRepository implements UsersRepository {
  constructor(@InjectRepository(User) readonly repository: Repository<User>) {}

  private retrieveCommonWhereStatment(filterIntegrationUsers: boolean, workspaceId: string): Array<object> {
    filterIntegrationUsers = filterIntegrationUsers ?? false;
    return [
      {
        isUserIntegration: filterIntegrationUsers,
        roles: { workspaceId: workspaceId },
      },
    ];
  }

  async findAllowedIgnoringPagination(
    workspaceId: string,
    filterIntegrationUsers: boolean,
    query: PaginateQuery,
  ): Promise<Paginated<User>> {
    query.limit = -1;
    query.select = ['id', 'name', 'email', 'status'];

    const config: PaginateConfig<User> = {
      ...USER_PAGINATION_CONFIG,
      maxLimit: -1,
      where: this.retrieveCommonWhereStatment(filterIntegrationUsers, workspaceId),
    };

    return await paginate<User>(query, this.repository, config);
  }

  findByEmail(email: string): Promise<User> {
    const queryBuilder = this.repository
      .createQueryBuilder('user')
      .select()
      .leftJoinAndMapOne('user.language', 'user.language', 'language')
      .where('user.email = :email')
      .setParameters({ email });
    return queryBuilder.getOne();
  }

  findById(id: string, workspaceId: string): Promise<User> {
    const queryBuilder = this.repository
      .createQueryBuilder('user')
      .select()
      .leftJoinAndMapOne('user.language', 'user.language', 'language')
      .where('user.id = :id')
      .setParameters({ id, workspaceId });
    return queryBuilder.getOneOrFail();
  }

  async findAllowed(
    workspaceId: string,
    filterIntegrationUsers: boolean,
    query: PaginateQuery,
  ): Promise<Paginated<User>> {
    filterIntegrationUsers = filterIntegrationUsers ?? false;
    const config: PaginateConfig<User> = {
      ...USER_PAGINATION_CONFIG,
      where: [
        {
          isUserIntegration: filterIntegrationUsers,
          roles: { workspaceId: workspaceId },
        },
      ],
    };

    const result = await paginate<User>(query, this.repository, config);
    result.data.forEach((user) => {
      const profile = user?.employeeInfos?.filter((employeeInfo) => employeeInfo.workspaceId == workspaceId);
      user.employeeInfos = profile;
    });
    return result;
  }

  findInfo(userId: string, workspaceId: string): Promise<User> {
    const queryBuilder = this.repository
      .createQueryBuilder('user')
      .select()
      .leftJoinAndMapOne('user.language', 'user.language', 'language')
      .leftJoinAndMapMany('user.roles', 'user.roles', 'roles', 'roles.workspace_id = :workspaceId')
      .leftJoinAndMapOne('roles.role', 'roles.role', 'role')
      .leftJoinAndMapOne('role.application', 'role.application', 'application')

      .leftJoinAndMapOne(
        'user.employeeInfo',
        'user.employeeInfos',
        'employeeInfo',
        'employeeInfo.workspace_id = :workspaceId',
      )
      .leftJoinAndMapOne('employeeInfo.jobFunction', 'employeeInfo.jobFunction', 'jobFunction')
      .leftJoinAndMapOne('employeeInfo.jobPosition', 'employeeInfo.jobPosition', 'jobPosition')
      .leftJoinAndMapOne('employeeInfo.workspace', 'employeeInfo.workspace', 'workspace')

      .leftJoinAndMapOne('user.relatedUserLeader', 'user.relatedUserLeader', 'leader')

      .where('user.id = :userId')
      .setParameters({ userId, workspaceId });
    return queryBuilder.getOneOrFail();
  }

  async findByIdAndNotIntegration(id: string): Promise<User> {
    const queryBuilder = this.repository
      .createQueryBuilder('user')
      .select()
      .leftJoinAndMapOne('user.language', 'user.language', 'language')
      .where('user.id = :id', { id })
      .andWhere('user.is_user_integration = false')
      .setParameters({ id });
    return await queryBuilder.getOneOrFail();
  }

  create(entityLikeArray: Partial<User>): User {
    return this.repository.create(entityLikeArray);
  }

  save(entityLikeArray: User): Promise<User> {
    return this.repository.save(entityLikeArray);
  }

  update(criteria: any, partialEntity: Partial<User>) {
    this.repository.update(criteria, partialEntity);
  }

  findLeaders(workspaceId: string): Promise<Pick<User, 'id' | 'name' | 'email'>[]> {
    const queryBuilder = this.repository
      .createQueryBuilder('leader')
      .select(['leader.id', 'leader.name', 'leader.email'])
      .innerJoin(User, 'user', 'user.related_user_leader_id = leader.id')
      .innerJoin(UserRoleWorkspace, 'user_role_workspace', 'user_role_workspace.user_id = user.id')
      .where('user_role_workspace.workspace_id = :workspaceId', { workspaceId })
      .distinct(true);

    return queryBuilder.getMany();
  }
}
