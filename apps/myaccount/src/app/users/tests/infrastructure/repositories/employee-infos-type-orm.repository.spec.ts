import { SelectQueryBuilder } from 'typeorm';
import EmployeeInfosTypeORMRepository from '../../../infrastructure/repositories/employee-infos-type-orm.repository';
import { EmployeeInfoListParamsDto } from '../../../application/dtos/employee-info-list-params.dto';

describe('EmployeeInfosRepository', () => {
  let repository: EmployeeInfosTypeORMRepository;
  let mockQueryBuilder: jest.Mocked<SelectQueryBuilder<any>>;
  const mockDataSource = {
    createEntityManager: jest.fn(),
    getRepository: jest.fn(),
  };

  beforeEach(() => {
    mockQueryBuilder = {
      andWhere: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      setParameters: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      leftJoinAndMapOne: jest.fn().mockReturnThis(),
      getQuery: jest.fn(),
      getParameters: jest.fn(),
      getMany: jest.fn().mockResolvedValue([]),
    } as any;

    mockDataSource.getRepository.mockReturnValue({
      createQueryBuilder: jest.fn(() => mockQueryBuilder),
    });

    repository = new EmployeeInfosTypeORMRepository(mockDataSource as any);
    repository.repository.createQueryBuilder = jest.fn().mockReturnValue(mockQueryBuilder);
  });

  describe('createBaseQueryBuilder', () => {
    it('should create a base query builder', () => {
      repository.createBaseQueryBuilder();

      expect(mockQueryBuilder.leftJoinAndMapOne).toHaveBeenCalledWith(
        'user_profile_workspace.user',
        'user_profile_workspace.user',
        'user',
      );
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('user_profile_workspace.workspace_id = :workspaceId');
    });
  });

  describe('findAllowed', () => {
    it('should create a query to find allowed employees with filters', () => {
      const workspaceId = 'workspace-id';
      const filters = new EmployeeInfoListParamsDto();
      filters.userId = 'user-id';

      repository.findAllowed(workspaceId, filters);

      expect(mockQueryBuilder.setParameters).toHaveBeenCalledWith({ workspaceId });
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith('user_profile_workspace.createdDate', 'DESC');
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('user_profile_workspace.user_id = :userId', {
        userId: filters.userId,
      });
    });

    it('should create a query to find allowed employees without filters', () => {
      const workspaceId = 'workspace-id';

      repository.findAllowed(workspaceId);

      expect(mockQueryBuilder.setParameters).toHaveBeenCalledWith({ workspaceId });
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith('user_profile_workspace.createdDate', 'DESC');
    });
  });

  describe('findUniqueValues', () => {
    it('should create a query to find unique values for a column', () => {
      const workspaceId = 'workspace-id';
      const column = 'areaOfActivity';

      repository.findUniqueValues(workspaceId, column);

      expect(mockQueryBuilder.select).toHaveBeenCalledWith('DISTINCT(user_profile_workspace.area_of_activity)', column);
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('user_profile_workspace.workspace_id = :workspaceId', {
        workspaceId,
      });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('user_profile_workspace.area_of_activity is not null');
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith("user_profile_workspace.area_of_activity != ''");
    });

    it('should create a query to find unique values for a column with search', () => {
      const workspaceId = 'workspace-id';
      const column = 'areaOfActivity';
      const search = 'finance';

      repository.findUniqueValues(workspaceId, column, search);

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'UNACCENT(LOWER(user_profile_workspace.area_of_activity)) LIKE UNACCENT(LOWER(:search))',
        { search: '%finance%' },
      );
    });
  });
});
