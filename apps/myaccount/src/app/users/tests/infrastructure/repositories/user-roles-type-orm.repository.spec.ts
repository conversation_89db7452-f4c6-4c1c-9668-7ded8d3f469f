import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { UserRolesTypeORMRepository } from '../../../infrastructure/repositories/user-roles-type-orm.repository';
import { UserRoleWorkspace } from '../../../../entities/user-role-workspace.entity';
import { UserRoleListParamsDto } from '../../../application/dtos/user-role-list-params.dto';

describe('UserRolesTypeORMRepository', () => {
  let repository: UserRolesTypeORMRepository;
  let ormRepo: jest.Mocked<Repository<UserRoleWorkspace>>;
  let queryBuilder: jest.Mocked<SelectQueryBuilder<UserRoleWorkspace>>;

  beforeEach(async () => {
    queryBuilder = {
      select: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      leftJoinAndMapOne: jest.fn().mockReturnThis(),
      getExists: jest.fn(),
    } as any;

    ormRepo = {
      createQueryBuilder: jest.fn().mockReturnValue(queryBuilder),
      delete: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserRolesTypeORMRepository,
        {
          provide: getRepositoryToken(UserRoleWorkspace),
          useValue: ormRepo,
        },
      ],
    }).compile();

    repository = module.get<UserRolesTypeORMRepository>(UserRolesTypeORMRepository);
  });

  describe('findAllowed', () => {
    it('should return query with basic where', () => {
      const filters = undefined;
      const workspaceId = 'workspace-1';

      const result = repository.findAllowed(workspaceId, filters);

      expect(ormRepo.createQueryBuilder).toHaveBeenCalled();
      expect(queryBuilder.where).toHaveBeenCalledWith('workspace_id = :workspaceId', { workspaceId });
      expect(result).toBe(queryBuilder);
    });

    it('should add roleId and userStatus filters if provided', () => {
      const workspaceId = 'workspace-1';
      const filters = {
        roleId: 'role-123',
        userStatus: true,
      } as UserRoleListParamsDto;

      const result = repository.findAllowed(workspaceId, filters);

      expect(queryBuilder.andWhere).toHaveBeenCalledWith('role_id = :roleId', { roleId: filters.roleId });
      expect(queryBuilder.andWhere).toHaveBeenCalledWith('user.status = :userStatus', {
        userStatus: filters.userStatus,
      });
      expect(result).toBe(queryBuilder);
    });
  });

  describe('userHasRoleOnWorkspace', () => {
    it('should return true if user has a role on the workspace', async () => {
      queryBuilder.getExists.mockResolvedValue(true);

      const result = await repository.userHasRoleOnWorkspace('user-1', 'workspace-1');

      expect(ormRepo.createQueryBuilder).toHaveBeenCalled();
      expect(queryBuilder.where).toHaveBeenCalledWith('user_id = :userId', { userId: 'user-1' });
      expect(queryBuilder.andWhere).toHaveBeenCalledWith('workspace_id = :workspaceId', { workspaceId: 'workspace-1' });
      expect(result).toBe(true);
    });
  });

  describe('deleteUserWorkspace', () => {
    it('should call delete with userId and workspaceId', async () => {
      ormRepo.delete.mockResolvedValue({ affected: 1, raw: [] });

      await repository.deleteUserWorkspace('user-1', 'workspace-1');

      expect(ormRepo.delete).toHaveBeenCalledWith({ userId: 'user-1', workspaceId: 'workspace-1' });
    });
  });

  describe('userHasRoleOnApplication', () => {
    it('should return true if user has a role for the application', async () => {
      queryBuilder.getExists.mockResolvedValue(true);

      const result = await repository.userHasRoleOnApplication('user-1', 'workspace-1', 'app-1');

      expect(queryBuilder.where).toHaveBeenCalledWith('user_id = :userId', { userId: 'user-1' });
      expect(queryBuilder.andWhere).toHaveBeenCalledWith('workspace_id = :workspaceId', { workspaceId: 'workspace-1' });
      expect(queryBuilder.andWhere).toHaveBeenCalledWith('role.application_id = :applicationId', {
        applicationId: 'app-1',
      });
      expect(result).toBe(true);
    });
  });

  describe('userHasAnyRole', () => {
    it('should return true if user has any of the specified roles', async () => {
      queryBuilder.getExists.mockResolvedValue(true);
      const result = await repository.userHasAnyRole('user-1', 'workspace-1', ['role-1', 'role-2']);

      expect(queryBuilder.where).toHaveBeenCalledWith('user_id = :userId', { userId: 'user-1' });
      expect(queryBuilder.andWhere).toHaveBeenCalledWith('workspace_id = :workspaceId', { workspaceId: 'workspace-1' });
      expect(queryBuilder.andWhere).toHaveBeenCalledWith('role_id IN (:...roleIds)', { roleIds: ['role-1', 'role-2'] });
      expect(result).toBe(true);
    });
  });
});
