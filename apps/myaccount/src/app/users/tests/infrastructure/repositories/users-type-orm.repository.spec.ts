/* eslint-disable @typescript-eslint/no-explicit-any */
import * as nestjsPaginate from 'nestjs-paginate';
import { PaginateQuery } from 'nestjs-paginate';
import { SelectQueryBuilder } from 'typeorm';
import { UsersTypeOrmRepository } from '../../../infrastructure/repositories/users-type-orm.repository';

// Mock do nestjs-paginate
jest.mock('nestjs-paginate', () => ({
  paginate: jest.fn(),
  FilterOperator: {
    ILIKE: 'ILIKE',
    EQ: 'EQ',
    NOT: 'NOT',
  },
  FilterSuffix: {
    NOT: 'NOT',
  },
}));

describe('UsersRepository', () => {
  let usersRepository: UsersTypeOrmRepository;
  let mockQueryBuilder: jest.Mocked<Partial<SelectQueryBuilder<any>>>;
  let mockDataSource: jest.Mocked<Partial<any>>;

  const createMockQueryBuilder = () =>
    ({
      select: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      leftJoinAndMapOne: jest.fn().mockReturnThis(),
      leftJoinAndMapMany: jest.fn().mockReturnThis(),
      innerJoinAndMapMany: jest.fn().mockReturnThis(),
      setParameters: jest.fn().mockReturnThis(),
      getOneOrFail: jest.fn(),
      getOne: jest.fn(),
      getMany: jest.fn(),
      getQuery: jest.fn(),
      getParameters: jest.fn(),
      skip: jest.fn().mockReturnThis(),
      take: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      getCount: jest.fn().mockResolvedValue(0),
      getManyAndCount: jest.fn().mockResolvedValue([[], 0]),
      getRawAndEntities: jest.fn().mockResolvedValue([[], []]),
      join: jest.fn().mockReturnThis(),
      leftJoinAndSelect: jest.fn().mockReturnThis(),
    }) as any;

  beforeEach(() => {
    mockQueryBuilder = createMockQueryBuilder();

    mockDataSource = {
      createEntityManager: jest.fn(),
      getRepository: jest.fn(),
      getConnection: jest.fn(),
      getRepositoryToken: jest.fn(),
    };

    mockDataSource.getRepository.mockReturnValue({
      createQueryBuilder: jest.fn(() => mockQueryBuilder),
    });

    usersRepository = new UsersTypeOrmRepository(mockDataSource as any);
    usersRepository.repository.createQueryBuilder = jest.fn().mockReturnValue(mockQueryBuilder);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('findByEmail', () => {
    it('should query for a user by email', async () => {
      const email = '<EMAIL>';

      await usersRepository.findByEmail(email);

      expect(mockQueryBuilder.select).toHaveBeenCalled();
      expect(mockQueryBuilder.leftJoinAndMapOne).toHaveBeenCalled();
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('user.email = :email');
      expect(mockQueryBuilder.setParameters).toHaveBeenCalledWith({ email });
    });
  });

  describe('findById', () => {
    it('should query for a user by id', async () => {
      const id = '<EMAIL>';
      const workspaceId = 'workspace-id';

      await usersRepository.findById(id, workspaceId);

      expect(mockQueryBuilder.select).toHaveBeenCalled();
      expect(mockQueryBuilder.leftJoinAndMapOne).toHaveBeenCalled();
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('user.id = :id');
      expect(mockQueryBuilder.setParameters).toHaveBeenCalledWith({ id, workspaceId });
    });
  });

  describe('findAllowed', () => {
    const mockQuery: PaginateQuery = {
      page: 1,
      limit: 10,
      path: '/users',
    };

    const mockUsers = [
      {
        id: 'user1',
        name: 'User 1',
        employeeInfos: [
          { workspaceId: 'workspace-id', areaOfActivity: 'Area 1' },
          { workspaceId: 'other-workspace', areaOfActivity: 'Area 2' },
        ],
      },
    ];

    const mockResult = {
      data: mockUsers,
      meta: {
        totalItems: 1,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1,
        sortBy: [['name', 'ASC']],
        searchBy: [],
        search: '',
        select: [],
      },
      links: {
        current: '/users?page=1&limit=10',
        first: '/users?page=1&limit=10',
        previous: null,
        next: null,
        last: '/users?page=1&limit=10',
      },
    };

    it('should return paginated users filtered by workspace and integration status', async () => {
      const workspaceId = 'workspace-id';
      (nestjsPaginate.paginate as jest.Mock).mockResolvedValueOnce(mockResult);

      const result = await usersRepository.findAllowed(workspaceId, false, mockQuery);

      expect(result).toEqual({
        ...mockResult,
        data: [
          {
            id: 'user1',
            name: 'User 1',
            employeeInfos: [{ workspaceId: 'workspace-id', areaOfActivity: 'Area 1' }],
          },
        ],
      });
      expect(nestjsPaginate.paginate).toHaveBeenCalledWith(mockQuery, expect.any(Object), expect.any(Object));
    });
  });

  describe('findInfo', () => {
    it('should query for user information based on userId and workspaceId', async () => {
      const userId = 'user-id';
      const workspaceId = 'workspace-id';

      await usersRepository.findInfo(userId, workspaceId);

      expect(mockQueryBuilder.select).toHaveBeenCalledWith();
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('user.id = :userId');
      expect(mockQueryBuilder.setParameters).toHaveBeenCalledWith({ userId, workspaceId });
    });
  });

  describe('findByIdAndNotIntegration', () => {
    it('should query for a user by id and ensure is_user_integration is false', async () => {
      const id = 'user-id';
      await usersRepository.findByIdAndNotIntegration(id);

      expect(mockQueryBuilder.select).toHaveBeenCalled();
      expect(mockQueryBuilder.leftJoinAndMapOne).toHaveBeenCalled();
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('user.id = :id', { id });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('user.is_user_integration = false');
      expect(mockQueryBuilder.setParameters).toHaveBeenCalledWith({ id });
      expect(mockQueryBuilder.getOneOrFail).toHaveBeenCalled();
    });

    it('should throw an error if no user is found', async () => {
      const id = 'non-existent-id';

      mockQueryBuilder.getOneOrFail.mockRejectedValueOnce(new Error('Not Found'));

      await expect(usersRepository.findByIdAndNotIntegration(id)).rejects.toThrow('Not Found');

      expect(mockQueryBuilder.select).toHaveBeenCalled();
      expect(mockQueryBuilder.leftJoinAndMapOne).toHaveBeenCalled();
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('user.id = :id', { id });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('user.is_user_integration = false');
      expect(mockQueryBuilder.setParameters).toHaveBeenCalledWith({ id });
    });
  });
});
