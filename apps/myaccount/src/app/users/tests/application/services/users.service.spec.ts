import { KeycloakRepository, KpCacheService, MYACCOUNT_ROLES, NotAllowedException } from '@keeps-node-apis/@core';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { plainToInstance } from 'class-transformer';
import { Paginated, PaginateQuery } from 'nestjs-paginate';
import { SelectQueryBuilder } from 'typeorm';
import { UserRolesService } from '../../../application/services/user-roles.service';
import { UsersService } from '../../../application/services/users.service';
import { UsersRepository } from '../../../domain/repositories/users.repository';
import { WorkspacesRepository } from '../../../../workspaces/repositories/workspaces-repository';
import { User } from '../../../../entities/user.entity';
import { Workspace } from '../../../../entities/workspace.entity';
import { UserResponseDto, UserSimpleResponseDto } from '../../../application/dtos/user-reponse.dto';

describe('UsersService - Full Coverage', () => {
  let usersService: UsersService;
  let mockUserRepository: jest.Mocked<Partial<UsersRepository>>;
  let mockWorkspacesRepository: jest.Mocked<Partial<WorkspacesRepository>>;
  let mockUserRolesService: jest.Mocked<Partial<UserRolesService>>;
  let mockSelectQueryBuilder: jest.Mocked<Partial<SelectQueryBuilder<User>>>;
  let mockCache: jest.Mocked<Partial<KpCacheService>>;
  let mockKeycloakRepository: jest.Mocked<Partial<KeycloakRepository>>;

  afterEach(() => {
    jest.resetAllMocks();
  });

  beforeEach(async () => {
    mockUserRepository = {
      findAllowed: jest.fn(),
      findInfo: jest.fn(),
      findByEmail: jest.fn(),
      findById: jest.fn(),
    } as any;
    mockWorkspacesRepository = {
      findAllUserWorkspaces: jest.fn(),
    } as any;
    mockSelectQueryBuilder = {
      orderBy: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      take: jest.fn().mockReturnThis(),
      getCount: jest.fn().mockReturnValue(10),
      getRawAndEntities: jest.fn(),
    };
    mockUserRolesService = {
      verifyUserPermission: jest.fn(),
    } as any;
    mockKeycloakRepository = {
      resetUserPassword: jest.fn(),
    };

    mockCache = {
      get: jest.fn(),
      set: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        { provide: UsersRepository, useValue: mockUserRepository },
        { provide: getRepositoryToken(Workspace), useValue: mockWorkspacesRepository },
        { provide: UserRolesService, useValue: mockUserRolesService },
        { provide: KpCacheService, useValue: mockCache },
        { provide: KeycloakRepository, useValue: mockKeycloakRepository },
      ],
    }).compile();

    usersService = module.get<UsersService>(UsersService);
  });

  describe('findAllowed', () => {
    it('should retrieve and return users after verifying permissions', async () => {
      const userId = 'user-id';
      const workspaceId = 'workspace-id';
      const mockPageOptions: PaginateQuery = {
        page: 0,
        limit: 10,
        path: '/users',
      };
      const mockUsers: User[] = [{ id: 'user1', name: 'Alice' } as User, { id: 'user2', name: 'Bob' } as User];
      mockSelectQueryBuilder.getRawAndEntities.mockResolvedValueOnce({
        entities: mockUsers,
        raw: {} as any,
      });

      const mockWorkspaceUsers: Paginated<User> = {
        data: mockUsers,
        meta: {
          totalItems: 10,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
          sortBy: [['name', 'ASC']],
          searchBy: [],
          search: '',
          select: [],
        },
        links: {
          current: '/users?page=0&limit=10',
          first: '/users?page=0&limit=10',
          previous: null,
          next: null,
          last: '/users?page=0&limit=10',
        },
      };

      mockUserRepository.findAllowed.mockResolvedValueOnce(mockWorkspaceUsers);
      mockUserRolesService.verifyUserPermission.mockResolvedValueOnce();

      const result = await usersService.findAllowed(userId, workspaceId, mockPageOptions);
      expect(result).toEqual(mockWorkspaceUsers);
      expect(mockUserRolesService.verifyUserPermission).toHaveBeenCalledWith(userId, workspaceId);
    });
  });

  describe('findInfo', () => {
    it('should retrieve and return a user', async () => {
      const userId = 'user-id';
      const workspaceId = 'workspace-id';
      const mockUser: User = { id: 'user1', name: 'Alice' } as any;

      mockUserRepository.findInfo.mockResolvedValueOnce(mockUser);

      const result = await usersService.findInfo(userId, workspaceId);

      expect(result).toEqual(plainToInstance(UserResponseDto, mockUser));
      expect(mockUserRepository.findInfo).toHaveBeenCalledWith(userId, workspaceId);
    });
  });

  describe('setPassword', () => {
    it('should call KeycloakRepository to set password for a single user', async () => {
      const userId = 'user-id';
      const password = 'new-password';
      mockKeycloakRepository.resetUserPassword.mockResolvedValueOnce(0);

      await usersService.setPassword({ userId, password, temporary: false });

      expect(mockKeycloakRepository.resetUserPassword).toHaveBeenCalledWith(userId, password, false);
    });

    it('should call KeycloakRepository to set passwords for multiple users', async () => {
      const users = [
        { userId: 'user1', password: 'password1', temporary: false },
        { userId: 'user2', password: 'password2', temporary: false },
      ];
      mockKeycloakRepository.resetUserPassword.mockResolvedValueOnce(0);

      await usersService.setPasswordBatch(users);

      expect(mockKeycloakRepository.resetUserPassword).toHaveBeenCalledTimes(2);
      expect(mockKeycloakRepository.resetUserPassword).toHaveBeenCalledWith('user1', 'password1', false);
      expect(mockKeycloakRepository.resetUserPassword).toHaveBeenCalledWith('user2', 'password2', false);
    });
  });

  describe('getUserRoles', () => {
    it('should return the roles of the user', async () => {
      const mockUser = {
        id: 'user1',
        name: 'Alice',
        roles: [
          {
            applicationId: 'mock-app-id',
            key: 'mock-role-key',
          },
          {
            applicationId: 'mock-another-app-id',
            key: 'mock-role-key-2',
          },
        ],
      } as unknown as UserSimpleResponseDto;

      jest.spyOn(usersService, 'findInfo').mockResolvedValueOnce(mockUser);

      const roles = await usersService.getUserRoles('mock-user-id', 'mock-workspace-id');

      expect(roles).toEqual(mockUser.roles.map((role) => role.key));
    });

    it('should return an empty array if the user is not found', async () => {
      jest.spyOn(usersService, 'findInfo').mockResolvedValueOnce(undefined);

      const roles = await usersService.getUserRoles('mock-user-id', 'mock-workspace-id');

      expect(roles).toEqual([]);
    });
  });

  describe('findOneById', () => {
    const workspaceId = 'workspace-id';

    it('should return user info when user is allowed to see', async () => {
      const userId = 'target-user-id';
      const authUser = { sub: 'admin-id', roles: [MYACCOUNT_ROLES.COMPANY_ADMIN] } as any;
      const mockUser = { id: 'user1', name: 'Alice' } as any;

      mockUserRepository.findInfo.mockResolvedValueOnce(mockUser);

      const result = await usersService.findOneById(userId, authUser, workspaceId);

      expect(result).toEqual(mockUser);
      expect(mockUserRepository.findInfo).toHaveBeenCalledWith(userId, workspaceId);
    });

    it('should throw NotAllowedException when user cannot see requested user', async () => {
      const userId = 'target-user-id';
      const authUser = { sub: 'other-user-id', roles: [] } as any;

      await expect(usersService.findOneById(userId, authUser, workspaceId)).rejects.toThrow(NotAllowedException);
    });
  });

  describe('findInfo', () => {
    const userId = 'user-id';
    const workspaceId = 'workspace-id';
    const mockCacheKey = ['userinfo', userId, workspaceId];

    it('should return cached user if cache hit', async () => {
      const cachedUser = { id: 'user1', name: 'Cached User', roles: [] };

      mockCache.get.mockResolvedValueOnce(cachedUser);

      const result = await usersService.findInfo(userId, workspaceId);

      expect(mockCache.get).toHaveBeenCalledWith(mockCacheKey);
      expect(mockUserRepository.findInfo).not.toHaveBeenCalled();
      expect(result).toEqual(plainToInstance(UserSimpleResponseDto, cachedUser));
    });

    it('should retrieve user, map roles and set cache if cache miss', async () => {
      const mockUser = {
        id: 'user1',
        name: 'User From DB',
        language: { name: 'English' },
        roles: [
          {
            id: 1,
            role: {
              name: 'Admin',
              key: 'admin_key',
              applicationId: 'app-id',
              application: { name: 'Test App' },
            },
          },
        ],
      } as unknown as User;

      mockCache.get.mockResolvedValueOnce(undefined);
      mockUserRepository.findInfo.mockResolvedValueOnce(mockUser);

      const result = await usersService.findInfo(userId, workspaceId);

      expect(mockCache.get).toHaveBeenCalledWith(mockCacheKey);
      expect(mockUserRepository.findInfo).toHaveBeenCalledWith(userId, workspaceId);
      expect(mockCache.set).toHaveBeenCalledWith(mockCacheKey, expect.any(Object));
      expect(result.languageName).toBe('English');
      expect(result.roles[0].roleName).toBe('Admin');
      expect(result.roles[0].key).toBe('admin_key');
      expect(result.roles[0].applicationId).toBe('app-id');
      expect(result.roles[0].applicationName).toBe('Test App');
    });
  });
});
