import { Test, TestingModule } from '@nestjs/testing';
import { UserKeycloakService } from '../../../application/services/user-keycloak.service';
import { KeycloakRepository } from '@keeps-node-apis/@core';

describe('UserKeycloakService', () => {
  let service: UserKeycloakService;
  let keycloakRepository: Partial<jest.Mocked<KeycloakRepository>>;

  beforeEach(async () => {
    keycloakRepository = {
      updateUser: jest.fn(),
      getUserByEmail: jest.fn(),
      createUser: jest.fn(),
    };
    const module: TestingModule = await Test.createTestingModule({
      providers: [UserKeycloakService, { provide: KeycloakRepository, useValue: keycloakRepository }],
    }).compile();

    service = module.get<UserKeycloakService>(UserKeycloakService);

    jest.clearAllMocks();
  });

  describe('syncUserProfile', () => {
    it('should call updateUser with correct parameters', async () => {
      const user = { id: '123', name: '<PERSON>e', email: '<EMAIL>', languageId: '2' };

      await service.syncUserProfile(user);

      expect(keycloakRepository.updateUser).toHaveBeenCalledWith(
        '123',
        expect.objectContaining({ username: '<EMAIL>' }),
      );
    });

    it('should throw error if updateUser fails', async () => {
      const user = { id: '123', name: 'John Doe', email: '<EMAIL>' };
      keycloakRepository.updateUser.mockRejectedValue(new Error('Update failed'));

      await expect(service.syncUserProfile(user)).rejects.toThrow('Update failed');
    });
  });

  describe('createUser', () => {
    it('should return existing user ID if user already exists', async () => {
      const existingUser = { id: 'existing-id', email: '<EMAIL>' };
      keycloakRepository.getUserByEmail.mockResolvedValue(existingUser);
      jest.spyOn(service, 'syncUserProfile').mockImplementation(async () => {});

      const result = await service.createUser({ email: '<EMAIL>', name: 'John Doe' });

      expect(result).toBe('existing-id');
      expect(service.syncUserProfile).toHaveBeenCalled();
    });

    it('should create a new user if not found', async () => {
      // First call returns null (user not found)
      // Second call returns the newly created user
      keycloakRepository.getUserByEmail
        .mockResolvedValueOnce(null)
        .mockResolvedValueOnce({ id: 'new-id', email: '<EMAIL>' });

      const createdUser = { id: 'new-id' };
      keycloakRepository.createUser.mockResolvedValue(createdUser);
      jest.spyOn(service, 'syncUserProfile').mockImplementation(async () => {});

      const result = await service.createUser({ email: '<EMAIL>', name: 'John Doe' });

      expect(result).toBe('new-id');
      expect(keycloakRepository.createUser).toHaveBeenCalled();
      expect(service.syncUserProfile).not.toHaveBeenCalled();
    });

    it('should throw error if createUser fails', async () => {
      keycloakRepository.getUserByEmail.mockResolvedValue(null);
      keycloakRepository.createUser.mockRejectedValue(new Error('Create failed'));
      jest.spyOn(service, 'syncUserProfile').mockImplementation(async () => {});

      await expect(service.createUser({ email: '<EMAIL>', name: 'John Doe' })).rejects.toThrow(
        'Create failed',
      );
    });
  });
});
