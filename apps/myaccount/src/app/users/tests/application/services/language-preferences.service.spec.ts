/* eslint-disable @typescript-eslint/no-explicit-any */
import { Test, TestingModule } from '@nestjs/testing';
import { LanguagePreferencesService } from '../../../application/services/language-preferences.service';
import { Repository } from 'typeorm';
import LanguagePreferencesRepository from '../../../domain/repositories/language-preferences.repository';
import { LanguagePreference } from '../../../../entities/language-preference.entity';
import { LanguageDto } from '../../../application/dtos/language.dto';

describe('LanguagePreferencesService', () => {
  let service: LanguagePreferencesService;
  let mockLanguagePreferencesRepository: jest.Mocked<Partial<LanguagePreferencesRepository>>;

  beforeEach(async () => {
    mockLanguagePreferencesRepository = {
      repository: {
        findBy: jest.fn(),
        findOneByOrFail: jest.fn(),
      } as unknown as jest.Mocked<Repository<LanguagePreference>>,
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LanguagePreferencesService,
        {
          provide: LanguagePreferencesRepository,
          useValue: mockLanguagePreferencesRepository,
        },
      ],
    }).compile();

    service = module.get<LanguagePreferencesService>(LanguagePreferencesService);
  });

  describe('findAll', () => {
    it('should retrieve and return all language preferences', async () => {
      const mockLanguages: LanguagePreference[] = [
        { id: '1', name: 'English' } as any,
        { id: '2', name: 'Spanish' } as any,
      ];

      const expectedLanguages: LanguageDto[] = mockLanguages;
      (mockLanguagePreferencesRepository.repository.findBy as jest.Mock).mockResolvedValueOnce(mockLanguages);

      const result = await service.findAll();

      expect(result).toEqual(expectedLanguages);
      expect(mockLanguagePreferencesRepository.repository.findBy).toHaveBeenCalledWith({ status: true });
    });
  });

  describe('findOneById', () => {
    it('should retrieve and return one', async () => {
      const languageId = '1';
      const mockLanguage: LanguagePreference = { id: '1', name: 'English' } as any;

      const expectedLanguage: LanguageDto = mockLanguage;
      (mockLanguagePreferencesRepository.repository.findOneByOrFail as jest.Mock).mockResolvedValueOnce(mockLanguage);

      const result = await service.findOneById(languageId);

      expect(result).toEqual(expectedLanguage);
      expect(mockLanguagePreferencesRepository.repository.findOneByOrFail as jest.Mock).toHaveBeenCalledWith({
        id: languageId,
        status: true,
      });
    });
  });
});
