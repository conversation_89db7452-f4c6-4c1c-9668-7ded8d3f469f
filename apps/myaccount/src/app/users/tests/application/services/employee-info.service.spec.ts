import { Test, TestingModule } from '@nestjs/testing';
import { EmployeeInfoService } from '../../../application/services/employee-info.service';
import { PageDto, PageOptionsDto } from '@keeps-node-apis/@core';
import { Chance } from 'chance';
import { Repository, SelectQueryBuilder } from 'typeorm';
import EmployeeInfosRepository from '../../../domain/repositories/employee-infos.repository';
import { EmployeeInfo } from '../../../../entities/employee-info.entity';
import { EmployeeInfoDto } from '../../../application/dtos/employee-info.dto';
import { EmployeeInfoCreateDto } from '../../../application/dtos/employee-info-create.dto';

describe('EmployeeInfoService', () => {
  const change = Chance();
  let service: EmployeeInfoService;
  let repository: jest.Mocked<EmployeeInfosRepository>;
  let mockSelectQueryBuilder: jest.Mocked<Partial<SelectQueryBuilder<EmployeeInfo>>>;
  let existingEmployeeInfo: EmployeeInfo;
  let listParams: PageOptionsDto;

  beforeEach(async () => {
    listParams = {
      page: 1,
      perPage: 10,
      skip: 0,
    };
    repository = {
      findAllowed: jest.fn(),
      findUniqueValues: jest.fn(),
      repository: {
        findOneByOrFail: jest.fn(),
        save: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        findOneBy: jest.fn(),
      } as unknown as jest.Mocked<Repository<EmployeeInfo>>,
    } as unknown as jest.Mocked<EmployeeInfosRepository>;
    existingEmployeeInfo = {
      manager: change.string(),
      id: change.guid(),
      director: change.string(),
      areaOfActivity: change.string(),
      createdDate: change.date(),
      updatedDate: change.date(),
      userId: change.guid(),
      workspaceId: change.guid(),
      jobPositionId: change.guid(),
      jobFunctionId: change.guid(),
      jobFunction: null,
      jobPosition: null,
      user: null,
      workspace: null,
    } as EmployeeInfo;
    mockSelectQueryBuilder = {
      orderBy: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      take: jest.fn().mockReturnThis(),
      getCount: jest.fn().mockReturnValue(10),
      getRawAndEntities: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [EmployeeInfoService, { provide: EmployeeInfosRepository, useValue: repository }],
    }).compile();

    service = module.get<EmployeeInfoService>(EmployeeInfoService);
  });

  describe('findAllowed', () => {
    it('should return paginated employee info', async () => {
      const workspaceId = change.guid();
      const listParams: PageOptionsDto = {
        page: 1,
        perPage: 10,
        skip: 0,
      };
      const mockPaginatedResult: PageDto<EmployeeInfoDto> = {
        items: [existingEmployeeInfo],
        total: 10,
        hasNextPage: false,
        hasPreviousPage: false,
        page: 1,
      };

      mockSelectQueryBuilder.getRawAndEntities.mockResolvedValueOnce({
        entities: [existingEmployeeInfo],
        raw: {} as any,
      });
      repository.findAllowed.mockReturnValueOnce(mockSelectQueryBuilder as any);

      repository.findAllowed.mockReturnValueOnce({
        getMany: jest.fn().mockResolvedValue([]),
      } as any);

      const result = await service.findAllowed(workspaceId, listParams);

      expect(repository.findAllowed).toHaveBeenCalledWith(workspaceId, listParams);
      expect(result).toEqual(mockPaginatedResult);
    });
  });

  it('findUniqueValues - should return paginated specfic employee-info value', async () => {
    const workspaceId = change.guid();
    const directors = [change.string(), change.string()];
    const mockPaginatedResult: PageDto<string> = {
      items: directors,
      total: 10,
      hasNextPage: false,
      hasPreviousPage: false,
      page: 1,
    };

    mockSelectQueryBuilder.getRawAndEntities.mockResolvedValueOnce({
      entities: [],
      raw: [{ director: directors[0] }, { director: directors[1] }],
    });
    repository.findUniqueValues.mockReturnValueOnce(mockSelectQueryBuilder as any);

    const result = await service.findUniqueValues(workspaceId, listParams, 'director');

    expect(repository.findUniqueValues).toHaveBeenCalledWith(workspaceId, 'director', undefined);
    expect(result).toEqual(mockPaginatedResult);
  });

  describe('update', () => {
    it('should update and save employee info', async () => {
      const userId = change.guid();
      const workspaceId = change.guid();
      const updateDto: EmployeeInfoCreateDto = {
        manager: change.string(),
      };

      (repository.repository.findOneBy as jest.Mock).mockResolvedValue(existingEmployeeInfo);
      (repository.repository.save as jest.Mock).mockResolvedValue({
        ...existingEmployeeInfo,
        ...updateDto,
      } as EmployeeInfo);

      const result = await service.upsert(userId, workspaceId, updateDto);

      expect(repository.repository.findOneBy).toHaveBeenCalledWith({ userId, workspaceId });
      updateDto.manager = result.manager;
      expect(result).toEqual({ ...existingEmployeeInfo, ...updateDto });
    });
  });

  describe('create', () => {
    it('should create and save employee info', async () => {
      const createDto: EmployeeInfoCreateDto = {
        id: change.guid(),
        jobFunctionId: change.guid(),
        jobPositionId: change.guid(),
        director: change.string(),
        manager: change.string(),
        areaOfActivity: change.string(),
        userId: change.guid(),
        workspaceId: change.guid(),
      } as EmployeeInfoCreateDto;
      const newEmployeeInfo: EmployeeInfo = {
        id: createDto.id,
        jobFunctionId: createDto.jobFunctionId,
        jobPositionId: createDto.jobPositionId,
        director: createDto.director,
        manager: createDto.manager,
        areaOfActivity: createDto.areaOfActivity,
        userId: createDto.userId,
        workspaceId: createDto.workspaceId,
      } as EmployeeInfo;

      (repository.repository.create as jest.Mock).mockReturnValue(newEmployeeInfo);
      (repository.repository.save as jest.Mock).mockResolvedValue(newEmployeeInfo);

      const result = await service.upsert(createDto.userId, createDto.workspaceId, createDto);

      expect(repository.repository.create as jest.Mock).toHaveBeenCalledWith(createDto);
      expect(repository.repository.save as jest.Mock).toHaveBeenCalledWith(newEmployeeInfo);
      expect(result).toEqual(newEmployeeInfo);
    });
  });
});
