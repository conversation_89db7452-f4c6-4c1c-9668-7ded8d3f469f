import { UserSanitizer } from '../../application/user-sanitizer';
import { InvalidEmailException } from '../../domain/exceptions/invalid-email.exception';

describe('UserSanitizer', () => {
  describe('cleanUserData', () => {
    it('should clean and normalize a valid user data object', () => {
      const input = {
        email: '  <EMAIL> ',
        ein: '123.0',
        phone: '(*************',
        name: '  <PERSON>  ',
        birthday: '',
      };

      const result = UserSanitizer.cleanUserData<typeof input>(input);

      expect(result.email).toBe('<EMAIL>');
      expect(result.ein).toBe('123');
      expect(result.phone).toBe('1234567890');
      expect(result.name).toBe('<PERSON>');
      expect(result.birthday).toBeNull();
    });

    it('should skip fields that are not provided', () => {
      const input = { name: ' <PERSON> ' };
      const result = UserSanitizer.cleanUserData<typeof input>(input);
      expect(result.name).toBe('Alice');
    });

    it('should throw InvalidEmailException for invalid email', () => {
      const input = { email: 'invalid_email' };
      expect(() => UserSanitizer.cleanUserData<typeof input>(input)).toThrow(InvalidEmailException);
    });
  });

  describe('formatPhoneNumber', () => {
    it('should remove common formatting characters', () => {
      expect(UserSanitizer.formatPhoneNumber('(*************')).toBe('1234567890');
      expect(UserSanitizer.formatPhoneNumber('************')).toBe('1234567890');
    });
  });

  describe('formatEin', () => {
    it('should truncate EIN decimals', () => {
      expect(UserSanitizer.formatEin('123.0')).toBe('123');
      expect(UserSanitizer.formatEin('456.99')).toBe('456');
    });

    it('should return unmodified string if not float', () => {
      expect(UserSanitizer.formatEin('ABC123')).toBe('ABC123');
    });
  });

  describe('cleanObject', () => {
    it('should remove null and undefined properties', () => {
      const obj = {
        a: 'value',
        b: null,
        c: undefined,
        d: 0,
      };
      const result = UserSanitizer.cleanObject<typeof obj>(obj);
      expect(result).toEqual({ a: 'value', d: 0 });
    });
  });

  describe('cleanEmail', () => {
    it('should clean valid email input', () => {
      const result = UserSanitizer.cleanEmail('<EMAIL>');
      expect(result).toBe('<EMAIL>');
    });

    it('should throw for missing email', () => {
      expect(() => UserSanitizer.cleanEmail(undefined)).toThrow(InvalidEmailException);
    });

    it('should throw for invalid email structure', () => {
      expect(() => UserSanitizer.cleanEmail('invalid@@domain')).toThrow(InvalidEmailException);
      expect(() => UserSanitizer.cleanEmail('@missinglocal')).toThrow(InvalidEmailException);
    });
  });
});
