import { InvalidEmailException } from '../../domain/exceptions/invalid-email.exception';
import { UserSanitizer } from '../../application/user-sanitizer';

describe('cleanEmail', () => {
  it('should return cleaned email when email is valid', () => {
    const validEmail = '<EMAIL>';
    const result = UserSanitizer.cleanEmail(validEmail);
    expect(result).toBe('<EMAIL>');
  });

  it('should remove invalid characters while maintaining valid structure', () => {
    const emailWithInvalidChars = 'u$er#name@exa!mple.com';
    const result = UserSanitizer.cleanEmail(emailWithInvalidChars);
    expect(result).toBe('<EMAIL>');
  });

  it('should throw exception when cleaning results in invalid email', () => {
    expect(() => UserSanitizer.cleanEmail('u$er@n@me@exa!mple.com')).toThrow(InvalidEmailException);
  });

  it('should throw exception when email is undefined', () => {
    expect(() => UserSanitizer.cleanEmail(undefined)).toThrow(InvalidEmailException);
  });

  it('should throw exception when email is empty', () => {
    expect(() => UserSanitizer.cleanEmail('')).toThrow(InvalidEmailException);
  });

  it('should throw exception when email does not contain @', () => {
    expect(() => UserSanitizer.cleanEmail('userexample.com')).toThrow(InvalidEmailException);
  });

  it('should throw exception when email contains multiple @', () => {
    expect(() => UserSanitizer.cleanEmail('user@<EMAIL>')).toThrow(InvalidEmailException);
  });

  it('should throw exception when local part (before @) is missing', () => {
    expect(() => UserSanitizer.cleanEmail('@example.com')).toThrow(InvalidEmailException);
  });

  it('should throw exception when domain (after @) is missing', () => {
    expect(() => UserSanitizer.cleanEmail('user@')).toThrow(InvalidEmailException);
  });

  it('should preserve valid special characters like +, ., - and _', () => {
    const emailWithSpecialChars = '<EMAIL>';
    const result = UserSanitizer.cleanEmail(emailWithSpecialChars);
    expect(result).toBe('<EMAIL>');
  });
});
