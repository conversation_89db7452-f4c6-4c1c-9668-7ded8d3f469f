import { Test, TestingModule } from '@nestjs/testing';
import { UsersController } from '../../../presentation/controllers/users.controller';

import { AuthUser } from '@keeps-node-apis/@core';
import { UpdateUserDto } from '../../../application/dtos/update-user.dto';
import { UpdateUserStatusDto } from '../../../application/dtos/update-user-status.dto';
import { UsersService } from '../../../application/services/users.service';
import { UserRolesService } from '../../../application/services/user-roles.service';
import { EmployeeInfoService } from '../../../application/services/employee-info.service';
import { UpdateUsersService } from '../../../application/services/update-users.service';

describe('UsersController', () => {
  let controller: UsersController;
  let usersServiceMock: Partial<Record<keyof UsersService, jest.Mock>>;
  let userRolesServiceMock: Partial<Record<keyof UserRolesService, jest.Mock>>;
  let employeeInfoServiceMock: Partial<Record<keyof EmployeeInfoService, jest.Mock>>;
  let updateUsersServiceMock: Partial<Record<keyof UpdateUsersService, jest.Mock>>;

  beforeEach(async () => {
    usersServiceMock = {
      findAllowed: jest.fn(),
      findInfo: jest.fn(),
      findOneById: jest.fn(),
      setPassword: jest.fn(),
      setPasswordBatch: jest.fn(),
    };

    userRolesServiceMock = {
      findUserWorkspace: jest.fn(),
    };

    employeeInfoServiceMock = {
      findAllowed: jest.fn(),
      findUniqueValues: jest.fn(),
    };

    updateUsersServiceMock = {
      update: jest.fn(),
      updateUserStatus: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [UsersController],
      providers: [
        { provide: UsersService, useValue: usersServiceMock },
        { provide: UserRolesService, useValue: userRolesServiceMock },
        { provide: EmployeeInfoService, useValue: employeeInfoServiceMock },
        { provide: UpdateUsersService, useValue: updateUsersServiceMock },
      ],
    }).compile();

    controller = module.get<UsersController>(UsersController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('updateUser', () => {
    it('should call updateUsersService.update with the correct parameters', async () => {
      const userId = 'user-id';
      const workspaceId = 'workspace-id';
      const updateUserDto: UpdateUserDto = { name: 'Updated Name' };
      const authenticatedUser: AuthUser = { sub: 'mock-user-id', legacyRoles: [] } as AuthUser;

      updateUsersServiceMock.update.mockResolvedValue(updateUserDto); // Mock return

      const result = await controller.updateUser(authenticatedUser, userId, updateUserDto, workspaceId);

      expect(updateUsersServiceMock.update).toHaveBeenCalledWith(authenticatedUser, userId, updateUserDto, workspaceId);
      expect(result).toEqual(updateUserDto); // Check returned value
    });
  });

  describe('findAllInfoViewProjection', () => {
    it('should call usersService.findInfo', async () => {
      const user: AuthUser = { sub: 'user-id' } as AuthUser;
      const workspaceId = 'workspace-id';

      await controller.findAllInfoViewProjection(user, workspaceId);
      expect(usersServiceMock.findInfo).toHaveBeenCalledWith(user.sub, workspaceId);
    });
  });

  describe('setPasswordBatch', () => {
    it('should call usersService.setPasswordBatch', async () => {
      const changePasswordsDto = [];
      await controller.setPasswordBatch(changePasswordsDto);
      expect(usersServiceMock.setPasswordBatch).toHaveBeenCalledWith(changePasswordsDto);
    });
  });

  describe('updateUserStatus', () => {
    it('should call update status informing the currently logged in user id', async () => {
      const userId = 'user-id';
      const workspaceId = 'workspace-id';
      const user: AuthUser = { sub: 'auth-user-id' } as AuthUser;
      const updateUserStatusDto: UpdateUserStatusDto = { status: true };

      await controller.updateUserStatus(userId, user, workspaceId, updateUserStatusDto);
      expect(updateUsersServiceMock.updateUserStatus).toHaveBeenCalledWith(
        userId,
        user.sub,
        workspaceId,
        updateUserStatusDto,
      );
    });
  });
});
