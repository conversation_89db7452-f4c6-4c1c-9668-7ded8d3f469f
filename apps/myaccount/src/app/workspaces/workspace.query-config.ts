import { PaginateConfig, FilterOperator } from 'nestjs-paginate';
import { Workspace } from '../entities/workspace.entity';

export const WORKSPACE_PAGINATION_CONFIG: PaginateConfig<Workspace> = {
  select: [
    'id',
    'name',
    'description',
    'address',
    'city',
    'country',
    'iconUrl',
    'logoUrl',
    'postCode',
    'state',
    'themeId',
    'themeDark',
    'companyId',
    'status',
    'hashId',
  ],
  sortableColumns: ['name', 'city', 'country', 'createdDate'],
  searchableColumns: ['id', 'name', 'description', 'city', 'country'],
  defaultSortBy: [['name', 'ASC']],
  relations: ['serviceWorkspaces.service.application'],
  filterableColumns: {
    id: [FilterOperator.IN],
    name: [FilterOperator.ILIKE],
    city: [FilterOperator.ILIKE],
    country: [FilterOperator.ILIKE],
    status: [FilterOperator.EQ],
    'serviceWorkspaces.service.applicationId': [FilterOperator.IN],
  },
};
