import { FilterOperator, PaginateConfig } from 'nestjs-paginate';
import { Workspace } from '../entities/workspace.entity';

export const WORKSPACES_PAGINATION_CONFIG: PaginateConfig<Workspace> = {
  select: ['id', 'name', 'iconSvgUrl', 'logoUrl', 'iconUrl', 'customColor', 'themeId', 'createdDate'],
  sortableColumns: ['name', 'createdDate'],
  searchableColumns: ['id', 'name'],
  defaultSortBy: [['name', 'ASC']],
  maxLimit: -1,
  filterableColumns: {
    id: [FilterOperator.IN],
    name: [FilterOperator.ILIKE],
    'r.application_id': [FilterOperator.EQ],
    'sw.status': [FilterOperator.EQ],
  },
};
