import { FilterOperator, PaginateConfig } from 'nestjs-paginate';
import { Workspace } from '../entities/workspace.entity';

export const WORKSPACES_PAGINATION_CONFIG: PaginateConfig<Workspace> = {
  select: ['id', 'name', 'iconSvgUrl', 'logoUrl', 'iconUrl', 'customColor', 'themeId', 'createdDate'],
  sortableColumns: ['name', 'createdDate'],
  searchableColumns: ['id', 'name'],
  defaultSortBy: [['createdDate', 'DESC']],
  maxLimit: -1,
  relations: ['userRoleWorkspaces.role.application'],
  filterableColumns: {
    id: [FilterOperator.IN],
    name: [FilterOperator.ILIKE],
    'userRoleWorkspaces.role.applicationId': [FilterOperator.IN],
  },
};
