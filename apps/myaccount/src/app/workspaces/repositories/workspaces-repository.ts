import { Paginated, PaginateQuery } from 'nestjs-paginate';
import { Workspace } from '../../entities/workspace.entity';
import { Repository } from 'typeorm';

export abstract class WorkspacesRepository {
  abstract findAll(filters: PaginateQuery): Promise<Paginated<Workspace>>;
  abstract findAllUserWorkspaces(userId: string, query: PaginateQuery): Promise<Paginated<Workspace>>;
  abstract getWorkspacesByApplicationAndUser(userId: string, applicationId: string);
  abstract get repository(): Repository<Workspace>;
  abstract inactivateWorkspace(workspaceId: string): Promise<void>;
}
