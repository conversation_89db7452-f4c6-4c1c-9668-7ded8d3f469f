import { Repository } from 'typeorm';
import { Injectable, NotFoundException } from '@nestjs/common';
import { Workspace } from '../../entities/workspace.entity';
import { Service } from '../../entities/service.entity';
import { ServiceWorkspace } from '../../entities/service-workspace.entity';
import { WorkspacesRepository } from './workspaces-repository';
import { InjectRepository } from '@nestjs/typeorm';
import { paginate, PaginateConfig, Paginated, PaginateQuery } from 'nestjs-paginate';
import { WORKSPACE_PAGINATION_CONFIG } from '../workspace.query-config';
import { WORKSPACES_PAGINATION_CONFIG } from '../workspaces.query-config';

@Injectable()
export class WorkspacesTypeOrmRepository implements WorkspacesRepository {
  constructor(@InjectRepository(Workspace) readonly _repository: Repository<Workspace>) {}

  async findAll(query: <PERSON>ginateQuery): Promise<Paginated<Workspace>> {
    const config: PaginateConfig<Workspace> = {
      ...WORKSPACE_PAGINATION_CONFIG,
      maxLimit: -1,
    };

    return await paginate<Workspace>(query, this.repository, config);
  }

  async findAllUserWorkspaces(userId: string, query: PaginateQuery) {
    // Handle applicationId filter to build the correct subquery
    const applicationIdFilter = this.extractApplicationIdFilter(query);

    let baseQuery: string;
    let parameters: any;

    if (applicationIdFilter && applicationIdFilter.length > 0) {
      // When filtering by applicationId, use subquery to avoid duplicates
      baseQuery = `
        SELECT DISTINCT w.id
        FROM workspace w
        INNER JOIN user_role_workspace urw ON urw.workspace_id = w.id
        INNER JOIN role r ON r.id = urw.role_id
        WHERE urw.user_id = :userId
          AND urw.status = true
          AND r.application_id IN (:...applicationIds)
      `;
      parameters = { userId, applicationIds: applicationIdFilter };
    } else {
      // When not filtering, simple subquery
      baseQuery = `
        SELECT DISTINCT urw.workspace_id as id
        FROM user_role_workspace urw
        WHERE urw.user_id = :userId
          AND urw.status = true
      `;
      parameters = { userId };
    }

    const queryBuilder = this.repository
      .createQueryBuilder('workspace')
      .where(`workspace.id IN (${baseQuery})`)
      .setParameters(parameters)
      .select([
        'workspace.id',
        'workspace.name',
        'workspace.iconSvgUrl',
        'workspace.logoUrl',
        'workspace.iconUrl',
        'workspace.customColor',
        'workspace.themeId',
      ]);

    const config: PaginateConfig<Workspace> = {
      ...WORKSPACES_PAGINATION_CONFIG,
    };

    return await paginate<Workspace>({ ...query, limit: -1 }, queryBuilder, config);
  }

  private extractApplicationIdFilter(query: PaginateQuery): string[] | null {
    const filter = query.filter;
    if (!filter) return null;

    // Check for applicationId filter in various possible formats
    const applicationIdKey = Object.keys(filter).find(
      (key) => key.includes('applicationId') || key.includes('application_id'),
    );

    if (!applicationIdKey) return null;

    const filterValue = filter[applicationIdKey];
    if (typeof filterValue === 'string') {
      // Handle $in:value1,value2 format
      if (filterValue.startsWith('$in:')) {
        return filterValue
          .substring(4)
          .split(',')
          .map((id) => id.trim());
      }
      return [filterValue];
    }

    if (Array.isArray(filterValue)) {
      return filterValue;
    }

    return null;
  }

  getWorkspacesByApplicationAndUser(userId: string, applicationId: string) {
    return this.repository
      .createQueryBuilder('ws')
      .innerJoin('user_role_workspace', 'urw', 'urw.workspace_id = ws.id')
      .innerJoin('role', 'r', 'urw.role_id = r.id')
      .innerJoin('application', 'app', 'r.application_id = app.id')
      .innerJoin('service_workspace', 'sw', 'sw.workspace_id = ws.id')
      .leftJoinAndMapMany('ws.services', Service, 's', 's.id = sw.service_id AND sw.status = true')
      .where('urw.user_id = :userId', { userId })
      .andWhere('urw.status = true')
      .andWhere('app.id = :applicationId', { applicationId })
      .andWhere('ws.status = true')
      .getMany();
  }

  /**
   * @inheritDoc
   */
  async inactivateWorkspace(workspaceId: string): Promise<void> {
    await this.repository.manager.transaction(async (manager) => {
      const workspace = await manager.findOne(Workspace, {
        where: { id: workspaceId },
      });

      if (!workspace) {
        throw new NotFoundException(`Workspace with ID ${workspaceId} not found`);
      }

      workspace.status = false;
      workspace.enableEmailNotifications = false;
      await manager.save(workspace);

      await manager
        .createQueryBuilder()
        .update(ServiceWorkspace)
        .set({ status: false })
        .where('workspaceId = :workspaceId', { workspaceId })
        .execute();
    });
  }

  get repository(): Repository<Workspace> {
    return this._repository;
  }
}
