import { Repository } from 'typeorm';
import { Injectable, NotFoundException } from '@nestjs/common';
import { Workspace } from '../../entities/workspace.entity';
import { Service } from '../../entities/service.entity';
import { ServiceWorkspace } from '../../entities/service-workspace.entity';
import { WorkspacesRepository } from './workspaces-repository';
import { InjectRepository } from '@nestjs/typeorm';
import { paginate, PaginateConfig, Paginated, PaginateQuery } from 'nestjs-paginate';
import { WORKSPACE_PAGINATION_CONFIG } from '../workspace.query-config';
import { WORKSPACES_PAGINATION_CONFIG } from '../workspaces.query-config';

@Injectable()
export class WorkspacesTypeOrmRepository implements WorkspacesRepository {
  constructor(@InjectRepository(Workspace) readonly _repository: Repository<Workspace>) {}

  async findAll(query: PaginateQuery): Promise<Paginated<Workspace>> {
    const config: PaginateConfig<Workspace> = {
      ...WORKSPACE_PAGINATION_CONFIG,
      maxLimit: -1,
    };

    return await paginate<Workspace>(query, this.repository, config);
  }

  async findAllUserWorkspaces(userId: string, query: PaginateQuery) {
    // Using your optimized query structure - adapted for TypeORM entity-based approach
    const queryBuilder = this.repository
      .createQueryBuilder('w')
      .distinct(true)
      .innerJoin('user_role_workspace', 'urw', 'urw.workspace_id = w.id')
      .leftJoin('role', 'r', 'urw.role_id = r.id')
      .leftJoin('service_workspace', 'sw', 'sw.workspace_id = urw.workspace_id')
      .where('urw.user_id = :userId', { userId })
      .andWhere('urw.status = true')
      .andWhere('w.status = true');

    // Handle manual filters
    this.applyManualFilters(queryBuilder, query);

    const config: PaginateConfig<Workspace> = {
      ...WORKSPACES_PAGINATION_CONFIG,
    };

    return await paginate<Workspace>({ ...query, limit: -1 }, queryBuilder, config);
  }

  private applyManualFilters(queryBuilder: any, query: PaginateQuery) {
    if (!query.filter) return;

    // Handle applicationId filter
    const applicationIdFilter = this.extractFilterValue(query, 'applicationId');
    if (applicationIdFilter) {
      queryBuilder.andWhere('r.application_id = :applicationId', { applicationId: applicationIdFilter });
    }

    // Handle sw.status filter
    const swStatusFilter = this.extractFilterValue(query, 'sw.status');
    if (swStatusFilter !== null) {
      const statusValue = swStatusFilter === 'true' || swStatusFilter === true;
      queryBuilder.andWhere('sw.status = :swStatus', { swStatus: statusValue });
    }
  }

  private extractFilterValue(query: PaginateQuery, filterKey: string): any {
    if (!query.filter) return null;

    // Check for exact key match
    if (query.filter[filterKey] !== undefined) {
      const value = query.filter[filterKey];
      // Handle $eq:value format
      if (typeof value === 'string' && value.startsWith('$eq:')) {
        return value.substring(4);
      }
      return value;
    }

    // Check for keys that contain the filterKey
    const foundKey = Object.keys(query.filter).find((key) => key.includes(filterKey));
    if (foundKey) {
      const value = query.filter[foundKey];
      if (typeof value === 'string' && value.startsWith('$eq:')) {
        return value.substring(4);
      }
      return value;
    }

    return null;
  }

  getWorkspacesByApplicationAndUser(userId: string, applicationId: string) {
    return this.repository
      .createQueryBuilder('ws')
      .innerJoin('user_role_workspace', 'urw', 'urw.workspace_id = ws.id')
      .innerJoin('role', 'r', 'urw.role_id = r.id')
      .innerJoin('application', 'app', 'r.application_id = app.id')
      .innerJoin('service_workspace', 'sw', 'sw.workspace_id = ws.id')
      .leftJoinAndMapMany('ws.services', Service, 's', 's.id = sw.service_id AND sw.status = true')
      .where('urw.user_id = :userId', { userId })
      .andWhere('urw.status = true')
      .andWhere('app.id = :applicationId', { applicationId })
      .andWhere('ws.status = true')
      .getMany();
  }

  /**
   * @inheritDoc
   */
  async inactivateWorkspace(workspaceId: string): Promise<void> {
    await this.repository.manager.transaction(async (manager) => {
      const workspace = await manager.findOne(Workspace, {
        where: { id: workspaceId },
      });

      if (!workspace) {
        throw new NotFoundException(`Workspace with ID ${workspaceId} not found`);
      }

      workspace.status = false;
      workspace.enableEmailNotifications = false;
      await manager.save(workspace);

      await manager
        .createQueryBuilder()
        .update(ServiceWorkspace)
        .set({ status: false })
        .where('workspaceId = :workspaceId', { workspaceId })
        .execute();
    });
  }

  get repository(): Repository<Workspace> {
    return this._repository;
  }
}
