import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WorkspaceCustomMenuItemsService } from './workspace-custom-menu-items.service';
import { WorkspaceCustomMenuItem } from '../../entities/workspace-custom-menu-item.entity';
import { CreateWorkspaceCustomMenuItemDto } from '../dto/create-workspace-custom-menu-item.dto';

describe('WorkspaceCustomMenuItemsService', () => {
  let service: WorkspaceCustomMenuItemsService;
  let repository: jest.Mocked<Repository<WorkspaceCustomMenuItem>>;

  const mockWorkspaceCustomMenuItem: WorkspaceCustomMenuItem = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    name: 'Custom Dashboard',
    icon: 'dashboard-icon',
    workspaceId: '456e7890-e89b-12d3-a456-426614174001',
    workspace: null,
    createdDate: new Date('2023-01-01T00:00:00Z'),
    updatedDate: new Date('2023-01-01T00:00:00Z'),
  } as WorkspaceCustomMenuItem;

  const mockCreateDto: CreateWorkspaceCustomMenuItemDto = {
    name: 'Custom Dashboard',
    icon: 'dashboard-icon',
    workspaceId: '456e7890-e89b-12d3-a456-426614174001',
  } as CreateWorkspaceCustomMenuItemDto;

  beforeEach(async () => {
    const mockRepository = {
      create: jest.fn(),
      save: jest.fn(),
      find: jest.fn(),
      findOne: jest.fn(),
      findOneBy: jest.fn(),
      findOneByOrFail: jest.fn(),
      delete: jest.fn(),
      update: jest.fn(),
      remove: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorkspaceCustomMenuItemsService,
        {
          provide: getRepositoryToken(WorkspaceCustomMenuItem),
          useValue: mockRepository,
        },
      ],
    }).compile();

    service = module.get<WorkspaceCustomMenuItemsService>(WorkspaceCustomMenuItemsService);
    repository = module.get(getRepositoryToken(WorkspaceCustomMenuItem));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new workspace custom menu item', async () => {
      repository.create.mockReturnValue(mockWorkspaceCustomMenuItem);
      repository.save.mockResolvedValue(mockWorkspaceCustomMenuItem);

      const result = await service.create(mockCreateDto);

      expect(repository.create).toHaveBeenCalledWith(mockCreateDto);
      expect(repository.save).toHaveBeenCalledWith(mockWorkspaceCustomMenuItem);
      expect(result).toEqual(mockWorkspaceCustomMenuItem);
    });

    it('should handle repository errors during creation', async () => {
      const error = new Error('Database error');
      repository.create.mockReturnValue(mockWorkspaceCustomMenuItem);
      repository.save.mockRejectedValue(error);

      await expect(service.create(mockCreateDto)).rejects.toThrow('Database error');
      expect(repository.create).toHaveBeenCalledWith(mockCreateDto);
      expect(repository.save).toHaveBeenCalledWith(mockWorkspaceCustomMenuItem);
    });
  });

  describe('findByWorkspaceId', () => {
    it('should return custom menu items for a workspace', async () => {
      const workspaceId = '456e7890-e89b-12d3-a456-426614174001';
      const mockItems = [mockWorkspaceCustomMenuItem];

      repository.find.mockResolvedValue(mockItems);

      const result = await service.findByWorkspaceId(workspaceId);

      expect(repository.find).toHaveBeenCalledWith({
        where: { workspaceId },
      });
      expect(result).toEqual(mockItems);
    });

    it('should return empty array when no items found', async () => {
      const workspaceId = '456e7890-e89b-12d3-a456-426614174001';
      repository.find.mockResolvedValue([]);

      const result = await service.findByWorkspaceId(workspaceId);

      expect(repository.find).toHaveBeenCalledWith({
        where: { workspaceId },
      });
      expect(result).toEqual([]);
    });

    it('should handle repository errors during find', async () => {
      const workspaceId = '456e7890-e89b-12d3-a456-426614174001';
      const error = new Error('Database error');
      repository.find.mockRejectedValue(error);

      await expect(service.findByWorkspaceId(workspaceId)).rejects.toThrow('Database error');
      expect(repository.find).toHaveBeenCalledWith({
        where: { workspaceId },
      });
    });
  });

  describe('remove', () => {
    it('should remove a workspace custom menu item', async () => {
      const id = '123e4567-e89b-12d3-a456-426614174000';
      const workspaceId = '456e7890-e89b-12d3-a456-426614174001';

      repository.findOneByOrFail.mockResolvedValue(mockWorkspaceCustomMenuItem);
      repository.remove.mockResolvedValue(mockWorkspaceCustomMenuItem);

      await service.remove(id, workspaceId);

      expect(repository.findOneByOrFail).toHaveBeenCalledWith({
        id,
        workspaceId,
      });
      expect(repository.remove).toHaveBeenCalledWith(mockWorkspaceCustomMenuItem);
    });

    it('should throw NotFoundException when item not found', async () => {
      const id = 'non-existent-id';
      const workspaceId = '456e7890-e89b-12d3-a456-426614174001';

      repository.findOneByOrFail.mockRejectedValue(new Error('Entity not found'));

      await expect(service.remove(id, workspaceId)).rejects.toThrow();
      expect(repository.findOneByOrFail).toHaveBeenCalledWith({
        id,
        workspaceId,
      });
      expect(repository.remove).not.toHaveBeenCalled();
    });
  });
});
