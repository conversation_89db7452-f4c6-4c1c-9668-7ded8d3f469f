### Create a workspace
POST http://localhost:3000/api/workspaces
Content-Type: application/json

{
    "name": "Workspace 1",
    "description": "Workspace 1",
    "smtpAuthPass": "senhamuitofoda"
}

### Get all workspaces
GET http://localhost:3000/api/workspaces
Content-Type: application/json

### Get workspaces filtered by user's role application ID
GET http://localhost:3000/api/workspaces?filter.userRoleWorkspaces.role.applicationId=$eq:app-id-1
Content-Type: application/json

### Get workspaces with active service workspaces only
GET http://localhost:3000/api/workspaces?filter.sw.status=$eq:true
Content-Type: application/json

### Get workspaces with inactive service workspaces
GET http://localhost:3000/api/workspaces?filter.sw.status=$eq:false
Content-Type: application/json

### Get workspaces with combined filters
GET http://localhost:3000/api/workspaces?filter.name=$ilike:workspace&filter.sw.status=$eq:true&filter.userRoleWorkspaces.role.applicationId=$eq:app-id-1
Content-Type: application/json

### Get workspaces sorted by name
GET http://localhost:3000/api/workspaces?sortBy=name:ASC
Content-Type: application/json

### Update workspace
PATCH  http://localhost:3000/api/workspaces/0384ce00-bffa-48a1-b294-4591cfa8cd50
Content-Type: application/json

{
    "name": "Workspace 2"
}

### Get workspace theme
GET http://localhost:3000/api/workspaces/0384ce00-bffa-48a1-b294-4591cfa8cd50/theme
Content-Type: application/json


### Set new service on workspace
POST http://localhost:3000/api/workspaces/66021cca-8294-4508-a3fa-4c7a41c372c2/services/0d3752f0-15d7-402a-8628-04ed47bcbf41
Content-Type: application/json
