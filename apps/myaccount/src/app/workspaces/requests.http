### Create a workspace
POST http://localhost:3000/api/workspaces
Content-Type: application/json

{
    "name": "Workspace 1",
    "description": "Workspace 1",
    "smtpAuthPass": "senhamuitofoda"
}

### Get all workspaces
GET http://localhost:3000/api/workspaces
Content-Type: application/json

### Get workspaces filtered by application ID
GET http://localhost:3000/api/workspaces?filter.serviceWorkspaces.service.applicationId=$in:app-id-1,app-id-2
Content-Type: application/json

### Get workspaces with pagination and filters
GET http://localhost:3000/api/workspaces?page=1&limit=10&filter.name=$ilike:workspace&filter.serviceWorkspaces.service.applicationId=$in:app-id-1
Content-Type: application/json

### Get workspaces sorted by name
GET http://localhost:3000/api/workspaces?sortBy=name:ASC
Content-Type: application/json

### Update workspace
PATCH  http://localhost:3000/api/workspaces/0384ce00-bffa-48a1-b294-4591cfa8cd50
Content-Type: application/json

{
    "name": "Workspace 2"
}

### Get workspace theme
GET http://localhost:3000/api/workspaces/0384ce00-bffa-48a1-b294-4591cfa8cd50/theme
Content-Type: application/json


### Set new service on workspace
POST http://localhost:3000/api/workspaces/66021cca-8294-4508-a3fa-4c7a41c372c2/services/0d3752f0-15d7-402a-8628-04ed47bcbf41
Content-Type: application/json
