import { Body, Controller, Delete, Get, Headers, Param, Patch, Post } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Roles, Serialize, MYACCOUNT_ADMIN_ROLES } from '@keeps-node-apis/@core';
import { WorkspaceCustomMenuItemsService } from '../services/workspace-custom-menu-items.service';
import {
  CreateWorkspaceCustomMenuItemDto,
  CreateWorkspaceCustomMenuItemRequestDto,
} from '../dto/create-workspace-custom-menu-item.dto';
import { WorkspaceCustomMenuItemDto } from '../dto/workspace-custom-menu-item.dto';
import { UpdateWorkspaceCustomMenuItemDto } from '../dto/update-workspace-custom-menu-item.dto';

@ApiTags('custom-menu-items')
@Controller('custom-menu-items')
export class WorkspaceCustomMenuItemsController {
  constructor(private readonly workspaceCustomMenuItemsService: WorkspaceCustomMenuItemsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new workspace custom menu item' })
  @Roles(MYACCOUNT_ADMIN_ROLES)
  @ApiBody({ type: CreateWorkspaceCustomMenuItemRequestDto })
  @ApiResponse({ status: 201, description: 'Custom menu item created successfully', type: WorkspaceCustomMenuItemDto })
  @Serialize(WorkspaceCustomMenuItemDto)
  createCustomMenuItem(
    @Body() requestDto: CreateWorkspaceCustomMenuItemRequestDto,
    @Headers('x-client') workspaceId: string,
  ) {
    const createWorkspaceCustomMenuItemDto: CreateWorkspaceCustomMenuItemDto = {
      ...requestDto,
      workspaceId,
    };
    return this.workspaceCustomMenuItemsService.create(createWorkspaceCustomMenuItemDto);
  }

  @ApiOperation({ summary: 'List all custom menu items for a workspace' })
  @ApiResponse({
    status: 200,
    description: 'List of custom menu items',
    type: WorkspaceCustomMenuItemDto,
    isArray: true,
  })
  @Serialize(WorkspaceCustomMenuItemDto)
  @Roles(MYACCOUNT_ADMIN_ROLES)
  @ApiBody({ required: false, type: undefined })
  @Get()
  listCustomMenuItems(@Headers('x-client') workspaceId: string) {
    return this.workspaceCustomMenuItemsService.findByWorkspaceId(workspaceId);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a workspace custom menu item' })
  @ApiParam({ name: 'id', description: 'The ID of the custom menu item', type: String })
  @Roles(MYACCOUNT_ADMIN_ROLES)
  @ApiBody({ type: UpdateWorkspaceCustomMenuItemDto })
  @ApiResponse({ status: 200, description: 'Custom menu item updated successfully', type: WorkspaceCustomMenuItemDto })
  @Serialize(WorkspaceCustomMenuItemDto)
  updateCustomMenuItem(
    @Param('id') id: string,
    @Body() updateDto: UpdateWorkspaceCustomMenuItemDto,
    @Headers('x-client') workspaceId: string,
  ) {
    return this.workspaceCustomMenuItemsService.update(id, { ...updateDto, workspaceId });
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a workspace custom menu item' })
  @ApiParam({ name: 'id', description: 'The ID of the custom menu item', type: String })
  @Roles(MYACCOUNT_ADMIN_ROLES)
  @ApiResponse({ status: 200, description: 'Custom menu item deleted successfully' })
  async deleteCustomMenuItem(@Param('id') id: string, @Headers('x-client') workspaceId: string) {
    await this.workspaceCustomMenuItemsService.remove(id, workspaceId);
    return { message: 'Custom menu item deleted successfully' };
  }
}
