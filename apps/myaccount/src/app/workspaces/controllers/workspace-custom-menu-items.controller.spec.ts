import { Test, TestingModule } from '@nestjs/testing';
import { WorkspaceCustomMenuItemsController } from './workspace-custom-menu-items.controller';
import { WorkspaceCustomMenuItemsService } from '../services/workspace-custom-menu-items.service';
import { CreateWorkspaceCustomMenuItemRequestDto } from '../dto/create-workspace-custom-menu-item.dto';
import { UpdateWorkspaceCustomMenuItemDto } from '../dto/update-workspace-custom-menu-item.dto';

const mockService = {
  create: jest.fn(),
  update: jest.fn(),
  findByWorkspaceId: jest.fn(),
  remove: jest.fn(),
};

describe('WorkspaceCustomMenuItemsController', () => {
  let controller: WorkspaceCustomMenuItemsController;
  let service: WorkspaceCustomMenuItemsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [WorkspaceCustomMenuItemsController],
      providers: [{ provide: WorkspaceCustomMenuItemsService, useValue: mockService }],
    }).compile();

    controller = module.get<WorkspaceCustomMenuItemsController>(WorkspaceCustomMenuItemsController);
    service = module.get<WorkspaceCustomMenuItemsService>(WorkspaceCustomMenuItemsService);
    jest.clearAllMocks();
  });

  it('should create a custom menu item', async () => {
    const dto: CreateWorkspaceCustomMenuItemRequestDto = {
      name: 'Menu',
      icon: 'icon',
      url: 'https://valid.com',
    };
    const workspaceId = 'workspace-uuid';
    const expected = { ...dto, workspaceId, id: 'item-uuid' };
    mockService.create.mockResolvedValue(expected);

    const result = await controller.createCustomMenuItem(dto, workspaceId);
    expect(service.create).toHaveBeenCalledWith({ ...dto, workspaceId });
    expect(result).toEqual(expected);
  });

  it('should list custom menu items for a workspace', async () => {
    const workspaceId = 'workspace-uuid';
    const expected = [
      { id: 'item-1', name: 'Menu1', icon: 'icon1', url: 'https://a.com', workspaceId },
      { id: 'item-2', name: 'Menu2', icon: 'icon2', url: 'https://b.com', workspaceId },
    ];
    mockService.findByWorkspaceId = jest.fn().mockResolvedValue(expected);
    const controller = new WorkspaceCustomMenuItemsController(mockService as any);
    const result = await controller.listCustomMenuItems(workspaceId);
    expect(mockService.findByWorkspaceId).toHaveBeenCalledWith(workspaceId);
    expect(result).toEqual(expected);
  });

  it('should update a custom menu item', async () => {
    const id = 'item-uuid';
    const updateDto: UpdateWorkspaceCustomMenuItemDto = { name: 'Updated', url: 'https://updated.com' };
    const workspaceId = 'workspace-uuid';
    const expected = { ...updateDto, workspaceId, id };
    mockService.update.mockResolvedValue(expected);

    const result = await controller.updateCustomMenuItem(id, updateDto, workspaceId);
    expect(service.update).toHaveBeenCalledWith(id, { ...updateDto, workspaceId });
    expect(result).toEqual(expected);
  });

  it('should delete a custom menu item', async () => {
    const id = 'item-uuid';
    const workspaceId = 'workspace-uuid';
    mockService.remove.mockResolvedValue(undefined);

    const result = await controller.deleteCustomMenuItem(id, workspaceId);
    expect(service.remove).toHaveBeenCalledWith(id, workspaceId);
    expect(result).toEqual({ message: 'Custom menu item deleted successfully' });
  });
});
