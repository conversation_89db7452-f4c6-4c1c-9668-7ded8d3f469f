import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsOptional, IsString, IsUrl } from 'class-validator';
import { CreateWorkspaceCustomMenuItemRequestDto } from './create-workspace-custom-menu-item.dto';

export class UpdateWorkspaceCustomMenuItemDto extends PartialType(CreateWorkspaceCustomMenuItemRequestDto) {
  @ApiProperty({ type: String, description: 'Name of the custom menu item', required: false })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ type: String, description: 'Icon for the custom menu item', required: false })
  @IsString()
  @IsOptional()
  icon?: string;

  @ApiProperty({ type: String, description: 'Url of the custom menu item', required: false })
  @IsString()
  @IsOptional()
  @IsUrl()
  url?: string;
}
