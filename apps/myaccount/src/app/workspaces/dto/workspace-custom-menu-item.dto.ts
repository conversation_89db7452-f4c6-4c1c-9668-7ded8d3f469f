import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class WorkspaceCustomMenuItemDto {
  @ApiProperty({
    type: String,
    description: 'Unique identifier of the custom menu item',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  id: string;

  @ApiProperty({
    type: String,
    description: 'Name of the custom menu item',
    example: 'Custom Dashboard',
  })
  @Expose()
  name: string;

  @ApiProperty({
    type: String,
    description: 'Url of the custom menu item',
    example: 'Custom https://example.com',
  })
  @Expose()
  url: string;

  @ApiProperty({
    type: String,
    description: 'Icon for the custom menu item',
    example: 'dashboard-icon',
  })
  @Expose()
  icon: string;
}
