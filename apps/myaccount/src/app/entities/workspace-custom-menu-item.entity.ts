import { Column, <PERSON>tity, Index, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm';
import { BaseEntity } from './base-entity';
import { Workspace } from './workspace.entity';

@Index('workspace_custom_menu_item_pkey', ['id'], { unique: true })
@Entity('workspace_custom_menu_item', { schema: 'public' })
export class WorkspaceCustomMenuItem extends BaseEntity {
  @Column('character varying', { name: 'name', length: 100 })
  name: string;

  @Column('character varying', { name: 'icon', length: 100 })
  icon: string;

  @Column('character varying', { name: 'url', length: 500, nullable: true })
  url: string | null;

  @Column('uuid', { name: 'workspace_id' })
  workspaceId: string;

  @ManyToOne(() => Workspace, (workspace) => workspace.customMenuItems)
  @JoinColumn([{ name: 'workspace_id', referencedColumnName: 'id' }])
  workspace: Workspace;
}
