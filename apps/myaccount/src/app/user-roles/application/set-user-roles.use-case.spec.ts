import { UserRolesRepository } from '../domain/user-roles.repository.interface';
import { RolesBuilderFactory } from './roles-builders.factory';
import { SetUserRolesUseCase } from './set-user-roles.use-case';
import { SetUserApplicationsRolesDto } from '../presentation/dtos/set-user-applications-roles.dto';
import { ApplicationRolesBuilder } from '../domain/roles-builders/application-roles.builder';
import { Logger } from '@nestjs/common';
import { UserRolesNotFoundException, UserRolesUserNotFoundException } from '../domain/exceptions/user-roles.exceptions';
import { UserNotificationService } from '../../users-creator/services/user-notification.service';
import { PasswordService } from '../../users-creator/services/password.service';
import { ConfigService } from '@nestjs/config';

const DEFAULT_DTO: SetUserApplicationsRolesDto[] = [
  { applicationId: 'app-id-1', roles: ['role-id-1', 'role-id-2'] },
  { applicationId: 'app-id-2', roles: ['role-id-3'] },
];

const PERSISTENT_ROLES_ID = ['keeps_admin_role_id'];

describe('SetUserRolesUseCase', () => {
  let useCase: SetUserRolesUseCase;
  let userRolesRepositoryMock: jest.Mocked<UserRolesRepository>;
  let rolesBuilderFactoryMock: jest.Mocked<RolesBuilderFactory>;
  let defaultRoleBuilderMock: jest.Mocked<ApplicationRolesBuilder>;
  let userNotificationServiceMock: jest.Mocked<UserNotificationService>;
  let passwordServiceMock: jest.Mocked<PasswordService>;
  let configServiceMock: jest.Mocked<ConfigService>;

  beforeEach(() => {
    userRolesRepositoryMock = {
      getUserRoles: jest.fn(),
      overrideUserRoles: jest.fn(),
      deleteUserRoles: jest.fn(),
      findUserById: jest.fn().mockResolvedValue({ userId: 'user-id' }),
      getAllUserRoles: jest.fn(),
      findApplicationRolesById: jest.fn().mockImplementation((roleIds: string[]) => {
        return Promise.resolve(roleIds.map((id) => ({ id })));
      }),
    } as unknown as jest.Mocked<UserRolesRepository>;

    defaultRoleBuilderMock = {
      buildRoles: jest.fn().mockImplementation((roleIds: string[]) => roleIds),
    } as unknown as jest.Mocked<ApplicationRolesBuilder>;

    rolesBuilderFactoryMock = {
      get: jest.fn().mockReturnValue(defaultRoleBuilderMock),
    } as unknown as jest.Mocked<RolesBuilderFactory>;

    userNotificationServiceMock = {
      notifyUserWithRoles: jest.fn(),
    } as unknown as jest.Mocked<UserNotificationService>;

    passwordServiceMock = {
      generatePassword: jest.fn().mockReturnValue('123456'),
      updatePassword: jest.fn(),
    } as unknown as jest.Mocked<PasswordService>;

    configServiceMock = {
      get: jest.fn().mockImplementation((key: string) => {
        if (key === 'MYACCOUNT_ID') return 'myaccount-app-id';
        if (key === 'SMARTZAP_DEFAULT_ROLE') return 'smartzap-default-role-id';
        return undefined;
      }),
    } as unknown as jest.Mocked<ConfigService>;

    jest.spyOn(Logger.prototype, 'debug').mockImplementation(() => undefined);
    jest.spyOn(Logger.prototype, 'warn').mockImplementation(() => undefined);

    useCase = new SetUserRolesUseCase(
      userRolesRepositoryMock,
      rolesBuilderFactoryMock,
      PERSISTENT_ROLES_ID,
      userNotificationServiceMock,
      passwordServiceMock,
      configServiceMock,
    );
  });

  it('should do nothing if rolesDTOs is empty', async () => {
    await useCase.execute('user-id', 'workspace-id', []);
    expect(userRolesRepositoryMock.overrideUserRoles).not.toHaveBeenCalled();
  });

  it('should throw if user not found', async () => {
    userRolesRepositoryMock.findUserById.mockResolvedValue(null);
    await expect(useCase.execute('user-id', 'workspace-id', DEFAULT_DTO)).rejects.toThrow(
      UserRolesUserNotFoundException,
    );
  });

  it('should throw if some roles are not found', async () => {
    const invalidDTO: SetUserApplicationsRolesDto[] = [{ applicationId: 'app-id-1', roles: ['invalid-role'] }];
    userRolesRepositoryMock.findApplicationRolesById.mockResolvedValue([]);
    await expect(useCase.execute('user-id', 'workspace-id', invalidDTO)).rejects.toThrow(UserRolesNotFoundException);
  });

  it('should override roles correctly', async () => {
    userRolesRepositoryMock.getUserRoles.mockResolvedValue([]);
    userRolesRepositoryMock.getAllUserRoles.mockResolvedValue([]);

    await useCase.execute('user-id', 'workspace-id', DEFAULT_DTO);

    expect(userRolesRepositoryMock.overrideUserRoles).toHaveBeenCalledTimes(1);
    expect(userRolesRepositoryMock.overrideUserRoles).toHaveBeenCalledWith(
      'user-id',
      'workspace-id',
      expect.any(Map),
      PERSISTENT_ROLES_ID,
    );
  });

  describe('notification and password logic', () => {
    const userId = 'user-id';
    const workspaceId = 'workspace-id';
    const rolesDTOs: SetUserApplicationsRolesDto[] = [{ applicationId: 'other-app-id', roles: ['role-id-123'] }];

    const mockMyAccountRole = {
      id: 'role-myaccount',
      workspaceId,
      userId,
      status: true,
      roleId: 'myaccount-role-id',
      role: { id: 'myaccount-role-id', application: { id: 'myaccount-app-id' } },
    } as any;

    const mockOtherAppRole = {
      id: 'role-other',
      workspaceId,
      userId,
      status: true,
      roleId: 'role-id-123',
      role: { id: 'role-id-123', application: { id: 'other-app-id' } },
    } as any;

    it('should reset password if a new user is created with roles', async () => {
      const newUserRole = {
        id: 'role-new',
        workspaceId,
        userId,
        status: true,
        roleId: 'role-id-new',
        role: {
          id: 'role-id-new',
          application: { id: 'new-app-id' },
        },
      } as any;

      userRolesRepositoryMock.getAllUserRoles.mockResolvedValue([]); // No roles in other workspaces
      userRolesRepositoryMock.getUserRoles.mockResolvedValueOnce([]);
      userRolesRepositoryMock.getUserRoles.mockResolvedValueOnce([newUserRole]);

      const rolesDTOs: SetUserApplicationsRolesDto[] = [{ applicationId: 'new-app-id', roles: ['role-id-new'] }];

      await useCase.execute(userId, workspaceId, rolesDTOs);

      expect(passwordServiceMock.generatePassword).toHaveBeenCalledTimes(1);
      expect(passwordServiceMock.updatePassword).toHaveBeenCalledWith(userId, '123456', true); // Ensure it triggers the password update
      expect(userNotificationServiceMock.notifyUserWithRoles).toHaveBeenCalledWith([newUserRole], '123456', true); // Ensure notification is triggered
    });

    it('should NOT reset password if only promoted in same application', async () => {
      const oldRole = {
        id: 'role-1',
        workspaceId,
        userId,
        status: true,
        roleId: 'role-id-1',
        role: { id: 'role-id-1', application: { id: 'app-id-1' } },
      } as any;

      const promotedRole = {
        id: 'role-2',
        workspaceId,
        userId,
        status: true,
        roleId: 'role-id-2',
        role: { id: 'role-id-2', application: { id: 'app-id-1' } },
      } as any;

      userRolesRepositoryMock.getUserRoles.mockResolvedValue([oldRole, promotedRole]); // Same application role promotion

      await useCase.execute(userId, workspaceId, DEFAULT_DTO);

      expect(passwordServiceMock.generatePassword).not.toHaveBeenCalled();
      expect(passwordServiceMock.updatePassword).not.toHaveBeenCalled();
      expect(userNotificationServiceMock.notifyUserWithRoles).not.toHaveBeenCalled();
    });

    it('should NOT reset password if user has a role in another workspace', async () => {
      userRolesRepositoryMock.getAllUserRoles.mockResolvedValue([mockOtherAppRole]); // Role in another workspace
      userRolesRepositoryMock.getUserRoles.mockResolvedValue([mockOtherAppRole]);

      await useCase.execute(userId, workspaceId, rolesDTOs);

      expect(passwordServiceMock.generatePassword).not.toHaveBeenCalled();
      expect(passwordServiceMock.updatePassword).not.toHaveBeenCalled();
    });

    it('should not send notification if there are no new applications', async () => {
      userRolesRepositoryMock.getAllUserRoles.mockResolvedValue([]);
      userRolesRepositoryMock.getUserRoles.mockResolvedValue([]);

      await useCase.execute(userId, workspaceId, rolesDTOs);

      expect(userNotificationServiceMock.notifyUserWithRoles).not.toHaveBeenCalled();
    });

    it('should catch and log error if notification fails', async () => {
      userRolesRepositoryMock.getAllUserRoles.mockResolvedValue([mockMyAccountRole]);
      userRolesRepositoryMock.getUserRoles.mockResolvedValue([mockOtherAppRole]);

      userNotificationServiceMock.notifyUserWithRoles.mockRejectedValueOnce(new Error('notify failed'));

      await expect(useCase.execute(userId, workspaceId, rolesDTOs)).resolves.not.toThrow();
    });

    it('should catch and log error if password reset fails', async () => {
      userRolesRepositoryMock.getAllUserRoles.mockResolvedValue([mockMyAccountRole]);
      userRolesRepositoryMock.getUserRoles.mockResolvedValue([mockOtherAppRole]);

      passwordServiceMock.updatePassword.mockRejectedValueOnce(new Error('update failed'));

      await expect(useCase.execute(userId, workspaceId, rolesDTOs)).resolves.not.toThrow();
    });
  });
});
