import { Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { UserRolesRepository } from '../domain/user-roles.repository.interface';
import { SetUserApplicationsRolesDto } from '../presentation/dtos/set-user-applications-roles.dto';
import { ROLES_BUILDERS_FACTORY, RolesBuilderFactory } from './roles-builders.factory';
import { UserRolesNotFoundException, UserRolesUserNotFoundException } from '../domain/exceptions/user-roles.exceptions';
import { PERSISTENT_ROLES_IDS } from './persistent-roles-tokens';
import { UserNotificationService } from '../../users-creator/services/user-notification.service';
import { PasswordService } from '../../users-creator/services/password.service';
import { UserRoleWorkspace } from '../../entities/user-role-workspace.entity';

@Injectable()
export class SetUserRolesUseCase {
  private readonly logger = new Logger(SetUserRolesUseCase.name);

  constructor(
    private readonly userRolesRepository: UserRolesRepository,
    @Inject(ROLES_BUILDERS_FACTORY) private readonly rolesBuilderFactory: RolesBuilderFactory,
    @Inject(PERSISTENT_ROLES_IDS) private readonly persistentRoleIds: string[],
    private readonly userNotificationService: UserNotificationService,
    private readonly passwordService: PasswordService,
    private readonly configService: ConfigService,
  ) {}

  async execute(userId: string, workspaceId: string, rolesDTOs: SetUserApplicationsRolesDto[]) {
    if (rolesDTOs.length === 0) return;

    await this.ensureUserExists(userId);
    await this.ensureRolesExist(rolesDTOs);

    const previousRolesByApp = await this.getPreviousRoleIdsByApp(userId, workspaceId);
    const rolesByApplication = this.buildRolesByApplication(rolesDTOs);

    await this.userRolesRepository.overrideUserRoles(userId, workspaceId, rolesByApplication, this.persistentRoleIds);

    try {
      await this.handlePostRoleAssignment(userId, workspaceId, previousRolesByApp);
    } catch (error) {
      this.logger.error('Failed to handle post role assignment:', error);
    }
  }

  private async ensureUserExists(userId: string) {
    const user = await this.userRolesRepository.findUserById(userId);
    if (!user) {
      this.logger.warn(`Attempt to define roles for non-existent user: ${userId}`);
      throw new UserRolesUserNotFoundException(userId);
    }
    return user;
  }

  private async ensureRolesExist(rolesDTOs: SetUserApplicationsRolesDto[]) {
    const rolesIds = rolesDTOs.flatMap((dto) => dto.roles);
    const applicationIds = rolesDTOs.map((dto) => dto.applicationId);
    const existingRoles = await this.userRolesRepository.findApplicationRolesById(rolesIds, applicationIds);

    const notFoundRoles = rolesIds.filter((id) => !existingRoles?.some((role) => role.id === id));
    if (notFoundRoles.length > 0) {
      this.logger.warn(`Attempt to define invalid roles for user`, notFoundRoles);
      throw new UserRolesNotFoundException(notFoundRoles);
    }
  }

  private async getPreviousRoleIdsByApp(userId: string, workspaceId: string): Promise<Map<string, Set<string>>> {
    const previousUserRoles = await this.userRolesRepository.getUserRoles(userId, workspaceId);
    const map = new Map<string, Set<string>>();

    for (const role of previousUserRoles) {
      const appId = role.role.application.id;
      if (!map.has(appId)) map.set(appId, new Set());
      map.get(appId)?.add(role.role.id);
    }

    return map;
  }

  private buildRolesByApplication(rolesDTOs: SetUserApplicationsRolesDto[]): Map<string, string[]> {
    const map = new Map<string, string[]>();

    for (const dto of rolesDTOs) {
      const builder = this.rolesBuilderFactory.get(dto.applicationId);
      const roles = builder.buildRoles(dto.roles);
      map.set(dto.applicationId, roles);
    }

    return map;
  }

  private async handlePostRoleAssignment(
    userId: string,
    workspaceId: string,
    previousRolesByApp: Map<string, Set<string>>,
  ) {
    const currentRoles = await this.userRolesRepository.getUserRoles(userId, workspaceId);

    const newRoles = currentRoles.filter((role) => {
      const appId = role.role.application.id;
      return !previousRolesByApp.has(appId);
    });

    if (newRoles.length === 0) return;

    const isNewUserOrRecreatedOne = await this.isNewUserOrRecreatedOne(userId, newRoles);
    const tempPassword = isNewUserOrRecreatedOne ? this.passwordService.generatePassword() : null;

    if (tempPassword) {
      await this.passwordService.updatePassword(userId, tempPassword, true);
    }

    await this.userNotificationService.notifyUserWithRoles(newRoles, tempPassword, isNewUserOrRecreatedOne);
  }

  private async isNewUserOrRecreatedOne(userId: string, newUserRoles: UserRoleWorkspace[]): Promise<boolean> {
    const myAccountAccountAdminRoleId = this.configService.get('MYACCOUNT_ACCOUNT_ADMIN_ROLE_ID');
    const smartzapDefaultRoleId = this.configService.get('SMARTZAP_DEFAULT_ROLE');

    const allUserRoles = await this.userRolesRepository.getAllUserRoles(userId);
    const oldUserRoles = allUserRoles.filter((role) => !newUserRoles.some((newRole) => newRole.id === role.id));

    const hasRelevantRoleInAnotherWorkspace = oldUserRoles.some(
      (role) => role.status && role.role.id !== smartzapDefaultRoleId && role.role.id !== myAccountAccountAdminRoleId,
    );

    if (hasRelevantRoleInAnotherWorkspace) return false;

    const hasNewRelevantRole = newUserRoles.some(
      (role) => role.role.id !== myAccountAccountAdminRoleId && role.role.id !== smartzapDefaultRoleId,
    );

    return hasNewRelevantRole;
  }
}
