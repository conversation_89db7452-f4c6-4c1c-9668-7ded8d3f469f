import { registerAs } from '@nestjs/config';
import { KeycloakConnectConfig, PolicyEnforcementMode, TokenValidation } from 'nest-keycloak-connect';

export default registerAs<KeycloakConnectConfig>('auth', () => ({
  authServerUrl: process.env.AUTH_URL,
  serverUrl: process.env.SERVER_URL || process.env.AUTH_URL,
  realm: process.env.AUTH_REALM,
  resource: process.env.AUTH_CLIENT_ID,
  secret: process.env.AUTH_CLIENT_SECRET,
  policyEnforcement: PolicyEnforcementMode.PERMISSIVE,
  tokenValidation: TokenValidation.OFFLINE,
  verifyTokenAudience: process.env.AUTH_VERIFY_TOKEN_AUDIENCE === 'true',
  useNestLogger: false,
}));
