import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { KeycloakModule, NotificationModule, MobileNotificationsModule } from '@keeps-node-apis/@core';
import { ConfigModule } from '@nestjs/config';

import { Workspace } from '../entities/workspace.entity';
import { InvitationDomainService } from './domain/services/invitation.domain-service';
import { ResendInvitationUseCase } from './application/resend-invitation.use-case';
import { InvitationNotifier } from './infrastructure/services/invitation-notifier.service';
import { UsersModule } from '../users/users.module';
import { NotificationsController } from './presentation/controllers/resend-invitation.controller';
import { InvitationGrpcController } from './presentation/controllers/resend-invitation-grpc.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([Workspace]),
    ConfigModule,
    KeycloakModule,
    NotificationModule,
    MobileNotificationsModule,
    UsersModule,
  ],
  providers: [InvitationDomainService, ResendInvitationUseCase, InvitationNotifier],
  controllers: [NotificationsController, InvitationGrpcController],
  exports: [ResendInvitationUseCase],
})
export class InvitationsModule {}
