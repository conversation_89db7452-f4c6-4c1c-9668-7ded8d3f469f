import { NotificationDto, NotificationService, MobileNotificationStrategy } from '@keeps-node-apis/@core';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { default as AppConfig } from '../../../config/modules/app.config';
import { ConfigType } from '@nestjs/config';
import { User } from '../../../entities/user.entity';
import { Workspace } from '../../../entities/workspace.entity';

@Injectable()
export class InvitationNotifier {
  private logger = new Logger(InvitationNotifier.name);

  constructor(
    private readonly notificationService: NotificationService,
    private readonly mobileNotificationStrategy: MobileNotificationStrategy,
    @Inject(AppConfig.KEY) private readonly config: ConfigType<typeof AppConfig>,
  ) {}

  async sendInvitation(user: User, workspace: Workspace, password: string, language: string) {
    const templateData = {
      company_id: workspace.id,
      company_logo: workspace.logoUrl,
      company: workspace.name,
      user_email: user.email,
      user_login: user.email,
      user_name: user.name,
      user_phone: user.phone,
      user_password: password,
      app_web_link: this.config.applicationsWebUlrs.myaccount,
    };

    const notification: NotificationDto = {
      subject: 'konquest_email_subject',
      template: 'myaccount_resend_invite.html',
      workspaceId: workspace.id,
      templateData,
      language,
    };

    await this.notificationService.notifyViaEmail(notification, user.email);

    if (user.phone) {
      await this.sendMobileNotification(user, workspace, password, language);
    } else {
      this.logger.log('Skipping mobile notification: User has no phone number');
    }
  }

  private async sendMobileNotification(
    user: User,
    workspace: Workspace,
    password: string,
    language: string,
  ): Promise<void> {
    try {
      const contentData: Record<string, string> = {
        '1': workspace.name,
        '2': user.email,
        '3': password,
        '4': this.config.applicationsWebUlrs.myaccount,
      };

      await this.mobileNotificationStrategy.sendNotification(
        'MYACCOUNT_KONQUEST_ONBOARDING',
        user.phone,
        contentData,
        language === 'pt-br' ? 'pt-BR' : language,
        workspace.id,
      );

      this.logger.log(`Mobile notification sent to user with phone: ${user.phone}`);
    } catch (error) {
      this.logger.error(`Failed to send mobile notification: ${error.message}`);
    }
  }
}
