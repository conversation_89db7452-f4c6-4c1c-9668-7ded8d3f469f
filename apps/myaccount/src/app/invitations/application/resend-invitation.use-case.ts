import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { InvitationDomainService } from '../domain/services/invitation.domain-service';
import { KeycloakRepository } from '@keeps-node-apis/@core';
import { UsersRepository } from '../../users/domain/repositories/users.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { Workspace } from '../../entities/workspace.entity';
import { ResendInvitationCommand } from '../domain/commands/resend-invitation.command';
import { InvitationNotifier } from '../infrastructure/services/invitation-notifier.service';
import { UserNotFoundException } from '../domain/exceptions/invitations.exceptions';
import { User } from '../../entities/user.entity';

@Injectable()
export class ResendInvitationUseCase {
  constructor(
    private readonly domainService: InvitationDomainService,
    private readonly invitationNotifier: InvitationNotifier,
    private readonly userRepository: UsersRepository,
    private readonly keycloakRepository: KeycloakRepository,
    @InjectRepository(Workspace) private readonly workspaceRepository: Repository<Workspace>,
  ) {}

  async execute(command: ResendInvitationCommand): Promise<void> {
    let user: User;

    try {
      user = await this.userRepository.findById(command.userId, command.workspaceId);
    } catch (error) {
      // The UsersRepository does not throw a domain-specific exception,
      // and importing an infrastructure-level error would break the domain boundary.
      // Therefore, we handle it generically and raise a domain-level exception instead.
      throw new UserNotFoundException(command.userId);
    }

    const workspace = await this.workspaceRepository.findOneByOrFail({ id: command.workspaceId });

    const password = this.domainService.generateTemporaryPassword();
    const language = this.domainService.resolveLanguage(user.language?.name);

    await this.keycloakRepository.resetUserPassword(user.id, password, true);
    await this.invitationNotifier.sendInvitation(user, workspace, password, language);
  }
}
