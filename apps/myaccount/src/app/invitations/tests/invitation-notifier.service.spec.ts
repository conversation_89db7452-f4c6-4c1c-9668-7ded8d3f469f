import { NotificationService, NotificationDto, MobileNotificationStrategy } from '@keeps-node-apis/@core';
import { ConfigType } from '@nestjs/config';
import Chance from 'chance';
import appConfig from '../../config/modules/app.config';
import { InvitationNotifier } from '../infrastructure/services/invitation-notifier.service';
import { User } from '../../entities/user.entity';
import { Workspace } from '../../entities/workspace.entity';

const chance = new Chance();

describe('InvitationNotifier', () => {
  let notifier: InvitationNotifier;
  let notificationService: NotificationService;
  let mobileNotificationStrategy: MobileNotificationStrategy;
  let mockConfig: ConfigType<typeof appConfig>;

  beforeEach(() => {
    notificationService = {
      notifyViaEmail: jest.fn(),
    } as any;

    mobileNotificationStrategy = {
      sendNotification: jest.fn(),
    } as any;

    mockConfig = {
      applicationsWebUlrs: {
        myaccount: chance.url(),
      },
    } as any;

    notifier = new InvitationNotifier(notificationService, mobileNotificationStrategy, mockConfig);
  });

  it('should call notifyViaEmail with correct NotificationDto', async () => {
    const user = {
      id: chance.guid(),
      email: chance.email(),
      name: chance.name(),
      phone: chance.phone(),
    } as User;

    const workspace = {
      id: chance.guid(),
      name: chance.company(),
      logoUrl: chance.url(),
    } as Workspace;

    const password = chance.string({ length: 8 });
    const language = 'pt-br';

    await notifier.sendInvitation(user, workspace, password, language);

    const expectedTemplateData = {
      company_id: workspace.id,
      company_logo: workspace.logoUrl,
      company: workspace.name,
      user_email: user.email,
      user_login: user.email,
      user_name: user.name,
      user_phone: user.phone,
      user_password: password,
      app_web_link: mockConfig.applicationsWebUlrs.myaccount,
    };

    const expectedNotification: NotificationDto = {
      subject: 'konquest_email_subject',
      template: 'myaccount_resend_invite.html',
      workspaceId: workspace.id,
      templateData: expectedTemplateData,
      language,
    };

    expect(notificationService.notifyViaEmail).toHaveBeenCalledWith(expectedNotification, user.email);
  });

  it('should send WhatsApp notification when user has phone', async () => {
    const user = {
      id: chance.guid(),
      email: chance.email(),
      name: chance.name(),
      phone: chance.phone(),
    } as User;

    const workspace = {
      id: chance.guid(),
      name: chance.company(),
      logoUrl: chance.url(),
    } as Workspace;

    const password = chance.string({ length: 8 });
    const language = 'pt-br';

    await notifier.sendInvitation(user, workspace, password, language);

    const expectedContentData = {
      '1': workspace.name,
      '2': user.email,
      '3': password,
      '4': mockConfig.applicationsWebUlrs.myaccount,
    };

    expect(mobileNotificationStrategy.sendNotification).toHaveBeenCalledWith(
      'MYACCOUNT_KONQUEST_ONBOARDING',
      user.phone,
      expectedContentData,
      'pt-BR',
      workspace.id,
    );
  });

  it('should not send WhatsApp notification when user has no phone', async () => {
    const user = {
      id: chance.guid(),
      email: chance.email(),
      name: chance.name(),
      phone: null,
    } as User;

    const workspace = {
      id: chance.guid(),
      name: chance.company(),
      logoUrl: chance.url(),
    } as Workspace;

    const password = chance.string({ length: 8 });
    const language = 'pt-br';

    await notifier.sendInvitation(user, workspace, password, language);

    expect(mobileNotificationStrategy.sendNotification).not.toHaveBeenCalled();
  });

  it('should handle WhatsApp notification errors gracefully', async () => {
    const user = {
      id: chance.guid(),
      email: chance.email(),
      name: chance.name(),
      phone: chance.phone(),
    } as User;

    const workspace = {
      id: chance.guid(),
      name: chance.company(),
      logoUrl: chance.url(),
    } as Workspace;

    const password = chance.string({ length: 8 });
    const language = 'pt-br';

    const error = new Error('WhatsApp service unavailable');
    (mobileNotificationStrategy.sendNotification as jest.Mock).mockRejectedValue(error);

    await expect(notifier.sendInvitation(user, workspace, password, language)).resolves.not.toThrow();

    expect(mobileNotificationStrategy.sendNotification).toHaveBeenCalled();
    expect(notificationService.notifyViaEmail).toHaveBeenCalled();
  });
});
