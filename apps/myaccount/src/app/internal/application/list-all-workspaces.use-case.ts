import { Injectable } from '@nestjs/common';
import { Workspace } from '../../entities/workspace.entity';
import { PaginateQuery } from 'nestjs-paginate';
import { WorkspacesRepository } from '../../workspaces/repositories/workspaces-repository';

@Injectable()
export class ListAllWorkspacesUseCase {
  constructor(private workspacesRepository: WorkspacesRepository) {}

  async execute(input: Input): Promise<Workspace[]> {
    const result = await this.workspacesRepository.findAll(input.query);
    return result.data;
  }
}

class Input {
  query: PaginateQuery;
}
