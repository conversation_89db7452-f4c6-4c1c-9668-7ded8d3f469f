import { ListAllWorkspacesUseCase } from './list-all-workspaces.use-case';
import { WorkspacesRepository } from '../../workspaces/repositories/workspaces-repository';
import { PaginateQuery } from 'nestjs-paginate';

type Workspace = {
  id: string;
  name: string;
};

type Paginated<T> = {
  data: T[];
  meta: any;
  links: any;
};

describe('ListAllWorkspacesUseCase', () => {
  let useCase: ListAllWorkspacesUseCase;
  let repo: { findAll: jest.Mock };

  const makePaginated = <T>(data: T[]): Paginated<T> => ({
    data,
    meta: {
      itemsPerPage: data.length,
      totalItems: data.length,
      currentPage: 1,
      totalPages: 1,
      sortBy: [],
      searchBy: [],
      search: '',
      select: [],
    },
    links: {
      current: '/workspaces?page=1&limit=10',
    },
  });

  beforeEach(() => {
    repo = {
      findAll: jest.fn(),
    };

    useCase = new ListAllWorkspacesUseCase(repo as unknown as WorkspacesRepository);
  });

  it('should call repository.findAll with the provided query and return data', async () => {
    const query: PaginateQuery = {
      page: 1,
      limit: 10,
      path: '/workspaces',
    } as any;

    const workspaces: Workspace[] = [
      { id: 'w1', name: 'Workspace A' },
      { id: 'w2', name: 'Workspace B' },
    ];

    repo.findAll.mockResolvedValueOnce(makePaginated(workspaces));

    const result = await useCase.execute({ query } as any);

    expect(repo.findAll).toHaveBeenCalledTimes(1);
    expect(repo.findAll).toHaveBeenCalledWith(query);
    expect(result).toEqual(workspaces);
  });

  it('should return an empty array when repository returns no data', async () => {
    const query: PaginateQuery = { page: 1, limit: 10, path: '/workspaces' } as any;

    repo.findAll.mockResolvedValueOnce(makePaginated<Workspace>([]));

    const result = await useCase.execute({ query } as any);

    expect(result).toEqual([]);
  });

  it('should propagate errors thrown by the repository', async () => {
    const query: PaginateQuery = { page: 1, limit: 10, path: '/workspaces' } as any;
    const error = new Error('DB down');

    repo.findAll.mockRejectedValueOnce(error);

    await expect(useCase.execute({ query } as any)).rejects.toThrow(error);
  });

  it('should not post-process the returned data (identity check)', async () => {
    const query: PaginateQuery = { page: 1, limit: 10, path: '/workspaces' } as any;
    const workspaces: Workspace[] = [{ id: 'w1', name: 'Only One' }];

    const paginated = makePaginated(workspaces);
    repo.findAll.mockResolvedValueOnce(paginated);

    const result = await useCase.execute({ query } as any);

    expect(result).toBe(workspaces);
  });
});
