import { Controller, UseInterceptors } from '@nestjs/common';
import { <PERSON>rpc<PERSON>ethod } from '@nestjs/microservices';
import { GrpcFiltersInterceptor } from '@keeps-node-apis/@core';
import { Paginate, PaginateQuery } from 'nestjs-paginate';
import { ListAllWorkspacesUseCase } from '../../application/list-all-workspaces.use-case';
import { Workspace } from '../../../entities/workspace.entity';

@Controller()
@UseInterceptors(GrpcFiltersInterceptor)
export class WorkspacesGrpcController {
  constructor(private readonly listAllWorkspacesUseCase: ListAllWorkspacesUseCase) {}

  @GrpcMethod('WorkspacesService', 'Get')
  async listAllWorkspaces(@Paginate() query: PaginateQuery) {
    const result = await this.listAllWorkspacesUseCase.execute({
      query,
    });
    console.log(result);
    return this.toGrpcWorkspaceListResponse(result);
  }

  toGrpcWorkspaceListResponse(workspaces: Workspace[]) {
    return {
      items: workspaces.map((workspace) => ({
        id: workspace.id,
        name: workspace.name,
        description: workspace.description ?? '',
        iconUrl: workspace.iconUrl ?? '',
        logoUrl: workspace.logoUrl ?? '',
        themeId: workspace.themeId ?? '',
        companyId: workspace.companyId,
        status: workspace.status,
        hashId: workspace.hashId ?? '',
      })),
    };
  }
}
