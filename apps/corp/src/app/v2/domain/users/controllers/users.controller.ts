import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, ParseUUIDPipe, Post, Query } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { UsersService } from '../services/users.service';
import { KONQUEST_ADMIN_ROLES, MYACCOUNT_ADMIN_ROLES, Roles, Serialize } from '@keeps-node-apis/@core';
import { UsersListResponseDto } from '../dtos/users-list-response.dto';
import { UserListParamsDto } from '../dtos/user-list-params.dto';
import { BatchUserCreateDto } from '../dtos/batch-user-create.dto';
import { UserRolesService } from '../services/user-roles.service';

@ApiTags('Users')
@Roles([...MYACCOUNT_ADMIN_ROLES, ...KONQUEST_ADMIN_ROLES])
@Controller({ path: 'users', version: '2' })
export class UsersController {
  constructor(
    private readonly usersService: UsersService,
    private readonly userRolesService: UserRolesService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'List users' })
  @ApiResponse({ type: UsersListResponseDto, status: HttpStatus.OK, description: 'List of users' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid credentials or missing fields' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  @Serialize(UsersListResponseDto)
  list(@Query() params: UserListParamsDto) {
    return this.usersService.list(params);
  }

  @Post('batch-create')
  @HttpCode(HttpStatus.ACCEPTED)
  @ApiOperation({ summary: 'Create new users in batch' })
  @ApiResponse({ status: HttpStatus.ACCEPTED, description: 'The request was accepted and will be processed' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid credentials or missing fields' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  @ApiBody({ type: BatchUserCreateDto, description: 'List of users and their roles' })
  batchCreate(@Body() batchUserCreateDto: BatchUserCreateDto) {
    return this.usersService.batchCreate(batchUserCreateDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiParam({
    name: 'id',
    type: String,
    description: 'The user UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiOperation({ summary: 'Remove a user from the workspace' })
  @ApiResponse({ status: HttpStatus.NO_CONTENT, description: 'The user was removed from the workspace' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid credentials or missing fields' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  deleteUser(@Param('id', ParseUUIDPipe) id: string) {
    return this.userRolesService.removeUserFromWorkspace(id);
  }
}
