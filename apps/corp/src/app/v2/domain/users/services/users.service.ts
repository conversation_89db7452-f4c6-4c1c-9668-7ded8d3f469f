import { Injectable } from '@nestjs/common';
import { CorpHttpClient } from '../../../infra';
import { ConfigService } from '@nestjs/config';
import { UserListParamsDto, UserListSortOrder } from '../dtos/user-list-params.dto';
import { UsersListResponseDto } from '../dtos/users-list-response.dto';
import { MyAccountUsersListResponseDto } from '../dtos/my-account-users-list.dto';
import { BatchUserCreateDto, BatchUserCreateRoles } from '../dtos/batch-user-create.dto';

const USERS_LIST_SELECTION = 'id,name,email,phone,avatar,status,createdDate';

@Injectable()
export class UsersService {
  private readonly basePath: string;
  private readonly MY_ACCOUNT_USER_ROLE_ID: string;
  private readonly MY_ACCOUNT_ADMIN_ROLE_ID: string;
  private readonly KONQUEST_USER_ROLE_ID: string;
  private readonly KONQUEST_ADMIN_ROLE_ID: string;
  private readonly KONQUEST_SUPER_ADMIN_ROLE_ID: string;
  private readonly KONQUEST_CONTRIBUTOR_ROLE_ID: string;
  private readonly KONQUEST_INSTRUCTOR_ROLE_ID: string;
  private readonly KONQUEST_CURATOR_ROLE_ID: string;
  private readonly ANALYTICS_USER_ROLE_ID: string;
  private readonly ANALYTICS_ADMIN_ROLE_ID: string;
  private readonly ANALYTICS_LEADER_ROLE_ID: string;

  constructor(
    private readonly http: CorpHttpClient,
    readonly configService: ConfigService,
  ) {
    this.basePath = `${this.configService.get('MYACCOUNT_V2_URL')}/users`;
    this.MY_ACCOUNT_USER_ROLE_ID = this.configService.get('MY_ACCOUNT_USER_ROLE_ID');
    this.MY_ACCOUNT_ADMIN_ROLE_ID = this.configService.get('MY_ACCOUNT_ADMIN_ROLE_ID');
    this.KONQUEST_USER_ROLE_ID = this.configService.get('KONQUEST_USER_ROLE_ID');
    this.KONQUEST_ADMIN_ROLE_ID = this.configService.get('KONQUEST_ADMIN_ROLE_ID');
    this.KONQUEST_SUPER_ADMIN_ROLE_ID = this.configService.get('KONQUEST_SUPER_ADMIN_ROLE_ID');
    this.KONQUEST_CONTRIBUTOR_ROLE_ID = this.configService.get('KONQUEST_CONTRIBUTOR_ROLE_ID');
    this.KONQUEST_INSTRUCTOR_ROLE_ID = this.configService.get('KONQUEST_INSTRUCTOR_ROLE_ID');
    this.KONQUEST_CURATOR_ROLE_ID = this.configService.get('KONQUEST_CURATOR_ROLE_ID');
    this.ANALYTICS_USER_ROLE_ID = this.configService.get('ANALYTICS_USER_ROLE_ID');
    this.ANALYTICS_ADMIN_ROLE_ID = this.configService.get('ANALYTICS_ADMIN_ROLE_ID');
    this.ANALYTICS_LEADER_ROLE_ID = this.configService.get('ANALYTICS_LEADER_ROLE_ID');
  }

  async list(params: UserListParamsDto): Promise<UsersListResponseDto> {
    const queryParams = this.buildMyAccountListParams(params);
    const response = await this.http.get<MyAccountUsersListResponseDto>(this.basePath, queryParams);
    return this.buildListResult(response);
  }

  batchCreate(batchUserCreateDto: BatchUserCreateDto) {
    const body = { users: batchUserCreateDto.users, permissions: this.buildRolesList(batchUserCreateDto.roles) };
    // We deliberately ignore the result of this API call, as in the future, this API will work in the background.
    // noinspection JSIgnoredPromiseFromCall
    this.http.post(`${this.basePath}/batch-create`, body);
  }

  private buildMyAccountListParams(params: UserListParamsDto) {
    const queryParams: Record<string, string | number> = {};
    const order = params?.order || UserListSortOrder.DESC;
    const sort = params?.sort;

    queryParams.sortBy = sort ? `${sort}:${order.toUpperCase()}` : '';
    queryParams.select = USERS_LIST_SELECTION;

    queryParams.page = params?.page;
    queryParams.limit = params?.per_page;
    queryParams.search = params?.search;

    return queryParams;
  }

  private buildListResult(myAccountResponse: MyAccountUsersListResponseDto) {
    const { items_per_page, total_items, total_pages, current_page } = myAccountResponse.meta;
    return { items: myAccountResponse.data, meta: { items_per_page, total_items, total_pages, current_page } };
  }

  private buildRolesList(batchRoles: BatchUserCreateRoles) {
    const roles: string[] = [];

    if (!batchRoles) {
      return [];
    }

    // My account roles
    if (batchRoles.myAccountUser) {
      roles.push(this.MY_ACCOUNT_USER_ROLE_ID);
    }

    if (batchRoles.myAccountAdmin) {
      roles.push(this.MY_ACCOUNT_ADMIN_ROLE_ID);
    }

    // Konquest roles
    if (batchRoles.konquestUser) {
      roles.push(this.KONQUEST_USER_ROLE_ID);
    }

    if (batchRoles.konquestAdmin) {
      roles.push(this.KONQUEST_ADMIN_ROLE_ID);
    }

    if (batchRoles.konquestSuperAdmin) {
      roles.push(this.KONQUEST_SUPER_ADMIN_ROLE_ID);
    }

    if (batchRoles.konquestContributor) {
      roles.push(this.KONQUEST_CONTRIBUTOR_ROLE_ID);
    }

    if (batchRoles.konquestCurator) {
      roles.push(this.KONQUEST_CURATOR_ROLE_ID);
    }

    if (batchRoles.konquestInstructor) {
      roles.push(this.KONQUEST_INSTRUCTOR_ROLE_ID);
    }

    if (batchRoles.analytiscUser) {
      roles.push(this.ANALYTICS_USER_ROLE_ID);
    }

    if (batchRoles.analyticsAdmin) {
      roles.push(this.ANALYTICS_ADMIN_ROLE_ID);
    }

    if (batchRoles.analyticsLeader) {
      roles.push(this.ANALYTICS_LEADER_ROLE_ID);
    }

    return roles;
  }
}
