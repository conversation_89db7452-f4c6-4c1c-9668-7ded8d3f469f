import { CorpHttpClient } from '../../../infra';
import { ConfigService } from '@nestjs/config';
import { Injectable } from '@nestjs/common';

@Injectable()
export class UserRolesService {
  private readonly basePath: string;

  constructor(
    private readonly http: CorpHttpClient,
    readonly configService: ConfigService,
  ) {
    this.basePath = `${this.configService.get('MYACCOUNT_V2_URL')}/user-roles`;
  }

  removeUserFromWorkspace(userId: string) {
    return this.http.delete<void>(`${this.basePath}/${userId}`);
  }
}
