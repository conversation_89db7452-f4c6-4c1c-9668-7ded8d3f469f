import { Module } from '@nestjs/common';
import { UsersService } from './services/users.service';
import { UserRolesService } from './services/user-roles.service';
import { CorpHttpModule } from '../../infra';
import { UsersController } from './controllers/users.controller';

@Module({
  imports: [CorpHttpModule],
  providers: [UsersService, UserRolesService],
  controllers: [UsersController],
})
export class UsersModule {}
