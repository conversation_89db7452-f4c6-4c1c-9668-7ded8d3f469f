import { Injectable } from '@nestjs/common';
import { KonquestBaseService } from '../../shared/services/konquest-base-service';
import { TrailDto } from '../dtos/trail.dto';
import { TrailListParamsDto } from '../dtos/trail-list-params.dto';
import { KonquestResponseDto } from '../../shared/dtos/konquest-response.dto';
import { BaseListParamsDto } from '../../shared/dtos/base-list-params.dto';

@Injectable()
export class TrailsService extends KonquestBaseService {
  private readonly basePath = `${this.KONQUEST_URL}/learning-trails`;

  async list(params: TrailListParamsDto) {
    const queryParams = this.buildKonquestListParams(params);
    const trailsList = await this.http.get<KonquestResponseDto<TrailDto>>(this.basePath, queryParams);
    return this.buildListResult(trailsList, params);
  }

  getById(id: string) {
    return this.http.get<TrailDto>(`${this.basePath}/${id}`);
  }

  protected buildListResult<T>(
    result: Pick<KonquestResponseDto<T>, 'results' | 'count'>,
    params: BaseListParamsDto,
  ): {
    items: T[];
    meta: { items_per_page: number; current_page: number; total_items: number; total_pages: number };
  } {
    const { count, results } = result;
    return {
      items: results,
      meta: {
        items_per_page: params.per_page,
        current_page: params.page,
        total_items: count,
        total_pages: Math.ceil(count / params.per_page),
      },
    };
  }
}
