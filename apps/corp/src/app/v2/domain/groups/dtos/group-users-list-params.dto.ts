import { ApiPropertyOptional, IntersectionType } from '@nestjs/swagger';
import { BaseListParamsDto } from '../../shared/dtos/base-list-params.dto';
import { IsBoolean, IsOptional } from 'class-validator';
import { ToBoolean } from '@keeps-node-apis/@core';

export class GroupUsersListParamsDto extends IntersectionType(BaseListParamsDto) {
  @ApiPropertyOptional({ description: 'Whether to return only deleted users, if false, will exclude deleted users' })
  @IsBoolean()
  @IsOptional()
  @ToBoolean()
  deleted?: boolean;
}
