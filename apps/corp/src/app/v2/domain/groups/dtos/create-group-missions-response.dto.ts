import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

export class GroupMissionErrorDto {
  @ApiPropertyOptional({ description: 'The mission UUID' })
  @Expose()
  mission_id: string;

  @ApiPropertyOptional({ description: 'The error message' })
  @Expose()
  detail: string;
}

export class CreateGroupMissionsResponseDto {
  @ApiPropertyOptional({ description: 'List of errors', type: GroupMissionErrorDto, isArray: true })
  @Type(() => GroupMissionErrorDto)
  @Expose()
  errors: GroupMissionErrorDto[];
}
