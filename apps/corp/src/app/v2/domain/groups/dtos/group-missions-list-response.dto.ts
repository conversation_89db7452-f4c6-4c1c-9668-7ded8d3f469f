import { Expose, Type } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { PaginationMetaDto } from '../../shared/dtos/pagination-meta.dto';
import { BasicMissionDto } from '../../missions/dtos/mission.dto';

export class GroupMissionsListResponseDto {
  @Expose()
  @ApiPropertyOptional({ type: [BasicMissionDto], description: 'List of missions' })
  @Type(() => BasicMissionDto)
  items: BasicMissionDto[];

  @Expose()
  @ApiPropertyOptional({ type: PaginationMetaDto, description: 'Pagination meta data' })
  meta: PaginationMetaDto;
}
