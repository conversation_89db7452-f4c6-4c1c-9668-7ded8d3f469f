import { Expose, Type } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { PaginationMetaDto } from '../../shared/dtos/pagination-meta.dto';
import { BasicUserDto } from '../../users/dtos/user.dto';

export class GroupUsersListResponseDto {
  @Expose()
  @ApiPropertyOptional({ type: [BasicUserDto], description: 'List of users' })
  @Type(() => BasicUserDto)
  items: BasicUserDto[];

  @Expose()
  @ApiPropertyOptional({ type: PaginationMetaDto, description: 'Pagination meta data' })
  meta: PaginationMetaDto;
}
