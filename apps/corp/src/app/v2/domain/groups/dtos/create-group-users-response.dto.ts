import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

export class GroupUserErrorDto {
  @ApiPropertyOptional({ description: 'The user UUID' })
  @Expose()
  user_id: string;

  @ApiPropertyOptional({ description: 'The error message' })
  @Expose()
  detail: string;
}

export class CreateGroupUsersResponseDto {
  @ApiPropertyOptional({ description: 'List of errors', type: GroupUserErrorDto, isArray: true })
  @Type(() => GroupUserErrorDto)
  @Expose()
  errors: GroupUserErrorDto[];
}
