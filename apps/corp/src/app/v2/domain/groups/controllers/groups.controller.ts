import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseUUIDPipe,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import { KONQUEST_ADMIN_ROLES, MYACCOUNT_ADMIN_ROLES, Roles, Serialize } from '@keeps-node-apis/@core';
import { ApiBody, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { GroupsService } from '../services/groups.service';
import { GroupsListResponseDto } from '../dtos/groups-list-response.dto';
import { GroupsListParamsDto } from '../dtos/groups-list-params.dto';
import { GroupEnrollDto } from '../dtos/group-enroll.dto';
import { GroupEnrollResponseDto } from '../dtos/group-enroll-response.dto';
import { GroupDto } from '../dtos/group.dto';
import { GroupCreateDto } from '../dtos/group-create.dto';
import { GroupMissionsListResponseDto } from '../dtos/group-missions-list-response.dto';
import { GroupMissionsListParamsDto } from '../dtos/group-missions-list-params.dto';
import { CreateGroupMissionsDto } from '../dtos/create-group-missions.dto';
import { CreateGroupMissionsResponseDto } from '../dtos/create-group-missions-response.dto';
import { GroupUsersListParamsDto } from '../dtos/group-users-list-params.dto';
import { GroupUsersListResponseDto } from '../dtos/group-users-list-response.dto';
import { CreateGroupUsersDto } from '../dtos/create-group-users.dto';
import { CreateGroupUsersResponseDto } from '../dtos/create-group-users-response.dto';

@ApiTags('Groups')
@Roles([...MYACCOUNT_ADMIN_ROLES, ...KONQUEST_ADMIN_ROLES])
@Controller({ path: 'groups', version: '2' })
export class GroupsController {
  constructor(private readonly groupsService: GroupsService) {}

  @Get()
  @ApiOperation({ summary: 'List groups' })
  @ApiResponse({ type: GroupsListResponseDto, status: HttpStatus.OK, description: 'List of groups' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid credentials or missing fields' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  @Serialize(GroupsListResponseDto)
  list(@Query() params: GroupsListParamsDto) {
    return this.groupsService.list(params);
  }

  @Post('batch-enroll')
  @ApiOperation({
    summary: 'Add users to a group in batch',
    description: 'Add users the users to the group and enrolls them in the group courses and learning trails',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The result of the batch enroll operation',
    type: GroupEnrollResponseDto,
  })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid credentials or missing fields' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  @ApiBody({ type: GroupEnrollDto })
  @Serialize(GroupEnrollResponseDto)
  enrollUsers(@Body() groupEnrollDto: GroupEnrollDto) {
    return this.groupsService.batchEnroll(groupEnrollDto);
  }

  @Post()
  @ApiOperation({ summary: 'Create a group' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'The group that was created',
    type: GroupDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'A group with the same name already exists, invalid credentials or missing fields',
  })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  @ApiBody({ type: GroupCreateDto })
  @Serialize(GroupDto)
  create(@Body() groupCreateDto: GroupCreateDto) {
    return this.groupsService.create(groupCreateDto.name);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a group' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The group that was updated',
    type: GroupDto,
  })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid credentials or missing fields' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'The Group UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiBody({ type: GroupCreateDto })
  @Serialize(GroupDto)
  update(@Param('id', ParseUUIDPipe) id: string, @Body() groupUpdateDto: GroupCreateDto) {
    return this.groupsService.update(id, groupUpdateDto.name);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a group' })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'The Group UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({ status: HttpStatus.NO_CONTENT, description: 'The group was deleted' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid credentials or missing fields' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  delete(@Param('id', ParseUUIDPipe) id: string) {
    return this.groupsService.delete(id);
  }

  @Get(':id/missions')
  @ApiOperation({ summary: 'List the missions of a group' })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'The Group UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({ type: GroupMissionsListResponseDto, status: HttpStatus.OK, description: 'List of the group missions' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid credentials or missing fields' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  @Serialize(GroupMissionsListResponseDto)
  getMissions(@Param('id', ParseUUIDPipe) id: string, @Query() params: GroupMissionsListParamsDto) {
    return this.groupsService.getMissions(id, params);
  }

  @Post(':id/missions')
  @ApiOperation({ summary: 'Link missions to a group' })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'The Group UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @HttpCode(HttpStatus.CREATED)
  @ApiResponse({
    type: CreateGroupMissionsResponseDto,
    status: HttpStatus.CREATED,
    description: 'The missions were added to the group',
  })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid credentials or missing fields' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  @Serialize(CreateGroupMissionsResponseDto)
  addMissionToGroup(@Param('id', ParseUUIDPipe) id: string, @Body() body: CreateGroupMissionsDto) {
    return this.groupsService.addMissions(id, body);
  }

  @Delete(':id/missions/:mission_id')
  @ApiOperation({ summary: 'Remove a mission from a group' })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'The Group UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiParam({
    name: 'mission_id',
    type: String,
    description: 'The Mission UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiResponse({ status: HttpStatus.NO_CONTENT, description: 'The mission was removed from the group' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'The provided user or group was not found' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid credentials or missing fields' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  deleteMission(@Param('id', ParseUUIDPipe) id: string, @Param('mission_id', ParseUUIDPipe) missionId: string) {
    return this.groupsService.removeMission(id, missionId);
  }

  @Get(':id/users')
  @ApiOperation({ summary: 'List the users of a group' })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'The Group UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({ type: GroupUsersListResponseDto, status: HttpStatus.OK, description: 'List of the group users' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid credentials or missing fields' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  @Serialize(GroupUsersListResponseDto)
  getUsers(@Param('id', ParseUUIDPipe) id: string, @Query() params: GroupUsersListParamsDto) {
    return this.groupsService.getUsers(id, params);
  }

  @Post(':id/users')
  @ApiOperation({ summary: 'Add users to a group' })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'The Group UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @HttpCode(HttpStatus.CREATED)
  @ApiResponse({
    type: CreateGroupUsersResponseDto,
    status: HttpStatus.CREATED,
    description: 'The users were added to the group',
  })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid credentials or missing fields' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  @Serialize(CreateGroupUsersResponseDto)
  addUserToGroup(@Param('id', ParseUUIDPipe) id: string, @Body() body: CreateGroupUsersDto) {
    return this.groupsService.addUsers(id, body);
  }

  @Delete(':id/users/:user_id')
  @ApiOperation({ summary: 'Remove a user from a group' })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'The Group UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiParam({
    name: 'user_id',
    type: String,
    description: 'The User UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiResponse({ status: HttpStatus.NO_CONTENT, description: 'The user was removed from the group' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'The provided user or group was not found' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid credentials or missing fields' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  deleteUser(@Param('id', ParseUUIDPipe) id: string, @Param('user_id', ParseUUIDPipe) userId: string) {
    return this.groupsService.removeUser(id, userId);
  }
}
