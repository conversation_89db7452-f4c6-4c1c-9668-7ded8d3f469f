import { Injectable } from '@nestjs/common';
import { GroupsListParamsDto } from '../dtos/groups-list-params.dto';
import { KonquestResponseDto } from '../../shared/dtos/konquest-response.dto';
import { GroupDto } from '../dtos/group.dto';
import { GroupEnrollDto } from '../dtos/group-enroll.dto';
import { KonquestGroupEnrollResponseDto } from '../dtos/konquest-group-enroll-response.dto';
import { GroupEnrollErrorDto, GroupEnrollResponseDto } from '../dtos/group-enroll-response.dto';
import { KonquestBaseService } from '../../shared/services/konquest-base-service';
import { GroupMissionsListParamsDto } from '../dtos/group-missions-list-params.dto';
import { BasicMissionDto } from '../../missions/dtos/mission.dto';
import { CreateGroupMissionsDto } from '../dtos/create-group-missions.dto';
import { CreateGroupMissionsResponseDto, GroupMissionErrorDto } from '../dtos/create-group-missions-response.dto';
import { GroupUsersListParamsDto } from '../dtos/group-users-list-params.dto';
import { BasicUserDto } from '../../users/dtos/user.dto';
import { CreateGroupUsersDto } from '../dtos/create-group-users.dto';
import { CreateGroupUsersResponseDto, GroupUserErrorDto } from '../dtos/create-group-users-response.dto';

@Injectable()
export class GroupsService extends KonquestBaseService {
  private readonly basePath = `${this.KONQUEST_URL}/groups`;

  async list(params: GroupsListParamsDto) {
    const queryParams = this.buildKonquestListParams(params);
    const result = await this.http.get<KonquestResponseDto<GroupDto>>(this.basePath, queryParams);
    return this.buildListResult(result, params);
  }

  async batchEnroll(groupEnrollDto: GroupEnrollDto): Promise<GroupEnrollResponseDto> {
    const result = await this.http.post<KonquestGroupEnrollResponseDto>(
      `${this.basePath}/${groupEnrollDto.id}/users`,
      groupEnrollDto,
    );
    return this.buildEnrollResponse(result);
  }

  create(name: string): Promise<GroupDto> {
    return this.http.post<GroupDto>(`${this.basePath}`, { name });
  }

  update(id: string, name: string): Promise<GroupDto> {
    return this.http.patch<GroupDto>(`${this.basePath}/${id}`, { name });
  }

  delete(id: string): Promise<void> {
    return this.http.delete<void>(`${this.basePath}/${id}`);
  }

  private buildEnrollResponse(result: KonquestGroupEnrollResponseDto): GroupEnrollResponseDto {
    const errors: GroupEnrollErrorDto[] = result.group_user_errors?.map((error) => ({
      error: error.error.detail,
      user_id: error.user.id,
    }));
    return { errors };
  }

  async getMissions(groupId: string, params: GroupMissionsListParamsDto) {
    const queryParams = this.buildKonquestListParams(params);
    const result = await this.http.get<KonquestResponseDto<{ mission: BasicMissionDto }>>(
      `${this.basePath}/${groupId}/missions`,
      queryParams,
    );
    const items = result.results?.map((item) => item.mission) ?? [];
    const count = result.count;
    return this.buildListResult({ results: items, count }, params);
  }

  async addMissions(groupId: string, body: CreateGroupMissionsDto): Promise<CreateGroupMissionsResponseDto> {
    const results = await this.http.post<{
      group_mission_errors: { mission: { id: string }; error: { detail: string } }[];
    }>(`${this.basePath}/${groupId}/missions`, body);
    const errors: GroupMissionErrorDto[] = [];

    if (results.group_mission_errors?.length) {
      results.group_mission_errors.map((error) => {
        errors.push({ mission_id: error.mission?.id, detail: error.error?.detail });
      });
    }

    return { errors };
  }

  removeMission(groupId: string, missionId: string) {
    return this.http.delete<void>(`${this.basePath}/${groupId}/missions/${missionId}`);
  }

  async getUsers(groupId: string, params: GroupUsersListParamsDto) {
    const queryParams = this.buildKonquestListParams(params);
    if (params.deleted !== undefined) {
      queryParams['deleted'] = params.deleted;
    }

    const result = await this.http.get<KonquestResponseDto<{ user: BasicUserDto }>>(
      `${this.basePath}/${groupId}/users`,
      queryParams,
    );
    const items = result.results?.map((item) => item.user) ?? [];
    const count = result.count;
    return this.buildListResult({ results: items, count }, params);
  }

  async addUsers(groupId: string, body: CreateGroupUsersDto): Promise<CreateGroupUsersResponseDto> {
    const results = await this.http.post<{
      group_user_errors: { user: { id: string }; error: { detail: string } }[];
    }>(`${this.basePath}/${groupId}/users`, body);
    const errors: GroupUserErrorDto[] = [];

    if (results.group_user_errors?.length) {
      results.group_user_errors.map((error) => {
        errors.push({ user_id: error.user?.id, detail: error.error?.detail });
      });
    }

    return { errors };
  }

  removeUser(groupId: string, userId: string) {
    return this.http.delete<void>(`${this.basePath}/${groupId}/users/${userId}`);
  }
}
