import { Injectable } from '@nestjs/common';
import { KonquestBaseService } from '../../shared/services/konquest-base-service';
import { TrailEnrollmentDto } from '../dtos/trail-enrollment.dto';
import { EnrollmentsListParamsDto } from '../dtos/enrollments-list-params.dto';
import { KonquestResponseDto } from '../../shared/dtos/konquest-response.dto';
import { TrailBatchEnrollmentCreateDto } from '../dtos/trail-batch-enrollment-create.dto';

@Injectable()
export class TrailEnrollmentsService extends KonquestBaseService {
  private readonly basePath = `${this.KONQUEST_URL}/learning-trail-enrollments`;

  async list(params: EnrollmentsListParamsDto) {
    const queryParams = this.buildKonquestListParams(params);
    const enrollmentsList = await this.http.get<KonquestResponseDto<TrailEnrollmentDto>>(this.basePath, queryParams);
    return this.buildListResult(enrollmentsList, params);
  }

  getById(id: string) {
    return this.http.get<TrailEnrollmentDto>(`${this.basePath}/${id}`);
  }

  batchEnroll(batchEnrollDto: TrailBatchEnrollmentCreateDto) {
    return this.http.post<void>(`${this.basePath}/batch/v2`, batchEnrollDto);
  }

  delete(id: string) {
    return this.http.delete(`${this.basePath}/${id}`);
  }
}
