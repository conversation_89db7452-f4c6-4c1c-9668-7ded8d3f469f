import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, ParseUUIDPipe, Post, Query } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { MissionEnrollmentsService } from '../services/mission-enrollments.service';
import { MissionEnrollmentListResponseDto } from '../dtos/mission-enrollment-list-response.dto';
import { MissionEnrollmentDto } from '../dtos/mission-enrollment.dto';
import { EnrollmentsListParamsDto } from '../dtos/enrollments-list-params.dto';
import { Serialize } from '@keeps-node-apis/@core';
import { MissionBatchEnrollmentCreateDto } from '../dtos/mission-batch-enrollment-create.dto';

@ApiTags('Mission Enrollments')
@Controller({ path: 'mission-enrollments', version: '2' })
export class MissionEnrollmentsController {
  constructor(private readonly missionEnrollmentsService: MissionEnrollmentsService) {}

  @Get()
  @ApiOperation({ summary: 'List mission enrollments' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of mission enrollments',
    type: MissionEnrollmentListResponseDto,
  })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid query parameters or missing fields' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  @Serialize(MissionEnrollmentListResponseDto)
  list(@Query() params: EnrollmentsListParamsDto) {
    return this.missionEnrollmentsService.list(params);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get mission enrollment by id' })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'The Mission Enrollment UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The selected mission enrollment',
    type: MissionEnrollmentDto,
  })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid query parameters or missing fields' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Mission enrollment not found' })
  @Serialize(MissionEnrollmentDto)
  getById(@Param('id', ParseUUIDPipe) id: string) {
    return this.missionEnrollmentsService.getById(id);
  }

  @Post()
  @HttpCode(HttpStatus.ACCEPTED)
  @ApiOperation({
    summary: 'Enroll users in batch',
    description: 'Enroll users in batch in the provided missions',
  })
  @ApiResponse({ status: HttpStatus.ACCEPTED, description: 'The request will be processed' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid credentials or missing fields' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  @ApiBody({ type: MissionBatchEnrollmentCreateDto })
  async batchEnroll(@Body() missionEnrollDto: MissionBatchEnrollmentCreateDto) {
    await this.missionEnrollmentsService.batchEnroll(missionEnrollDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete course enrollment by id' })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'The Course Enrollment UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({ status: HttpStatus.NO_CONTENT, description: 'The course enrollment was deleted' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid credentials or missing fields' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  deleteEnrollment(@Param('id', ParseUUIDPipe) id: string) {
    return this.missionEnrollmentsService.delete(id);
  }
}
