import Joi from 'joi';

export default Joi.object({
  NODE_ENV: Joi.string().valid('development', 'stage', 'production').required(),
  PORT: Joi.number().default(3000),

  // elastic
  ELASTICSEARCH_SERVER: Joi.string().uri().required(),
  ELASTICSEARCH_USER: Joi.string().required(),
  ELASTICSEARCH_PASSWORD: Joi.string().required(),

  // database
  DB_USER: Joi.string().default('postgres'),
  DB_PASS: Joi.string().default('postgres'),
  DB_NAME: Joi.string().valid('caixa_partners_db', 'caixa_partners_dev_db').required(),
  DB_HOST: Joi.string().default('db'),
  DB_PORT: Joi.number().default(5432),
  DB_DEBUG: Joi.boolean().default(false),
  DB_MIGRATIONS_RUN: Joi.boolean().default(false),

  // AWS
  AWS_S3_ACCESS_KEY_ID: Joi.string().required(),
  AWS_S3_SECRET_ACCESS_KEY: Joi.string().required(),
  AWS_S3_REGION: Joi.string().required(),
  AWS_S3_BUCKET: Joi.string().required(),
  AWS_S3_BUCKET_PATH: Joi.string().required(),

  // Partners file path
  PARTNERS_FILE_PATH: Joi.string().required(),
});
