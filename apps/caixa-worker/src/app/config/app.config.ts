import { registerAs } from '@nestjs/config';
import * as process from 'node:process';

export interface AppConfigDefinition {
  nodeEnv: string;
  port: number;
  s3: {
    region: string;
    bucket: string;
    basePath: string;
    credentials: {
      accessKeyId: string;
      secretAccessKey: string;
    };
  };
  elastic: {
    node: string;
    auth: {
      username: string;
      password: string;
    };
    maxRetries: number;
    requestTimeout: number;
  };
  partnersFilePath: string;
}

export default registerAs(
  'app',
  (): AppConfigDefinition => ({
    nodeEnv: process.env.NODE_ENV,
    port: parseInt(process.env.PORT),
    s3: {
      region: process.env.AWS_S3_REGION,
      bucket: process.env.AWS_S3_BUCKET,
      basePath: process.env.AWS_S3_BUCKET_PATH,
      credentials: {
        accessKeyId: process.env.AWS_S3_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_S3_SECRET_ACCESS_KEY,
      },
    },
    elastic: {
      node: process.env.ELASTICSEARCH_SERVER,
      auth: {
        username: process.env.ELASTICSEARCH_USER,
        password: process.env.ELASTICSEARCH_PASSWORD,
      },
      maxRetries: 5,
      requestTimeout: 60 * 1000,
    },
    partnersFilePath: process.env.PARTNERS_FILE_PATH,
  }),
);
