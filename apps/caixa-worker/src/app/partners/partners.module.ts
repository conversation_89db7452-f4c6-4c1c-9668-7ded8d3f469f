import { Module } from '@nestjs/common';
import { PartnersSyncService } from './services';
import { PartnersFileParser, PartnersFileReader } from './parsers';
import { PartnersRepository, PartnersTypeOrmRepository } from './repositories';
import { ElasticModule } from '../elastic';
import { AwsModule } from '@keeps-node-apis/@core';

@Module({
  providers: [
    PartnersSyncService,
    PartnersFileParser,
    PartnersFileReader,
    { provide: PartnersRepository, useClass: PartnersTypeOrmRepository },
  ],
  imports: [ElasticModule, AwsModule],
  exports: [PartnersSyncService],
})
export class PartnersModule {}
