import { PartnerSyncDto } from '../../common/dtos';

const MOBILE_WITH_DDD_REGEX = /(\d{2})(\d{5})(\d{4})/;
const FIXED_WITH_DDD_REGEX = /(\d{2})(\d{4})(\d{4})/;
const MOBILE_NO_DDD_REGEX = /(\d{5})(\d{4})/;
const FIXED_NO_DDD_REGEX = /(\d{4})(\d{4})/;

export type PartnerFormatter = (row: PartnerSyncDto) => PartnerSyncDto;

export function formatPhoneNumber(phoneNumber: string): string {
  if (!phoneNumber) {
    return phoneNumber;
  }

  const digits = phoneNumber.replace(/\D/g, '');

  const formatters: Record<number, (digits: string) => string> = {
    11: (d) => d.replace(MOBILE_WITH_DDD_REGEX, '($1) $2-$3'),
    10: (d) => d.replace(FIXED_WITH_DDD_REGEX, '($1) $2-$3'),
    9: (d) => d.replace(MOBILE_NO_DDD_REGEX, '$1-$2'),
    8: (d) => d.replace(FIXED_NO_DDD_REGEX, '$1-$2'),
  };

  return formatters[digits.length] ? formatters[digits.length](digits) : phoneNumber;
}

export const partnerPhoneFormatter: PartnerFormatter = (row) => ({ ...row, phone: formatPhoneNumber(row.phone) });
