import { PartnersFileReader } from './partners-file-reader';
import { S3Downloader } from '@keeps-node-apis/@core';
import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';

describe('PartnersFileReader', () => {
  let reader: PartnersFileReader;
  let downloaderMock: jest.Mocked<S3Downloader>;
  let configServiceMock: jest.Mocked<ConfigService>;
  const filePath = 'path/to/file';

  beforeEach(() => {
    downloaderMock = { downloadFile: jest.fn() } as unknown as jest.Mocked<S3Downloader>;
    configServiceMock = { get: jest.fn().mockReturnValue(filePath) } as unknown as jest.Mocked<ConfigService>;

    reader = new PartnersFileReader(configServiceMock, downloaderMock);
  });

  it('should call downloadFile with the filePath', async () => {
    downloaderMock.downloadFile.mockResolvedValueOnce(Buffer.from(filePath));

    const result = await reader.readFile();
    expect(result.toString()).toEqual(filePath);

    expect(configServiceMock.get).toHaveBeenCalledWith('PARTNERS_FILE_PATH');
    expect(downloaderMock.downloadFile).toHaveBeenCalledWith(filePath);
  });

  it('should log the error when one occurs', async () => {
    const warnSpy = jest.spyOn(Logger.prototype, 'warn').mockImplementation(() => undefined);
    downloaderMock.downloadFile.mockRejectedValueOnce('Error while downloading file');

    await reader.readFile();

    expect(warnSpy).toHaveBeenCalledWith('Error while downloading file');
  });
});
