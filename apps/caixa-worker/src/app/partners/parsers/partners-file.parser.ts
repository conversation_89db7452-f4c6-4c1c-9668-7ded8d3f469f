import { Injectable } from '@nestjs/common';
import { parseString } from '@fast-csv/parse';
import { PartnerSyncDto } from '../../common/dtos';
import { PartnerFormatter } from './partner.formatters';

const defaultFormatter: PartnerFormatter = (row: PartnerSyncDto): PartnerSyncDto => row;

@Injectable()
export class PartnersFileParser {
  parseFromBuffer(buffer: Buffer, formatter: PartnerFormatter = defaultFormatter): Promise<PartnerSyncDto[]> {
    return new Promise((resolve, reject) => {
      const results: PartnerSyncDto[] = [];
      parseString(buffer.toString('utf-8'), {
        headers: true,
      })
        .transform(formatter)
        .on('error', (error) => reject(error))
        .on('data', (row) => results.push(row))
        .on('end', () => resolve(results));
    });
  }
}
