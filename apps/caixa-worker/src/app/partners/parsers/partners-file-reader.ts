import { ConfigService } from '@nestjs/config';
import { Injectable, Logger } from '@nestjs/common';
import { S3Downloader } from '@keeps-node-apis/@core';

@Injectable()
export class PartnersFileReader {
  private logger = new Logger(PartnersFileReader.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly downloader: S3Downloader,
  ) {}

  async readFile(): Promise<Buffer> {
    const path = this.configService.get('PARTNERS_FILE_PATH') ?? '';
    try {
      return await this.downloader.downloadFile(path);
    } catch (e) {
      this.logger.warn(e);
    }
  }
}
