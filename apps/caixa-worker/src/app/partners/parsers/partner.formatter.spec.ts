import { formatPhoneNumber, partnerPhoneFormatter } from './partner.formatters';
import { PartnerSyncDto } from '../../common/dtos';

describe('formatPhoneNumber', () => {
  it('should handle falsy phone values', () => {
    expect(formatPhoneNumber(null)).toBe(null);
    expect(formatPhoneNumber(undefined)).toBe(undefined);
    expect(formatPhoneNumber('')).toBe('');
  });

  it('should format valid phone numbers with 8 to 10 characters', () => {
    expect(formatPhoneNumber('99991111')).toBe('9999-1111');
    expect(formatPhoneNumber('0088881111')).toBe('(00) 8888-1111');
    expect(formatPhoneNumber('999888111')).toBe('99988-8111');
    expect(formatPhoneNumber('00999888111')).toBe('(00) 99988-8111');
  });

  it('should return unmatched values as they are', () => {
    expect(formatPhoneNumber('9999')).toBe('9999');
  });
});

describe('partnerPhoneFormatter', () => {
  it('should format the phone number of a partner', () => {
    const partner = { phone: '00999888111', name: 'Mock Partner' } as PartnerSyncDto;
    expect(partnerPhoneFormatter(partner)).toEqual({ phone: '(00) 99988-8111', name: 'Mock Partner' });
  });
});
