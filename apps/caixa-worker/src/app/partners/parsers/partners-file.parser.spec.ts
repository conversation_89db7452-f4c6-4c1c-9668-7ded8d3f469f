import { PartnersFileParser } from './partners-file.parser';
import { PartnerSyncDto } from '../../common/dtos';

const MOCK_DATA = `convention_number,name,cnpj,agency_name,sev_code,sev_name,sr_code,sr_name,status,phone,email,sn_name,type,network_type,unit_size,sr_size,address,district,zip_code,city,uf,region,consultant_enrollment,consultant_cvp,consultant_email,manager,cvp_manager,manager_email,gcn,strategic_wallet
178772,PARTNER INFORMACOES LTDA,219328000101,"PRACA DA LIBERDADE, SP",6086,SEV BELA VISTA,2574,"SAO PAULO CENTRO, SP",Parceiro Ativo,01132774177,<EMAIL>,REDE PARCEIRO,Correspondente,REDE FISICA,4,1,AVENIDA LIBERDADE N 21 CJ 812,LIBERDADE,1503000,SAO PAULO,SP        ,<PERSON>O PAULO,CVP14272,MILENA MENDES GANDINI RODRIGUES,<EMAIL>,GECAN,GABRIELA MACEIO,<EMAIL>,Felipe Mamede,CARTEIRA SÃO PAULO 2`;

describe('PartnersFileParser', () => {
  let parser: PartnersFileParser;

  beforeEach(() => {
    parser = new PartnersFileParser();
  });

  it('should parse a Buffer from a partners CSV file', async () => {
    const expectedResult = {
      address: 'AVENIDA LIBERDADE N 21 CJ 812',
      agency_name: 'PRACA DA LIBERDADE, SP',
      city: 'SAO PAULO',
      cnpj: '219328000101',
      consultant_cvp: 'MILENA MENDES GANDINI RODRIGUES',
      consultant_email: '<EMAIL>',
      consultant_enrollment: 'CVP14272',
      convention_number: '178772',
      cvp_manager: 'GABRIELA MACEIO',
      district: 'LIBERDADE',
      email: '<EMAIL>',
      gcn: 'Felipe Mamede',
      manager: 'GECAN',
      manager_email: '<EMAIL>',
      name: 'PARTNER INFORMACOES LTDA',
      network_type: 'REDE FISICA',
      phone: '01132774177',
      region: 'SAO PAULO',
      sev_code: '6086',
      sev_name: 'SEV BELA VISTA',
      sn_name: 'REDE PARCEIRO',
      sr_code: '2574',
      sr_name: 'SAO PAULO CENTRO, SP',
      sr_size: '1',
      status: 'Parceiro Ativo',
      strategic_wallet: 'CARTEIRA SÃO PAULO 2',
      type: 'Correspondente',
      uf: 'SP        ',
      unit_size: '4',
      zip_code: '1503000',
    } as PartnerSyncDto;

    const result = await parser.parseFromBuffer(Buffer.from(MOCK_DATA));

    expect(result).toEqual([expectedResult]);
  });
});
