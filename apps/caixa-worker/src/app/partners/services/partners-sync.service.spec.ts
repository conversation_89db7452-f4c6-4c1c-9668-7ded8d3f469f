import { PartnersSyncService } from './partners-sync.service';
import { PartnersRepository } from '../repositories';
import { ElasticSearchClient } from '../../elastic';
import { Chance } from 'chance';
import { PageDto } from '@keeps-node-apis/@core';
import { Partner } from '../../entities/partner.entity';
import { PartnerSyncDto } from '../../common/dtos';
import { PartnersFileParser, PartnersFileReader } from '../parsers';
import { Logger } from '@nestjs/common';

function buildMockItems(): Partner[] {
  return Array.from(
    { length: 100 },
    (_, index) =>
      ({
        data: { name: `item-${index}` },
      }) as Partner,
  );
}

describe('PartnersSyncService', () => {
  const chance = new Chance();
  const mockItems = buildMockItems();
  let service: PartnersSyncService;
  let fileParser: jest.Mocked<PartnersFileParser>;
  let fileReader: jest.Mocked<PartnersFileReader>;
  let partnersRepository: jest.Mocked<PartnersRepository>;
  let elasticClient: jest.Mocked<ElasticSearchClient>;
  jest.spyOn(Logger.prototype, 'debug').mockImplementation(() => undefined);

  beforeEach(async () => {
    fileReader = {
      readFile: jest.fn().mockResolvedValue(Buffer.from('')),
    } as unknown as jest.Mocked<PartnersFileReader>;
    fileParser = { parseFromBuffer: jest.fn() } as unknown as jest.Mocked<PartnersFileParser>;
    partnersRepository = { batchAdd: jest.fn(), clear: jest.fn(), list: jest.fn() };
    elasticClient = {
      createBatchPartners: jest.fn().mockResolvedValue({}),
    } as unknown as jest.Mocked<ElasticSearchClient>;
    service = new PartnersSyncService(fileParser, fileReader, partnersRepository, elasticClient);
  });

  describe('storePartners', () => {
    it('should save the parsed partners in the database', async () => {
      const mockPartners = [{ name: chance.name() }] as PartnerSyncDto[];
      fileParser.parseFromBuffer.mockResolvedValueOnce(mockPartners);

      await service.storePartners();

      expect(partnersRepository.batchAdd).toHaveBeenCalledWith(mockPartners);
    });
  });

  describe('syncWithElastic', () => {
    it('should not synchronize if there are no items in the database', async () => {
      partnersRepository.list.mockResolvedValueOnce({
        items: [],
        hasNextPage: false,
        total: 0,
      } as unknown as PageDto<Partner>);

      await service.syncWithElastic();

      expect(elasticClient.createBatchPartners).not.toHaveBeenCalled();
    });

    it('should synchronize the current partners with elastic in batches', async () => {
      partnersRepository.list.mockResolvedValueOnce({
        items: mockItems,
        hasNextPage: true,
        total: 230,
      } as unknown as PageDto<Partner>);
      partnersRepository.list.mockResolvedValueOnce({
        items: mockItems,
        hasNextPage: true,
        total: 230,
      } as unknown as PageDto<Partner>);
      partnersRepository.list.mockResolvedValue({
        items: mockItems,
        hasNextPage: false,
        total: 230,
      } as unknown as PageDto<Partner>);
      const expectedSyncItems = mockItems.map((item) => item.data);

      await service.syncWithElastic();

      expect(partnersRepository.list).toHaveBeenCalledTimes(3);
      expect(partnersRepository.list).toHaveBeenCalledWith(expect.objectContaining({ page: 1 }));
      expect(partnersRepository.list).toHaveBeenCalledWith(expect.objectContaining({ page: 2 }));
      expect(partnersRepository.list).toHaveBeenCalledWith(expect.objectContaining({ page: 3 }));
      expect(elasticClient.createBatchPartners).toHaveBeenCalledTimes(3);
      expect(elasticClient.createBatchPartners).toHaveBeenCalledWith(expectedSyncItems);
    });
  });
});
