import { Modu<PERSON> } from '@nestjs/common';
import { ElasticsearchModule } from '@nestjs/elasticsearch';
import { ConfigService } from '@nestjs/config';
import { ElasticSearchClient } from './elastic-search.client';
import { AppConfigDefinition } from '../config/app.config';

@Module({
  imports: [
    ElasticsearchModule.registerAsync({
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        const appConfig = configService.get<AppConfigDefinition>('app');
        return appConfig.elastic;
      },
    }),
  ],
  providers: [ElasticSearchClient],
  exports: [ElasticsearchModule, ElasticSearchClient],
})
export class ElasticModule {}
