NODE_ENV=development

# Elastic
ELASTICSEARCH_SERVER=
<PERSON>LASTICSEARCH_USER=
ELASTICSEARCH_PASSWORD=

# DB
DB_HOST=db
DB_PORT=5432
DB_NAME=caixa_partners_db
DB_USER=postgres
DB_PASS=postgres
DB_DIALECT=postgres
DB_DEBUG=true
# SET TRUE ONLY TO LOCAL DATABASE
MIGRATIONS_RUN=false

#AWS S3
AWS_S3_ACCESS_KEY_ID=
AWS_S3_SECRET_ACCESS_KEY=
AWS_S3_REGION=
AWS_S3_BUCKET=
AWS_S3_BUCKET_PATH=

# PARTNERS FILE PATH
PARTNERS_FILE_PATH=partners/utf8/processed_partners.csv
