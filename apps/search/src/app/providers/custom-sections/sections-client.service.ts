import { GrpcResilientClientService } from '@keeps-node-apis/@core';
import { Inject, Injectable, OnModuleInit } from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';
import { Observable } from 'rxjs';

export interface Section {
  id: string;
  title: string;
  description: string;
  learningObjectType: string;
  startDate: string;
  endDate: string;
  order: number;
  filters: Record<string, { values: string[] }>;
}

interface SectionService {
  Get(data: { filters: Record<string, { values: string[] }> }): Observable<{
    items: Section[];
  }>;
  GetById(data: { id: string }): Observable<{ section: Section }>;
}

@Injectable()
export class SectionClientService implements OnModuleInit {
  private sectionService: SectionService;

  constructor(
    @Inject('CUSTOM-SECTIONS') private readonly client: ClientGrpc,
    private readonly grpcResilientClientService: GrpcResilientClientService,
  ) {}

  onModuleInit() {
    this.sectionService = this.client.getService<SectionService>('SectionService');
  }

  async getSections(filters: Record<string, { values: string[] }>) {
    const grpcCall$ = this.sectionService.Get({ filters });
    return this.grpcResilientClientService.execute(grpcCall$);
  }

  async getSectionById(id: string): Promise<Section> {
    const grpcCall$ = this.sectionService.GetById({ id });
    const response = await this.grpcResilientClientService.execute(grpcCall$);
    return response.section;
  }
}
