import { GrpcResilientClientService } from '@keeps-node-apis/@core';
import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { join } from 'path';
import { SectionClientService } from './sections-client.service';
import { ConfigModule, ConfigService } from '@nestjs/config';

@Module({
  imports: [
    ClientsModule.registerAsync([
      {
        name: 'CUSTOM-SECTIONS',
        imports: [ConfigModule],
        inject: [ConfigService],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.GRPC,
          options: {
            url: configService.get<string>('CUSTOM_SECTIONS_GRPC_SERVER_URL'),
            package: 'sections',
            protoPath: join(__dirname, './assets/protos/sections.proto'),
          },
        }),
      },
    ]),
  ],
  providers: [GrpcResilientClientService, SectionClientService],
  exports: [SectionClientService],
})
export class CustomSectionsModule {}
