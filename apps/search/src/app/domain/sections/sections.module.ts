import { Module } from '@nestjs/common';
import { CoursesModule } from '../courses/courses.module';
import { TrailsModule } from '../trails/trails.module';
import { EnrollmentsModule } from '../enrollments/enrollments.module';
import { CustomSectionsModule } from '../../providers/custom-sections/custom-sections.module';

import { ContentsController } from './sections.controller';
import { SectionContentResolver } from './contents/section-content.resolver';
import { CourseHandler } from './contents/handlers/course.handler';
import { TrailHandler } from './contents/handlers/trail.handler';
import { EventHandler } from './contents/handlers/event.handler';
import { SectionHandlerFactory } from './contents/handlers/section-handler.factory';
import { SectionsService } from './sections.service';
import { SectionContentMapper } from './contents/section-content.mapper';

@Module({
  imports: [CoursesModule, TrailsModule, EnrollmentsModule, CustomSectionsModule],
  providers: [
    SectionsService,
    SectionContentResolver,
    CourseHandler,
    TrailHandler,
    EventHandler,
    SectionHandlerFactory,
    SectionContentMapper,
  ],
  exports: [SectionsService],
  controllers: [ContentsController],
})
export class SectionContentModule {}
