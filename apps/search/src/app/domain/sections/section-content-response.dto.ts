import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class SectionContentEnrollmentDto {
  @ApiPropertyOptional()
  enrollment_id: string | null;

  @ApiPropertyOptional()
  status: string | null;

  @ApiPropertyOptional()
  required: boolean | null;

  @ApiPropertyOptional()
  goal_date: string | null;

  @ApiPropertyOptional()
  progress: number | null;
}

export class SectionContentStatsDto {
  @ApiProperty()
  is_owner: boolean;

  @ApiPropertyOptional()
  favorite?: string;

  @ApiPropertyOptional()
  is_integration?: boolean;

  @ApiPropertyOptional()
  is_contributor?: boolean;

  @ApiPropertyOptional()
  is_instructor?: boolean;

  @ApiPropertyOptional()
  missions_count?: number;

  @ApiPropertyOptional()
  pulse_count?: number;

  @ApiPropertyOptional({ type: () => SectionContentEnrollmentDto })
  enrollment: SectionContentEnrollmentDto | null;
}

export class SectionContentResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  title: string;

  @ApiPropertyOptional()
  thumb_image: string | null;

  @ApiProperty()
  duration: number;

  @ApiProperty()
  language: string;

  // Trail only
  @ApiPropertyOptional()
  is_active?: boolean;

  // Course only
  @ApiPropertyOptional()
  course_model?: string;

  @ApiPropertyOptional()
  event_date?: string | null;

  @ApiPropertyOptional()
  development_status?: string;

  @ApiPropertyOptional()
  external_url?: string | null;

  @ApiPropertyOptional()
  vertical_holder_image?: string | null;

  @ApiPropertyOptional()
  external_course?: {
    id: string | null;
    course_url: string | null;
    course_type: string | null;
    provider_id: string | null;
    provider_name: string | null;
    provider_description: string | null;
    provider_icon: string | null;
  } | null;

  @ApiProperty({ type: () => SectionContentStatsDto })
  stats: SectionContentStatsDto;
}

export class PaginatedSectionContentResponseDto {
  @ApiProperty({ type: [SectionContentResponseDto] })
  items: SectionContentResponseDto[];

  @ApiProperty({ example: 69 })
  total: number;

  @ApiProperty({ example: 1 })
  page: number;

  @ApiProperty({ example: 20 })
  per_page: number;

  @ApiProperty({ example: 4 })
  last_page: number;
}
