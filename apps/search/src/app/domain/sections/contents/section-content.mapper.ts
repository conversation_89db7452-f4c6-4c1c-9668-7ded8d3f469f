import { Injectable } from '@nestjs/common';
import { TrailsResponseDto } from '../../trails/trails-response.dto';
import { StatsDto } from '../../../common/models/basic.dto';
import { CoursesResponseDto } from '../../courses/courses-response.dto';
import { SectionContentResponseDto } from './section-contents.interface';

@Injectable()
export class SectionContentMapper {
  toDto(item: (TrailsResponseDto & StatsDto) | (CoursesResponseDto & StatsDto)): SectionContentResponseDto {
    const stats = item.stats ?? {};
    const userEnrollment = (stats as any).user_enrollment ?? null;

    const isCourse = 'course_model' in item;
    const isTrail = 'is_active' in item;

    const common: SectionContentResponseDto = {
      id: item.id,
      title: item.name,
      thumb_image: item.thumb_image ?? null,
      duration: item.duration_time,
      language: item.language,
      stats: {
        is_owner: Boolean((stats as any).user_is_owner),
        enrollment: userEnrollment
          ? {
              enrollment_id: userEnrollment.id ?? null,
              status: userEnrollment.status ?? null,
              required: userEnrollment.required ?? null,
              goal_date: userEnrollment.goal_date ?? null,
              progress: userEnrollment.progress ?? null,
            }
          : null,
      },
    };

    if (isTrail) {
      return {
        ...common,
        is_active: (item as TrailsResponseDto).is_active ?? null,
        stats: {
          ...common.stats,
          missions_count: (stats as any).total_missions ?? 0,
          pulse_count: (stats as any).total_pulses ?? 0,
        },
      };
    }

    if (isCourse) {
      const course = item as CoursesResponseDto;
      return {
        ...common,
        external_course: course.external_course ?? null,
        course_model: course.course_model,
        event_date: course.event_date ?? null,
        external_url: course.external_course?.course_url ?? null,
        development_status: course.development_status ?? undefined,
        vertical_holder_image: course.vertical_holder_image ?? null,
        stats: {
          ...common.stats,
          is_integration: (stats as any).is_integration ?? false,
          is_contributor: (stats as any).user_is_contributor ?? false,
          is_instructor: (stats as any).user_is_instructor ?? false,
          favorite: (stats as any).favorite,
        },
      };
    }

    return common;
  }
}
