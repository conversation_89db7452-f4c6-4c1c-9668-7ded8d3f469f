import { Injectable } from '@nestjs/common';
import { SectionHandlerFactory } from './handlers/section-handler.factory';
import { SectionContentMapper } from './section-content.mapper';
import { SectionLearningObjectType } from '../../../providers/custom-sections/section-learning-object-type';
import { PageRequest } from '../../../common/models/page-request.dto';
import { PageResponse } from '../../../common/models/page-response.dto';

@Injectable()
export class SectionContentResolver {
  constructor(
    private readonly factory: SectionHandlerFactory,
    private readonly mapper: SectionContentMapper,
  ) {}

  async resolve(
    type: SectionLearningObjectType,
    workspaceId: string,
    userId: string,
    filters: Record<string, string[]>,
    page: PageRequest,
  ) {
    const handler = this.factory.getHandler(type);
    const onlyEnrolled = type.toString().includes('ENROLLED');
    const raw = await handler.handle({ workspaceId, userId, filters, page, onlyEnrolled });
    const items = raw.items.map((item) => this.mapper.toDto(item));
    return PageResponse.fromBasicResponse({ items, total: raw.total }, page);
  }
}
