import { Injectable } from '@nestjs/common';
import { ISection<PERSON><PERSON>ler, ISectionHandlerInput } from './base-handler.interface';
import { TrailsService } from '../../../trails/trails.service';
import { TrailsListParamsDto } from '../../../trails/trails-list-params.dto';
import { EnrollmentStatus } from '../../../../common/models/enrollment_status.enum';

@Injectable()
export class TrailHandler implements ISectionHandler {
  constructor(private readonly trailsService: TrailsService) {}

  handle(input: ISectionHandlerInput) {
    const dto = new TrailsListParamsDto();
    dto.id = input.filters.ID || [];
    dto.excludeEnrollmentStatus = [
      EnrollmentStatus.COMPLETED,
      EnrollmentStatus.INACTIVATED,
      EnrollmentStatus.EXPIRED,
      EnrollmentStatus.GIVE_UP,
      EnrollmentStatus.REPROVED,
    ];
    dto.is_active = true;

    if (input.onlyEnrolled) {
      dto.enrolled = true;
    }

    return this.trailsService.getTrails(input.workspaceId, input.userId, dto, input.page);
  }
}
