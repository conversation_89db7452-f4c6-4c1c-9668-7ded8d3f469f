import { Injectable } from '@nestjs/common';
import { <PERSON>ec<PERSON><PERSON><PERSON><PERSON>, ISectionHandlerInput } from './base-handler.interface';
import { CoursesService } from '../../../courses/courses.service';
import { CoursesListParamsDto } from '../../../courses/courses-list-params.dto';
import { MissionModel } from '../../../../common/models/mission_model.enum';

@Injectable()
export class EventHandler implements ISectionHandler {
  constructor(private readonly coursesService: CoursesService) {}

  handle(input: ISectionHandlerInput) {
    const dto = new CoursesListParamsDto();
    dto.id = input.filters.ID || [];
    dto.missionCategory = input.filters.CATEGORY_IDS || [];
    dto.missionModel = [MissionModel.LIVE, MissionModel.PRESENTIAL];
    if (input.onlyEnrolled) {
      dto.enrolled = true;
    }
    return this.coursesService.getCourses(input.workspaceId, input.userId, dto, input.page);
  }
}
