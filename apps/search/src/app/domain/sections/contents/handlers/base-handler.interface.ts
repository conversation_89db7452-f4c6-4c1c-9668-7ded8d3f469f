import { BasicResponse } from '../../../../common/models/basic.dto';
import { PageRequest } from '../../../../common/models/page-request.dto';

export interface ISectionHandlerInput {
  workspaceId: string;
  userId: string;
  filters: Record<string, string[]>;
  page: PageRequest;
  onlyEnrolled: boolean;
}

export interface ISectionHandler {
  handle(input: ISectionHandlerInput): Promise<BasicResponse<any>>;
}
