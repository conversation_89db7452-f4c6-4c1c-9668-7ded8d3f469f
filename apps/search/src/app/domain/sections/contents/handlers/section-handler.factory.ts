import { Injectable } from '@nestjs/common';
import { SectionLearningObjectType } from '../../../../providers/custom-sections/section-learning-object-type';
import { ISectionHandler } from './base-handler.interface';
import { CourseHandler } from './course.handler';
import { TrailHandler } from './trail.handler';
import { EventHandler } from './event.handler';

@Injectable()
export class SectionHandlerFactory {
  private readonly handlers: Map<SectionLearningObjectType, ISectionHandler>;

  constructor(courseHandler: CourseHandler, trailHandler: TrailHandler, eventHandler: EventHandler) {
    this.handlers = new Map<SectionLearningObjectType, ISectionHandler>();
    const courseTypes = [
      SectionLearningObjectType.COURSE,
      SectionLearningObjectType.COURSE_ENROLLED,
      SectionLearningObjectType.COURSE_ALL,
      SectionLearningObjectType.HIGHLIGHT_COURSE,
      SectionLearningObjectType.HIGHLIGHT_COURSE_ENROLLED,
      SectionLearningObjectType.HIGHLIGHT_COURSE_ALL,
    ];

    const eventTypes = [
      SectionLearningObjectType.EVENTS,
      SectionLearningObjectType.EVENTS_ENROLLED,
      SectionLearningObjectType.EVENTS_ALL,
      SectionLearningObjectType.HIGHLIGHT_EVENTS,
      SectionLearningObjectType.HIGHLIGHT_EVENTS_ENROLLED,
      SectionLearningObjectType.HIGHLIGHT_EVENTS_ALL,
    ];

    const trailTypes = [
      SectionLearningObjectType.TRAIL,
      SectionLearningObjectType.TRAIL_ENROLLED,
      SectionLearningObjectType.TRAIL_ALL,
      SectionLearningObjectType.HIGHLIGHT_TRAIL,
      SectionLearningObjectType.HIGHLIGHT_TRAIL_ENROLLED,
      SectionLearningObjectType.HIGHLIGHT_TRAIL_ALL,
    ];

    courseTypes.forEach((type) => this.handlers.set(type, courseHandler));
    trailTypes.forEach((type) => this.handlers.set(type, trailHandler));
    eventTypes.forEach((type) => this.handlers.set(type, eventHandler));
  }

  getHandler(type: SectionLearningObjectType): ISectionHandler {
    const handler = this.handlers.get(type);
    if (!handler) throw new Error(`Unsupported learningObjectType: ${type}`);
    return handler;
  }
}
