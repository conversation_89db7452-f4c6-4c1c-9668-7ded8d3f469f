export interface SectionContentEnrollmentDto {
  enrollment_id: string | null;
  status: string | null;
  required: boolean | null;
  goal_date: string | null;
  progress: number | null;
}

export interface SectionContentStatsDto {
  is_owner: boolean;
  favorite?: string;
  is_integration?: boolean;
  is_contributor?: boolean;
  is_instructor?: boolean;
  missions_count?: number;
  pulse_count?: number;
  enrollment: SectionContentEnrollmentDto | null;
}

export interface SectionContentResponseDto {
  id: string;
  title: string;
  thumb_image: string | null;
  duration: number;
  language: string;

  // Trail only
  is_active?: boolean;

  // Course only
  external_course?: {
    id: string | null;
    course_url: string | null;
    course_type: string | null;
    provider_id: string | null;
    provider_name: string | null;
    provider_description: string | null;
    provider_icon: string | null;
  } | null;
  course_model?: string;
  event_date?: string | null;
  external_url?: string | null;
  development_status?: string;
  vertical_holder_image?: string | null;

  stats: SectionContentStatsDto;
}
