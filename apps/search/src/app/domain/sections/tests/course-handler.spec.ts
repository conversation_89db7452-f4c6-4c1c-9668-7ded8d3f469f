import { MissionModel } from '../../../common/models/mission_model.enum';
import { CoursesService } from '../../courses/courses.service';
import { DevelopmenStatus } from '../../global/global-params.dto';
import { ISectionHandlerInput } from '../contents/handlers/base-handler.interface';
import { CourseHandler } from '../contents/handlers/course.handler';

describe('CourseHandler', () => {
  let courseHandler: CourseHandler;
  let coursesService: jest.Mocked<CoursesService>;

  beforeEach(() => {
    coursesService = {
      getCourses: jest.fn(),
    } as any;

    courseHandler = new CourseHandler(coursesService);
  });

  it('should call coursesService.getCourses with correct DTO (no enrolled)', async () => {
    const input: ISectionHandlerInput = {
      filters: {
        ID: ['1', '2'],
        CATEGORY_IDS: ['cat1'],
      },
      onlyEnrolled: false,
      workspaceId: 'workspace123',
      userId: 'user456',
      page: { perPage: 10, page: 1 } as any,
    };

    await courseHandler.handle(input);

    const expectedDto = {
      id: ['1', '2'],
      missionCategory: ['cat1'],
      developmentStatus: DevelopmenStatus.DONE,
      missionModel: [MissionModel.INTERNAL, MissionModel.EXTERNAL_PROVIDER, MissionModel.SCORM],
    } as any;

    expect(coursesService.getCourses).toHaveBeenCalledWith(
      input.workspaceId,
      input.userId,
      expect.objectContaining(expectedDto),
      input.page,
    );
  });

  it('should set enrolled to true when onlyEnrolled is true', async () => {
    const input: ISectionHandlerInput = {
      filters: {
        ID: [],
        CATEGORY_IDS: [],
      },
      onlyEnrolled: true,
      workspaceId: 'workspace123',
      userId: 'user456',
      page: { perPage: 10, page: 1 } as any,
    };

    await courseHandler.handle(input);

    expect(coursesService.getCourses).toHaveBeenCalledWith(
      input.workspaceId,
      input.userId,
      expect.objectContaining({
        enrolled: true,
        developmentStatus: DevelopmenStatus.DONE,
        missionModel: [MissionModel.INTERNAL, MissionModel.EXTERNAL_PROVIDER, MissionModel.SCORM],
      }),
      input.page,
    );
  });
});
