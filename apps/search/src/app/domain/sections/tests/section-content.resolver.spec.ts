import { SectionLearningObjectType } from '../../../providers/custom-sections/section-learning-object-type';
import { PageRequest } from '../../../common/models/page-request.dto';
import { PageResponse } from '../../../common/models/page-response.dto';
import { SectionContentResolver } from '../contents/section-content.resolver';
import { SectionHandlerFactory } from '../contents/handlers/section-handler.factory';
import { SectionContentMapper } from '../contents/section-content.mapper';

describe('SectionContentResolver', () => {
  let resolver: SectionContentResolver;
  let factoryMock: jest.Mocked<SectionHandlerFactory>;
  let mapperMock: jest.Mocked<SectionContentMapper>;
  let handlerMock: any;

  beforeEach(() => {
    handlerMock = {
      handle: jest.fn(),
    };

    factoryMock = {
      getHandler: jest.fn(),
    } as any;

    mapperMock = {
      toDto: jest.fn(),
    } as any;

    resolver = new SectionContentResolver(factoryMock, mapperMock);
  });

  it('should resolve content using correct handler and map results', async () => {
    const mockType = SectionLearningObjectType.COURSE_ENROLLED;
    const mockPage = new PageRequest();
    mockPage.page = 2;
    mockPage.perPage = 10;

    const mockItems = [{ id: 'item1' }, { id: 'item2' }];
    const mappedItems = [{ id: 'mapped1' }, { id: 'mapped2' }];

    handlerMock.handle.mockResolvedValue({
      items: mockItems,
      total: 50,
    });

    factoryMock.getHandler.mockReturnValue(handlerMock);
    mapperMock.toDto.mockReturnValueOnce(mappedItems[0] as any).mockReturnValueOnce(mappedItems[1] as any);

    const result = await resolver.resolve(mockType, 'workspace-123', 'user-456', { CATEGORY: ['abc'] }, mockPage);

    expect(factoryMock.getHandler).toHaveBeenCalledWith(mockType);
    expect(handlerMock.handle).toHaveBeenCalledWith({
      workspaceId: 'workspace-123',
      userId: 'user-456',
      filters: { CATEGORY: ['abc'] },
      page: mockPage,
      onlyEnrolled: true,
    });

    expect(mapperMock.toDto).toHaveBeenCalledTimes(2);
    expect(result).toBeInstanceOf(PageResponse);
    expect(result.items).toEqual(mappedItems);
    expect(result.total).toEqual(50);
    expect(result.page).toEqual(2);
    expect(result.per_page).toEqual(10);
    expect(result.last_page).toEqual(5); // 50 / 10
  });

  it('should set onlyEnrolled to false when type does not include ENROLLED', async () => {
    const mockType = SectionLearningObjectType.TRAIL;
    const mockPage = new PageRequest();

    handlerMock.handle.mockResolvedValue({
      items: [],
      total: 0,
    });

    factoryMock.getHandler.mockReturnValue(handlerMock);
    mapperMock.toDto.mockReturnValue({} as any);

    await resolver.resolve(mockType, 'ws', 'user', {}, mockPage);

    expect(handlerMock.handle).toHaveBeenCalledWith(expect.objectContaining({ onlyEnrolled: false }));
  });
});
