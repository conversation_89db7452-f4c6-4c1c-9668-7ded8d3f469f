import { NotFoundException } from '@nestjs/common';
import { SectionsService } from '../sections.service';
import { SectionClientService } from '../../../providers/custom-sections/sections-client.service';
import { SectionContentResolver } from '../contents/section-content.resolver';
import { PageRequest } from '../../../common/models/page-request.dto';
import { SectionLearningObjectType } from '../../../providers/custom-sections/section-learning-object-type';

describe('ListContentsFromSection', () => {
  let service: SectionsService;
  let sectionRepository: jest.Mocked<SectionClientService>;
  let resolver: jest.Mocked<SectionContentResolver>;

  beforeEach(() => {
    sectionRepository = {
      getSectionById: jest.fn(),
    } as any;

    resolver = {
      resolve: jest.fn(),
    } as any;

    service = new SectionsService(sectionRepository, resolver);
  });

  it('should throw NotFoundException when section is not found', async () => {
    sectionRepository.getSectionById.mockResolvedValue(null);

    await expect(service.execute('invalid-id', 'workspace-id', 'user-id', new PageRequest())).rejects.toThrow(
      NotFoundException,
    );
  });

  it('should call resolver.resolve with correct params', async () => {
    const mockSection = {
      learningObjectType: SectionLearningObjectType.COURSE,
      filters: {
        ID: { values: ['abc'] },
        CATEGORY_IDS: { values: ['123'] },
      },
    };

    const pageRequest = new PageRequest();
    const expectedFilters = {
      ID: ['abc'],
      CATEGORY_IDS: ['123'],
    };

    const expectedResult = { items: [], total: 0 };
    sectionRepository.getSectionById.mockResolvedValue(mockSection as any);
    resolver.resolve.mockResolvedValue(expectedResult as any);

    const result = await service.execute('section-id', 'workspace-id', 'user-id', pageRequest);

    expect(result).toEqual(expectedResult);
    expect(resolver.resolve).toHaveBeenCalledWith(
      SectionLearningObjectType.COURSE,
      'workspace-id',
      'user-id',
      expectedFilters,
      pageRequest,
    );
  });

  it('should map filters correctly when filters are undefined', async () => {
    const mockSection = {
      learningObjectType: SectionLearningObjectType.TRAIL,
      filters: undefined,
    };

    sectionRepository.getSectionById.mockResolvedValue(mockSection as any);
    resolver.resolve.mockResolvedValue({ items: [], total: 0 } as any);

    const result = await service.execute('section-id', 'workspace-id', 'user-id', new PageRequest());

    expect(result).toEqual({ items: [], total: 0 });
    expect(resolver.resolve).toHaveBeenCalledWith(
      SectionLearningObjectType.TRAIL,
      'workspace-id',
      'user-id',
      {},
      expect.any(PageRequest),
    );
  });
});
