import { CoursesResponseDto } from '../../courses/courses-response.dto';
import { TrailsResponseDto } from '../../trails/trails-response.dto';
import { SectionContentMapper } from '../contents/section-content.mapper';

describe('SectionContentMapper', () => {
  let mapper: SectionContentMapper;

  beforeEach(() => {
    mapper = new SectionContentMapper();
  });

  it('should map a course item correctly', () => {
    const course = {
      id: 'course-1',
      name: 'Course Example',
      duration_time: 100,
      language: 'pt-BR',
      thumb_image: null,
      course_model: 'INTERNAL',
      event_date: null,
      development_status: 'DONE',
      vertical_holder_image: null,
      external_course: {
        id: 'ext-1',
        course_url: 'https://external.com',
        course_type: null,
        provider_id: 'provider-id',
        provider_name: 'Provider Name',
        provider_description: null,
        provider_icon: null,
      },
      stats: {
        user_is_owner: true,
        user_enrollment: {
          id: 'enroll-1',
          status: 'COMPLETED',
          required: true,
          goal_date: '2025-01-01',
          progress: 0.95,
        },
        is_integration: false,
        user_is_contributor: true,
        user_is_instructor: false,
        favorite: 'fav-1',
      },
    } as CoursesResponseDto & { stats: any };

    const result = mapper.toDto(course);

    expect(result).toEqual({
      id: 'course-1',
      title: 'Course Example',
      thumb_image: null,
      duration: 100,
      language: 'pt-BR',
      course_model: 'INTERNAL',
      event_date: null,
      external_course: course.external_course,
      external_url: 'https://external.com',
      development_status: 'DONE',
      vertical_holder_image: null,
      stats: {
        is_owner: true,
        enrollment: {
          enrollment_id: 'enroll-1',
          status: 'COMPLETED',
          required: true,
          goal_date: '2025-01-01',
          progress: 0.95,
        },
        is_integration: false,
        is_contributor: true,
        is_instructor: false,
        favorite: 'fav-1',
      },
    });
  });

  it('should map a trail item correctly', () => {
    const trail = {
      id: 'trail-1',
      name: 'Trail Example',
      duration_time: 200,
      language: 'pt-BR',
      thumb_image: null,
      is_active: true,
      stats: {
        user_is_owner: false,
        user_enrollment: null,
        total_missions: 5,
        total_pulses: 10,
      },
    } as TrailsResponseDto & { stats: any };

    const result = mapper.toDto(trail);

    expect(result).toEqual({
      id: 'trail-1',
      title: 'Trail Example',
      thumb_image: null,
      duration: 200,
      language: 'pt-BR',
      is_active: true,
      stats: {
        is_owner: false,
        enrollment: null,
        missions_count: 5,
        pulse_count: 10,
      },
    });
  });

  it('should handle missing stats and enrollment', () => {
    const trail = {
      id: 'trail-2',
      name: 'Trail No Stats',
      duration_time: 150,
      language: 'pt-BR',
      thumb_image: null,
      is_active: false,
      stats: {},
    } as TrailsResponseDto & { stats: any };

    const result = mapper.toDto(trail);

    expect(result.stats.enrollment).toBeNull();
    expect(result.stats.is_owner).toBe(false);
  });

  it('should return only common fields if item is neither trail nor course', () => {
    const unknown: any = {
      id: 'generic-1',
      name: 'Generic Content',
      duration_time: 50,
      language: 'pt-BR',
      thumb_image: null,
      stats: {
        user_is_owner: true,
      },
    };

    const result = mapper.toDto(unknown);

    expect(result).toEqual({
      id: 'generic-1',
      title: 'Generic Content',
      thumb_image: null,
      duration: 50,
      language: 'pt-BR',
      stats: {
        is_owner: true,
        enrollment: null,
      },
    });
  });
});
