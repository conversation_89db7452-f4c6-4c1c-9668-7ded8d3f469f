import { Injectable, NotFoundException } from '@nestjs/common';
import { SectionClientService } from '../../providers/custom-sections/sections-client.service';
import { PageRequest } from '../../common/models/page-request.dto';
import { SectionLearningObjectType } from '../../providers/custom-sections/section-learning-object-type';
import { SectionContentResolver } from './contents/section-content.resolver';

@Injectable()
export class SectionsService {
  constructor(
    private readonly sectionRepository: SectionClientService,
    private readonly resolver: SectionContentResolver,
  ) {}

  async execute(sectionId: string, workspaceId: string, userId: string, pageRequest: PageRequest) {
    const section = await this.sectionRepository.getSectionById(sectionId);

    if (!section) {
      throw new NotFoundException(`Section "${sectionId}" not found`);
    }

    const filters = this.mapFilters(section.filters);

    return this.resolver.resolve(
      section.learningObjectType as SectionLearningObjectType,
      workspaceId,
      userId,
      filters,
      pageRequest,
    );
  }

  private mapFilters(rawFilters: Record<string, { values: string[] }> = {}): Record<string, string[]> {
    return Object.entries(rawFilters).reduce(
      (acc, [key, value]) => {
        acc[key] = value.values ?? [];
        return acc;
      },
      {} as Record<string, string[]>,
    );
  }
}
