import { <PERSON>, Get, Headers, Param, Query } from '@nestjs/common';
import { AuthenticatedUser } from 'nest-keycloak-connect';
import { PageRequest } from '../../common/models/page-request.dto';
import { APP_KONQUEST_ALL_ROLES, AppRoles, AuthUser } from '@keeps-node-apis/@core';
import { SectionsService } from './sections.service';
import { ApiOkResponse } from '@nestjs/swagger';
import { PaginatedSectionContentResponseDto } from './section-content-response.dto';

@Controller('sections')
export class ContentsController {
  constructor(private readonly service: SectionsService) {}

  @Get(':sectionId/contents')
  @ApiOkResponse({
    description: 'Lista paginada de conteúdos (cursos e trilhas)',
    type: PaginatedSectionContentResponseDto,
  })
  @AppRoles(APP_KONQUEST_ALL_ROLES)
  async listContents(
    @Param('sectionId') sectionId: string,
    @Headers('x-client') workspaceId: string,
    @AuthenticatedUser() user: AuthUser,
    @Query() page: PageRequest,
  ) {
    return this.service.execute(sectionId, workspaceId, user.sub, page);
  }
}
