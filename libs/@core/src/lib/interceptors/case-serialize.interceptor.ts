import { type ExecutionContext, Injectable, type NestInterceptor, type CallHandler, Optional } from '@nestjs/common';
import { type Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { snakeToCamelCase } from '../common';

/** because the regenerated value's field is differ from original,
 * it is hard to declare return type.
 * the input type is also not meaningful.
 *
 * in: request layer (default: snakeToCamel),
 * out: response layer (default: camelToSnake).
 *
 * i.e. const DEFAULT_STRATEGY: Strategy = { in: snakeToCamel, out: camelToSnake };
 */
export class Strategy {
  in: (value: unknown, whitelist: string[]) => unknown;
  out: (value: unknown, whitelist: string[]) => unknown;
}
export const DEFAULT_STRATEGY: Strategy = {
  in: snakeToCamel,
  out: camelToSnake,
};

// where NestInterceptor<T, R>, T is stream of response, R is stream of value
@Injectable()
export class CaseSerializeInterceptor implements NestInterceptor<unknown, unknown> {
  constructor(
    @Optional() readonly strategy: Strategy = DEFAULT_STRATEGY,
    private readonly whitelist: string[] = [],
  ) {}

  intercept(context: ExecutionContext, next: CallHandler<unknown>): Observable<unknown> {
    const request = context.switchToHttp().getRequest();
    request.body = this.strategy.in(request.body, this.whitelist);
    request.query = this.strategy.in(request.query, this.whitelist);

    return next.handle().pipe(map((data) => camelToSnake(data, this.whitelist)));
  }
}

export function camelToSnake<T>(value: T, whitelist: string[] = []): unknown {
  if (value === null || value === undefined) return value;

  if (Array.isArray(value)) return value.map((item) => camelToSnake(item, whitelist));

  if (typeof value === 'object' && !(value instanceof Date)) {
    return Object.fromEntries(
      Object.entries(value).map(([key, val]) => {
        if (whitelist.includes(key)) {
          return [key, val];
        }

        const snakeKey = key.replace(/([A-Z])/g, '_$1').toLowerCase();
        return [snakeKey, camelToSnake(val, whitelist)];
      }),
    );
  }

  return value;
}

export function snakeToCamel<T>(value: T, whitelist: string[] = []): unknown {
  if (value === null || value === undefined) return value;

  if (Array.isArray(value)) return value.map((item) => snakeToCamel(item, whitelist));

  if (typeof value === 'object' && !(value instanceof Date)) {
    return Object.fromEntries(
      Object.entries(value).map(([key, val]) => {
        if (whitelist.includes(key)) {
          return [key, val];
        }

        return [snakeToCamelCase(key), snakeToCamel(val, whitelist)];
      }),
    );
  }

  return value;
}
