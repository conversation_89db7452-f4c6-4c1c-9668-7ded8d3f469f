import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ConnectionService } from '../../messages';
import { MobileNotificationMessageDto } from '../dtos/mobile-notification-message.dto';
import { MobileNotificationMessageSnakeDto } from '../dtos/mobile-notification-message-snake.dto';

@Injectable()
export class MobileNotificationsService implements OnModuleInit {
  private readonly logger = new Logger(MobileNotificationsService.name);
  private readonly queue: string;
  private readonly deadLetterQueue: string;
  private readonly queueIsDurable: boolean;

  constructor(
    private configService: ConfigService,
    private messageConnectionService: ConnectionService,
  ) {
    this.queue = this.configService.get('RABBITMQ_MOBILE_NOTIFICATIONS_QUEUE') || 'mobile';
    this.deadLetterQueue = this.queue + '-dead-letter';
    const queueIsDurableConfig = this.configService.get('MOBILE_NOTIFICATIONS_QUEUE_IS_DURABLE');
    this.queueIsDurable = queueIsDurableConfig ? JSON.parse(queueIsDurableConfig) : true;
  }

  async onModuleInit(): Promise<void> {
    await this.messageConnectionService.connectChannel(
      this.queue,
      this.deadLetterQueue,
      async () => Promise.resolve(),
      { queueIsDurable: this.queueIsDurable },
    );
    this.logger.log(`Mobile notifications service initialized with queue: ${this.queue}`);
  }

  async sendMobileNotification(notification: MobileNotificationMessageDto): Promise<void> {
    try {
      const snakeNotification: MobileNotificationMessageSnakeDto = {
        recipient_phone: notification.recipientPhone,
        type_key: notification.typeKey,
        content_data: notification.contentData,
        language: notification.language,
        workspace_id: notification.workspaceId,
      };

      await this.messageConnectionService.publishToQueue(this.queue, snakeNotification);
      this.logger.log(`Mobile notification sent to queue: ${JSON.stringify(snakeNotification)}`);
    } catch (error) {
      this.logger.error(`Error sending mobile notification: ${error}`);
      throw error;
    }
  }
}
