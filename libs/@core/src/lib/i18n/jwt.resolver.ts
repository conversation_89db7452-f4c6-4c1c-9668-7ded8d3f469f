import { ExecutionContext, Injectable, Logger } from '@nestjs/common';
import { I18nResolver } from 'nestjs-i18n';
import { LANGUAGES } from '../common';
@Injectable()
export class JWTLocaleResolver implements I18nResolver {
  private readonly logger = new Logger('I18nService');

  resolve(context: ExecutionContext) {
    const req = context.switchToHttp().getRequest();

    let lang = LANGUAGES.PT_BR;

    const token = req?.headers?.authorization;

    if (!token) {
      return lang;
    }

    try {
      const tokenBody = token.split('.')[1];
      const tokenBodyDecoded = atob(tokenBody);
      const tokenPayload = JSON.parse(tokenBodyDecoded);
      lang = tokenPayload?.locale ?? lang;
    } catch (error) {
      this.logger.warn(`Error getting language from token, using fallback language ${lang}`, error);
    }

    return lang;
  }
}
