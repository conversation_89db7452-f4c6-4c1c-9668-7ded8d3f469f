import { JWTLocaleResolver } from './jwt.resolver';
import { ExecutionContext, Logger } from '@nestjs/common';
import { HttpArgumentsHost } from '@nestjs/common/interfaces';
import { LANGUAGES } from '../common/constants';

describe('JWTLocaleResolver', () => {
  let resolver: JWTLocaleResolver;
  let httpContext: jest.Mocked<HttpArgumentsHost>;
  let context: jest.Mocked<ExecutionContext>;
  let warnLogSpy: jest.SpyInstance;

  beforeEach(() => {
    // Suppress logger during tests
    warnLogSpy = jest.spyOn(Logger.prototype, 'warn').mockImplementation(() => undefined);

    httpContext = {
      getRequest: jest.fn().mockReturnValue({
        headers: {},
      }),
    } as unknown as jest.Mocked<HttpArgumentsHost>;

    context = {
      switchToHttp: jest.fn().mockReturnValue(httpContext),
    } as unknown as jest.Mocked<ExecutionContext>;

    resolver = new JWTLocaleResolver();
  });

  it('should return default language if no token is present', () => {
    const result = resolver.resolve(context);

    expect(result).toBe(LANGUAGES.PT_BR);
  });

  it('should return default language if token is invalid', () => {
    httpContext.getRequest.mockReturnValueOnce({ headers: { authorization: 'invalid' } });

    const result = resolver.resolve(context);

    expect(result).toBe(LANGUAGES.PT_BR);
    expect(warnLogSpy).toHaveBeenCalledWith(
      expect.stringContaining('Error getting language from token'),
      expect.anything(),
    );
  });

  it('should return locale from valid token', () => {
    const token = `header.${btoa(JSON.stringify({ locale: LANGUAGES.EN }))}.signature`;
    httpContext.getRequest.mockReturnValueOnce({ headers: { authorization: token } });

    const result = resolver.resolve(context);

    expect(result).toBe(LANGUAGES.EN);
  });

  it('should return default language if token does not contain locale', () => {
    const token = `header.${btoa(JSON.stringify({ sub: 'mock_sub' }))}.signature`;
    httpContext.getRequest.mockReturnValueOnce({ headers: { authorization: token } });

    const result = resolver.resolve(context);

    expect(result).toBe(LANGUAGES.PT_BR);
  });
});
