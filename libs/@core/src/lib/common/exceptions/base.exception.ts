/**
 * Base exception class for domain exceptions
 */
export class DomainException extends Error {
  /**
   * Error code for easier client-side handling
   */
  readonly errorCode: string;

  /**
   * Additional error details
   */
  readonly details?: Record<string, any>;

  constructor(message: string, errorCode: string, details?: Record<string, any>) {
    super(message);
    this.name = this.constructor.name;
    this.errorCode = errorCode;
    this.details = details;
  }
}
