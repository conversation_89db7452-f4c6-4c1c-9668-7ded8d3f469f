import { of } from 'rxjs';
import { GrpcResilientClientService } from './grpc-resilient-client.service';
import { Logger } from '@nestjs/common';

describe('GrpcResilientClientService', () => {
  let service: GrpcResilientClientService;
  let warnLogSpy: jest.SpyInstance;
  let errorLogSpy: jest.SpyInstance;

  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  beforeEach(() => {
    // Suppress logger during tests
    warnLogSpy = jest.spyOn(Logger.prototype, 'warn').mockImplementation(() => undefined);
    errorLogSpy = jest.spyOn(Logger.prototype, 'error').mockImplementation(() => undefined);

    service = new GrpcResilientClientService();
  });

  it('should successfully execute and return a value', async () => {
    const mockObservable = of('testValue');
    const result = await service.execute(mockObservable);
    expect(result).toBe('testValue');
    expect(warnLogSpy).not.toHaveBeenCalled();
    expect(errorLogSpy).not.toHaveBeenCalled();
  });
});
