import { Injectable, Logger } from '@nestjs/common';
import { Observable, firstValueFrom, timer } from 'rxjs';
import { retry, catchError } from 'rxjs/operators';

@Injectable()
export class GrpcResilientClientService {
  private readonly logger = new Logger(GrpcResilientClientService.name);

  async execute<T>(grpcCall$: Observable<T>, maxRetries = 5, initialDelayMs = 1000): Promise<T> {
    const resilientCall$ = grpcCall$.pipe(
      retry({
        count: maxRetries,
        delay: (error, retryCount) => {
          this.logger.warn(`Retry ${retryCount}: ${error.message}`);
          return timer(initialDelayMs * Math.pow(2, retryCount));
        },
        resetOnSuccess: true,
      }),
      catchError((err) => {
        this.logger.error(`failed after ${maxRetries} tries`, err.stack);
        throw err;
      }),
    );

    return await firstValueFrom(resilientCall$);
  }
}
