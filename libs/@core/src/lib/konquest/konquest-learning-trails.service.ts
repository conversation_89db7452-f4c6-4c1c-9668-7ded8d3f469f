import { Injectable } from '@nestjs/common';
import { RestClient, RestParams } from '../common';
import { ConfigService } from '@nestjs/config';
import { PaginatedResponse } from './types';
import { Observable } from 'rxjs';
import { LearningTrail } from './types/learning-trail.interface';

@Injectable()
export class KonquestLearningTrailsService {
  readonly baseUrl: string;
  readonly learningTrailUrl: string;
  readonly courseCategoryUrl: string;

  constructor(
    private readonly httpClient: RestClient,
    private readonly configService: ConfigService,
  ) {
    this.baseUrl = this.configService.getOrThrow('KONQUEST_API_URL', '');
    this.learningTrailUrl = this.baseUrl + '/learning-trails';
  }
  findAll(params: RestParams): Observable<PaginatedResponse<LearningTrail>> {
    return this.httpClient.get<PaginatedResponse<LearningTrail>>(this.learningTrailUrl, params);
  }
}
