export interface Course {
  id: string;
  name: string;
  description: string;
  summary: string | null;
  holder_image: string | null;
  vertical_holder_image: string | null;
  thumb_image: string | null;
  created_date: string;
  updated_date: string;
  deleted_date: string | null;
  deleted: boolean;
  external_course_url: string | null;
  mission_type: {
    id: string;
    name: string;
    image: string;
  };
  mission_category: {
    id: string;
    name: string;
  };
  development_status: string;
  mission_model: string;
  duration_time: number;
  points: number | null;
  stages: number;
  rating_avg: number;
  users_enrolled: number;
  is_active: boolean;
  _is_active: boolean;
  enrollment_goal_duration_days: number | null;
  min_time_in_content: number;
  required_evaluation: boolean;
  assessment_type: string;
  allow_self_enrollment_renewal: boolean;
  allow_self_reproved_enrollment_renewal: boolean;
  minimum_performance: number;
  language: string;
  expiration_date: string | null;
  internal_code: string | null;
  learning_trail_linked: boolean;
  is_owner: boolean;
  is_contributor: boolean;
  tags: string[];
  bookmark_id: string | null;
  workspace_min_performance: number;
  workspace_source_id: string;
  provider: any | null;
  external: any | null;
  live: any | null;
  presential: any | null;
  user_creator: {
    id: string;
    name: string;
    avatar: string | null;
  };
}
