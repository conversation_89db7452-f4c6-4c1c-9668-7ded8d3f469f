export interface LearningTrail {
  id: string;
  name: string;
  description: string | null;
  holder_image: string | null;
  thumb_image: string | null;
  duration_time: number;
  points: number;
  is_active: boolean;
  language: string;
  expiration_date: string | null;
  created_date: string;
  updated_date: string;
  deleted_date: string | null;
  deleted: boolean;
  count_missions: number;
  count_pulses: number;
  users_enrolled: number;
  users_finished: number;
  is_owner: boolean;
  enrolled: boolean;
  enrollment: {
    id?: string;
    points: number | null;
    start_date?: string;
    end_date: string | null;
    goal_date: string | null;
    give_up: boolean;
    give_up_comment: string | null;
    status: string | null;
    performance: number | null;
    progress: number | null;
    required: boolean;
    certificate_url: string;
  };
  user_creator: {
    id: string;
    job: string | null;
    name: string;
    email: string;
    email_verified: boolean;
    avatar: string | null;
    status: boolean;
    phone: string;
    last_access_date: string;
    language_id: string;
    country: string | null;
    ein: string | null;
    time_zone: string;
    related_user_leader: string;
  };
  learning_trail_type: {
    id: string;
    name: string;
    description: string;
    image: string;
    created_date: string;
    updated_date: string;
    deleted_date: string | null;
    deleted: boolean;
  };
}
