import { Injectable } from '@nestjs/common';
import { RestClient, RestParams } from '../common';
import { ConfigService } from '@nestjs/config';
import { PaginatedResponse } from './types';
import { Observable } from 'rxjs';
import { Course } from './types/course.interface';
import { CourseCategory } from './types/course-category.interface';

@Injectable()
export class KonquestCoursesService {
  readonly baseUrl: string;
  readonly courseUrl: string;
  readonly courseCategoryUrl: string;

  constructor(
    private readonly httpClient: RestClient,
    private readonly configService: ConfigService,
  ) {
    this.baseUrl = this.configService.getOrThrow('KONQUEST_API_URL', '');
    this.courseUrl = this.baseUrl + '/missions';
    this.courseCategoryUrl = this.baseUrl + '/missions/categories';
  }
  findAll(params: RestParams): Observable<PaginatedResponse<Course>> {
    return this.httpClient.get<PaginatedResponse<Course>>(this.courseUrl, params);
  }

  findAllCoursesCategories(params: RestParams): Observable<PaginatedResponse<CourseCategory>> {
    return this.httpClient.get<PaginatedResponse<CourseCategory>>(this.courseCategoryUrl, params);
  }
}
