import { GetObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { S3Downloader } from './s3-downloader';

jest.mock('@aws-sdk/client-s3');

describe('S3Downloader', () => {
  let downloader: S3Downloader;
  const accessKeyId = 'accessKeyId';
  const secretAccessKey = 'secretAccessKey';
  const region = 'region';
  const bucket = 'bucket';
  const bucketPath = 'bucketPath';

  beforeEach(() => {
    (S3Client as jest.Mock).mockClear();
    (GetObjectCommand as unknown as jest.Mock).mockClear();

    downloader = new S3Downloader(accessKeyId, secretAccessKey, region, bucket, bucketPath);
  });

  it('should create the S3 client with the correct parameters', () => {
    expect(S3Client).toHaveBeenCalledTimes(1);
    expect(S3Client).toHaveBeenCalledWith({ region, credentials: { accessKeyId, secretAccessKey } });
  });

  it('should download a file from S3', async () => {
    const fileKey = 'fileKey';
    const s3MockInstance = (S3Client as jest.Mock).mock.instances[0];
    const send = s3MockInstance.send as jest.Mock;
    const transformToByteArray = jest.fn();
    send.mockResolvedValueOnce({ Body: { transformToByteArray } });
    await downloader.downloadFile(fileKey);

    expect(GetObjectCommand).toHaveBeenCalledTimes(1);
    expect(GetObjectCommand).toHaveBeenCalledWith({
      Bucket: bucket,
      Key: `${bucketPath}/${fileKey}`,
    });
    expect(transformToByteArray).toHaveBeenCalledTimes(1);
  });

  it('should return undefined if the file does not exists', async () => {
    const fileKey = 'fileKey';
    const s3MockInstance = (S3Client as jest.Mock).mock.instances[0];
    const send = s3MockInstance.send as jest.Mock;
    send.mockResolvedValueOnce({ Body: undefined });
    const result = await downloader.downloadFile(fileKey);

    expect(result).toBeUndefined();
  });
});
