import { Injectable } from '@nestjs/common';
import { Downloader } from '../downloader/downloader.interface';
import { GetObjectCommand, S3Client } from '@aws-sdk/client-s3';

@Injectable()
export class S3Downloader implements Downloader {
  private readonly s3Client: S3Client;

  constructor(
    readonly accessKeyId: string,
    readonly secretAccessKey: string,
    readonly region: string,
    private readonly bucket: string,
    private readonly bucketPath: string,
  ) {
    this.s3Client = new S3Client({ region, credentials: { accessKeyId, secretAccessKey } });
  }

  async downloadFile(key: string): Promise<Buffer | undefined> {
    const fileKey = `${this.bucketPath}/${key}`;
    const command = new GetObjectCommand({ Bucket: this.bucket, Key: fileKey });
    const response = await this.s3Client.send(command);
    const responseByteArray = await response.Body?.transformToByteArray();

    if (!responseByteArray) {
      return undefined;
    }

    return Buffer.from(responseByteArray);
  }
}
